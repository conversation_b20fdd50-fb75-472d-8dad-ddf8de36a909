/**
 * 为管理员角色添加所有权限
 * 
 * 该脚本确保管理员角色拥有系统中所有可用的权限
 */

const { Role, Permission, sequelize } = require('./src/models');

async function addAdminPermissions() {
  try {
    console.log('开始为管理员角色添加权限...');

    // 查找管理员角色
    const adminRole = await Role.findOne({
      where: { name: '管理员' }
    });

    if (!adminRole) {
      console.log('未找到管理员角色，无法添加权限');
      return;
    }

    console.log('找到管理员角色，ID:', adminRole.id);

    // 获取所有权限
    const allPermissions = await Permission.findAll();
    
    if (allPermissions.length === 0) {
      console.log('系统中没有定义任何权限，创建基本权限...');
      
      // 创建基本权限
      const basicPermissions = [
        { name: '用户管理', code: 'user:manage', description: '管理用户账号', module: 'user' },
        { name: '角色管理', code: 'role:manage', description: '管理角色和权限', module: 'system' },
        { name: '知识库管理', code: 'knowledge:manage', description: '管理知识库', module: 'knowledge' },
        { name: '文件管理', code: 'file:manage', description: '管理文件', module: 'file' },
        { name: 'AI助手管理', code: 'ai:manage', description: '管理AI助手', module: 'ai' },
        { name: '活动管理', code: 'activity:manage', description: '管理活动', module: 'activity' },
        { name: '系统配置', code: 'system:config', description: '管理系统配置', module: 'system' },
        { name: '知识库访问', code: 'knowledge:access', description: '访问知识库', module: 'knowledge' },
        { name: '文件上传', code: 'file:upload', description: '上传文件', module: 'file' },
        { name: '文件下载', code: 'file:download', description: '下载文件', module: 'file' },
        { name: '活动查看', code: 'activity:view', description: '查看活动', module: 'activity' },
        { name: '评论管理', code: 'comment:manage', description: '管理评论', module: 'comment' },
        { name: '通知管理', code: 'notification:manage', description: '管理通知', module: 'notification' },
        { name: '时间线管理', code: 'timeline:manage', description: '管理时间线', module: 'timeline' },
        { name: '展板管理', code: 'exhibition:manage', description: '管理展板', module: 'exhibition' }
      ];
      
      // 批量创建权限
      const createdPermissions = await Permission.bulkCreate(basicPermissions);
      console.log(`已创建 ${createdPermissions.length} 个基本权限`);
      
      // 为管理员角色添加所有权限
      await adminRole.addPermissions(createdPermissions);
      console.log('已为管理员角色添加所有基本权限');
      
      return createdPermissions;
    } else {
      console.log(`系统中已定义 ${allPermissions.length} 个权限`);
      
      // 为管理员角色添加所有权限
      await adminRole.addPermissions(allPermissions);
      console.log('已为管理员角色添加所有权限');
      
      return allPermissions;
    }
  } catch (error) {
    console.error('添加管理员权限失败:', error);
    throw error;
  }
}

// 执行添加权限
addAdminPermissions()
  .then(permissions => {
    if (permissions) {
      console.log(`成功添加 ${permissions.length} 个权限到管理员角色`);
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('添加权限时发生错误:', error);
    process.exit(1);
  });
