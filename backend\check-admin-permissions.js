/**
 * 检查管理员权限脚本
 * 
 * 该脚本用于检查管理员角色是否具有所有必要的权限
 */

const { User, Role, Permission, sequelize } = require('./src/models');

async function checkAdminPermissions() {
  try {
    console.log('开始检查管理员权限...');

    // 查找管理员角色
    const adminRole = await Role.findOne({
      where: { name: '管理员' },
      include: [
        {
          model: Permission,
          as: 'permissions',
          through: { attributes: [] }
        }
      ]
    });

    if (!adminRole) {
      console.log('未找到管理员角色');
      return;
    }

    console.log('管理员角色信息:');
    console.log('- ID:', adminRole.id);
    console.log('- 名称:', adminRole.name);
    console.log('- 描述:', adminRole.description);
    console.log('- 是否系统角色:', adminRole.is_system);

    // 检查权限
    if (adminRole.permissions && adminRole.permissions.length > 0) {
      console.log('\n管理员角色拥有的权限:');
      adminRole.permissions.forEach((permission, index) => {
        console.log(`${index + 1}. ${permission.name} (${permission.code})`);
      });
    } else {
      console.log('\n管理员角色没有任何权限');
      
      // 获取所有权限
      const allPermissions = await Permission.findAll();
      
      if (allPermissions.length > 0) {
        console.log('\n系统中存在的权限:');
        allPermissions.forEach((permission, index) => {
          console.log(`${index + 1}. ${permission.name} (${permission.code})`);
        });
        
        // 为管理员角色添加所有权限
        console.log('\n为管理员角色添加所有权限...');
        await adminRole.addPermissions(allPermissions);
        console.log('权限添加成功');
      } else {
        console.log('\n系统中没有定义任何权限');
      }
    }

    // 查找管理员用户
    const adminUser = await User.findOne({
      where: { username: 'admin' },
      include: [
        {
          model: Role,
          as: 'userRole',
          include: [
            {
              model: Permission,
              as: 'permissions',
              through: { attributes: [] }
            }
          ]
        }
      ]
    });

    if (adminUser) {
      console.log('\n管理员用户信息:');
      console.log('- ID:', adminUser.id);
      console.log('- 用户名:', adminUser.username);
      console.log('- 邮箱:', adminUser.email);
      console.log('- 角色:', adminUser.role);
      console.log('- 角色ID:', adminUser.role_id);
      
      if (adminUser.userRole) {
        console.log('- 角色名称:', adminUser.userRole.name);
        
        if (adminUser.userRole.permissions && adminUser.userRole.permissions.length > 0) {
          console.log('\n管理员用户通过角色拥有的权限:');
          adminUser.userRole.permissions.forEach((permission, index) => {
            console.log(`${index + 1}. ${permission.name} (${permission.code})`);
          });
        } else {
          console.log('\n管理员用户通过角色没有任何权限');
        }
      } else {
        console.log('- 未关联角色');
      }
    } else {
      console.log('\n未找到管理员用户');
    }
  } catch (error) {
    console.error('检查管理员权限时发生错误:', error);
    throw error;
  }
}

// 执行检查
checkAdminPermissions()
  .then(() => {
    console.log('\n检查完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('检查过程中发生错误:', error);
    process.exit(1);
  });
