/**
 * 创建管理员账号脚本
 * 
 * 该脚本用于创建一个具有管理员权限的用户账号
 */

const { User, Role, sequelize } = require('./src/models');
const bcrypt = require('bcryptjs');

async function createAdminUser() {
  try {
    console.log('开始创建管理员账号...');

    // 检查是否已存在管理员角色
    let adminRole = await Role.findOne({
      where: { name: '管理员' }
    });

    // 如果管理员角色不存在，创建一个
    if (!adminRole) {
      console.log('管理员角色不存在，创建新的管理员角色');
      adminRole = await Role.create({
        name: '管理员',
        description: '系统管理员，拥有所有权限',
        is_system: true
      });
      console.log('管理员角色创建成功，ID:', adminRole.id);
    } else {
      console.log('找到现有管理员角色，ID:', adminRole.id);
    }

    // 检查是否已存在admin用户
    const existingAdmin = await User.findOne({
      where: { username: 'admin' }
    });

    if (existingAdmin) {
      console.log('管理员用户已存在，更新密码');
      
      // 生成密码哈希
      const salt = await bcrypt.genSalt(10);
      const passwordHash = await bcrypt.hash('admin123', salt);
      
      // 更新管理员用户
      await existingAdmin.update({
        password: passwordHash,
        role: 'admin',
        role_id: adminRole.id,
        is_active: true
      });
      
      console.log('管理员用户密码已更新');
      return existingAdmin;
    } else {
      console.log('创建新的管理员用户');
      
      // 生成密码哈希
      const salt = await bcrypt.genSalt(10);
      const passwordHash = await bcrypt.hash('admin123', salt);
      
      // 创建管理员用户
      const adminUser = await User.create({
        username: 'admin',
        password: passwordHash, // 密码会在模型的beforeCreate钩子中自动加密
        email: '<EMAIL>',
        phone: '13800000000',
        role: 'admin',
        role_id: adminRole.id,
        is_active: true
      });
      
      console.log('管理员用户创建成功，ID:', adminUser.id);
      return adminUser;
    }
  } catch (error) {
    console.error('创建管理员账号失败:', error);
    throw error;
  }
}

// 执行创建管理员账号
createAdminUser()
  .then(admin => {
    console.log('管理员账号创建/更新成功:', admin.username);
    process.exit(0);
  })
  .catch(error => {
    console.error('创建管理员账号时发生错误:', error);
    process.exit(1);
  });
