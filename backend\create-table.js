const { sequelize } = require('./src/models');

sequelize.query(`
CREATE TABLE IF NOT EXISTS \`exhibition_boards\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`type\` enum('home','family','personal') NOT NULL COMMENT '展板类型：home-首页, family-家族专题, personal-个人专题',
  \`sub_type\` varchar(50) DEFAULT NULL COMMENT '子类型，如个人专题中的具体人物ID',
  \`image_url\` varchar(255) NOT NULL COMMENT '图片URL',
  \`title\` varchar(100) DEFAULT NULL COMMENT '标题',
  \`description\` text DEFAULT NULL COMMENT '描述',
  \`button_text\` varchar(50) DEFAULT NULL COMMENT '按钮文本',
  \`button_link\` varchar(255) DEFAULT NULL COMMENT '按钮链接',
  \`order\` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  \`status\` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态：active-启用, inactive-禁用',
  \`created_by\` int(11) DEFAULT NULL COMMENT '创建者ID',
  \`updated_by\` int(11) DEFAULT NULL COMMENT '更新者ID',
  \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (\`id\`),
  KEY \`type\` (\`type\`),
  KEY \`sub_type\` (\`sub_type\`),
  KEY \`status\` (\`status\`),
  KEY \`order\` (\`order\`),
  CONSTRAINT \`exhibition_boards_ibfk_1\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\` (\`id\`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT \`exhibition_boards_ibfk_2\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\` (\`id\`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
`).then(() => {
  console.log('展板图片表创建成功');
  process.exit(0);
}).catch(err => {
  console.error('展板图片表创建失败:', err);
  process.exit(1);
});
