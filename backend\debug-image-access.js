/**
 * 调试图片访问问题
 */

const fs = require('fs');
const path = require('path');

function debugImageAccess() {
  console.log('=== 调试图片访问问题 ===\n');
  
  // 1. 检查目录结构
  console.log('1. 检查目录结构:');
  const baseDir = __dirname;
  const publicUploadsDir = path.join(baseDir, 'public/uploads');
  const uploadsDir = path.join(baseDir, '../uploads');
  const imagesDir = path.join(publicUploadsDir, 'images');
  
  console.log('基础目录:', baseDir);
  console.log('public/uploads目录:', publicUploadsDir, fs.existsSync(publicUploadsDir) ? '✅' : '❌');
  console.log('uploads目录:', uploadsDir, fs.existsSync(uploadsDir) ? '✅' : '❌');
  console.log('images目录:', imagesDir, fs.existsSync(imagesDir) ? '✅' : '❌');
  
  // 2. 检查图片文件
  console.log('\n2. 检查图片文件:');
  if (fs.existsSync(imagesDir)) {
    const files = fs.readdirSync(imagesDir);
    const externalFiles = files.filter(f => f.startsWith('external-')).slice(-3);
    
    console.log('最新的3个external文件:');
    externalFiles.forEach(file => {
      const filePath = path.join(imagesDir, file);
      const stats = fs.statSync(filePath);
      console.log(`  - ${file} (${stats.size} bytes)`);
    });
    
    // 3. 测试静态文件路径映射
    console.log('\n3. 静态文件路径映射:');
    if (externalFiles.length > 0) {
      const testFile = externalFiles[0];
      console.log('测试文件:', testFile);
      
      // Express静态文件服务的路径映射
      console.log('Express配置:');
      console.log('  app.use(\'/uploads\', express.static(path.join(__dirname, \'../public/uploads\')))');
      console.log('  实际映射: /uploads/images/' + testFile + ' -> ' + path.join(publicUploadsDir, 'images', testFile));
      
      // 检查文件是否在正确位置
      const expectedPath = path.join(publicUploadsDir, 'images', testFile);
      console.log('  文件存在:', fs.existsSync(expectedPath) ? '✅' : '❌');
      
      // 4. 生成测试URL
      console.log('\n4. 测试URL:');
      const relativeUrl = `/uploads/images/${testFile}`;
      console.log('相对URL:', relativeUrl);
      console.log('完整URL (生产环境):', `http://hefuf.com${relativeUrl}`);
      console.log('完整URL (内网):', `http://10.2.20.14${relativeUrl}`);
      console.log('完整URL (后端直连):', `http://10.2.20.14:5001${relativeUrl}`);
    }
  }
  
  // 5. 检查Nginx配置逻辑
  console.log('\n5. Nginx配置分析:');
  console.log('当前Nginx配置:');
  console.log('  location /uploads {');
  console.log('    proxy_pass http://backend:5001/uploads;');
  console.log('  }');
  console.log('');
  console.log('请求流程:');
  console.log('  1. 用户访问: http://hefuf.com/uploads/images/xxx.jpg');
  console.log('  2. Nginx接收请求，匹配 /uploads 规则');
  console.log('  3. Nginx转发到: http://backend:5001/uploads/images/xxx.jpg');
  console.log('  4. 后端Express处理: app.use(\'/uploads\', express.static(...))');
  console.log('  5. Express查找文件: public/uploads/images/xxx.jpg');
  
  // 6. 可能的问题点
  console.log('\n6. 可能的问题点:');
  console.log('❓ 域名解析: hefuf.com 是否正确解析到 10.2.20.14?');
  console.log('❓ Nginx服务: Nginx是否正在运行并监听80端口?');
  console.log('❓ 后端服务: 后端是否正在运行并监听5001端口?');
  console.log('❓ 容器网络: Docker容器间网络是否正常?');
  console.log('❓ 文件权限: 图片文件是否有正确的读取权限?');
  
  // 7. 建议的调试步骤
  console.log('\n7. 建议的调试步骤:');
  console.log('1. 测试域名解析: nslookup hefuf.com');
  console.log('2. 测试Nginx: curl -I http://hefuf.com');
  console.log('3. 测试后端直连: curl -I http://10.2.20.14:5001/uploads/images/xxx.jpg');
  console.log('4. 检查Docker容器状态: docker-compose ps');
  console.log('5. 查看Nginx日志: docker-compose logs nginx');
  console.log('6. 查看后端日志: docker-compose logs backend');
}

debugImageAccess();
