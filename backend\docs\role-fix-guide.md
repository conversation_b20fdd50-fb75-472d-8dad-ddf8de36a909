# 角色修复指南

本文档提供了关于如何修复系统中角色相关问题的详细说明。

## 问题概述

系统中存在以下角色相关问题：

1. **多余的测试角色**：系统中存在多个测试角色（test_admin_role_*），这些角色无法删除。
2. **多个管理员角色**：系统中存在多个管理员角色，权限不一致。
3. **权限检查机制混乱**：admin账号的权限来源是双重机制（硬编码和数据库角色关联）。

## 解决方案

我们提供了以下脚本来解决这些问题：

1. `fix-admin-roles.js`：修复管理员角色问题，确保admin账号使用正确的管理员角色。
2. `cleanup-test-roles.js`：清理测试角色，并将使用这些角色的用户迁移到访问者角色。
3. `improvedPermissionMiddleware.js`：改进的权限中间件，统一使用基于角色的权限检查。

## 使用方法

### 自动修复（推荐）

1. 确保您已备份数据库。
2. 在命令行中运行 `fix-roles.bat` 批处理文件：
   ```
   cd backend
   fix-roles.bat
   ```
3. 脚本将自动执行修复管理员角色和清理测试角色的操作。

### 手动修复

如果您希望手动控制修复过程，可以按以下步骤操作：

1. 修复管理员角色：
   ```
   node src/scripts/fix-admin-roles.js
   ```

2. 清理测试角色：
   ```
   node src/scripts/cleanup-test-roles.js
   ```

### 集成改进的权限中间件

要完成权限检查机制的修复，您需要手动将 `improvedPermissionMiddleware.js` 集成到您的应用程序中：

1. 打开 `src/app.js` 或您的主应用程序文件。
2. 将原有的权限中间件导入替换为改进的权限中间件：
   ```javascript
   // 替换这一行
   const { checkPermission, isAdmin } = require('./middlewares/authMiddleware');
   
   // 使用这一行
   const { checkPermission, isAdmin, hasPermission } = require('./middlewares/improvedPermissionMiddleware');
   ```

3. 在所有使用硬编码权限检查的地方，替换为基于角色的权限检查。

## 验证修复

修复完成后，您应该验证以下内容：

1. 系统中只有必要的角色（管理员和访问者）。
2. admin账号使用正确的管理员角色，并拥有所有权限。
3. 权限检查机制一致，使用基于角色的权限检查。

## 注意事项

- 在执行修复脚本之前，请务必备份数据库。
- 修复过程可能需要一些时间，取决于数据库中的角色和用户数量。
- 如果您的系统有自定义的权限检查逻辑，可能需要额外的修改。

## 故障排除

如果您在执行修复脚本时遇到问题，请检查以下内容：

1. 确保数据库连接正常。
2. 检查日志输出，了解具体错误信息。
3. 确保您有足够的权限执行数据库操作。

如果问题仍然存在，请联系系统管理员或开发团队获取帮助。
