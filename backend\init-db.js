/**
 * 数据库初始化脚本
 * 
 * 该脚本按照正确的顺序创建所有必要的数据库表
 */

const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// 数据库配置
const config = {
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'hefamily_dev',
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3306,
  dialect: 'mysql'
};

// 创建Sequelize实例
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    logging: console.log
  }
);

// 迁移文件的顺序
const migrationOrder = [
  '20250503000001-create-users.js',
  '20250503000002-create-roles.js',
  '20250503000003-create-permissions.js',
  '20250503000004-create-role-permissions.js',
  '20250503000005-create-knowledge-bases.js',
  '20250503000006-create-knowledge-base-access.js',
  '20250503000007-create-files.js',
  '20250503000008-create-ai-assistants.js',
  '20250503000009-create-notifications.js',
  '20250503000010-create-comments.js',
  '20250503000011-create-activities.js',
  '20250503000012-create-activity-attachments.js',
  '20250503000013-create-system-config.js',
  '20240506000000-create-timeline-events.js',
  '20250510-modify-avatar-field.js',
  '20250520-create-exhibition-boards.js',
  '20240520_add_dify_fields_to_files.js',
  '20240520-update-ai-assistant-fields.js',
  '20240520-update-knowledge-file-assistants.js',
  'update-notification-types.js'
];

// 迁移函数
async function runMigrations() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 创建SequelizeMeta表（如果不存在）
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS \`SequelizeMeta\` (
        \`name\` VARCHAR(255) NOT NULL,
        PRIMARY KEY (\`name\`),
        UNIQUE INDEX \`name\` (\`name\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('SequelizeMeta表创建成功');

    // 获取已经执行的迁移
    const [results] = await sequelize.query('SELECT name FROM SequelizeMeta');
    const executedMigrations = results.map(result => result.name);
    console.log('已执行的迁移:', executedMigrations);

    // 迁移文件目录
    const migrationsDir = path.join(__dirname, 'src', 'migrations');

    // 按顺序执行迁移
    for (const migrationFile of migrationOrder) {
      // 如果迁移已经执行，跳过
      if (executedMigrations.includes(migrationFile)) {
        console.log(`迁移 ${migrationFile} 已经执行，跳过`);
        continue;
      }

      console.log(`执行迁移 ${migrationFile}...`);
      const migration = require(path.join(migrationsDir, migrationFile));
      
      // 执行迁移的up方法
      await migration.up(sequelize.getQueryInterface(), Sequelize);
      
      // 将迁移记录添加到SequelizeMeta表
      await sequelize.query('INSERT INTO SequelizeMeta (name) VALUES (?)', {
        replacements: [migrationFile]
      });
      
      console.log(`迁移 ${migrationFile} 执行成功`);
    }

    console.log('所有迁移执行完成');
    process.exit(0);
  } catch (error) {
    console.error('迁移执行失败:', error);
    process.exit(1);
  }
}

// 运行迁移
runMigrations();
