# 生产环境数据库修复脚本使用说明

## 问题描述

生产环境出现 `AxiosError: Request failed with status code 500` 错误，错误信息为 "获取消息列表失败"。这是因为生产环境数据库缺少新增的 `messages` 表和相关权限配置。

## 解决方案

使用 `fix-production-database.js` 脚本安全地修复生产环境数据库结构。

## 脚本功能

该脚本会安全地执行以下操作：

1. **检查并创建 messages 表**
   - 如果表不存在，则创建完整的 messages 表结构
   - 如果表已存在，则跳过创建步骤

2. **添加消息相关权限**
   - `content:publish` - 发布消息权限
   - `content:manage` - 管理消息权限  
   - `content:view` - 查看消息权限

3. **为管理员角色分配权限**
   - 自动为管理员角色分配所有消息相关权限

## 安全特性

- ✅ **事务保护**: 所有操作在事务中执行，出错时自动回滚
- ✅ **幂等性**: 可以重复执行，不会重复创建已存在的表或权限
- ✅ **数据保护**: 只添加缺失的结构，不会修改或删除现有数据
- ✅ **详细日志**: 输出详细的执行日志，便于监控和调试

## 使用方法

### 1. 在生产服务器上执行

```bash
# 进入后端目录
cd /path/to/your/backend

# 设置环境变量（根据实际情况修改）
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
export DB_NAME=your_database_name

# 执行修复脚本
node scripts/fix-production-database.js
```

### 2. 使用 Docker 环境变量

如果使用 Docker 部署，可以直接使用现有的环境变量：

```bash
# 在 Docker 容器中执行
docker exec -it your_backend_container node scripts/fix-production-database.js
```

### 3. 本地测试（可选）

在本地环境测试脚本：

```bash
# 设置测试数据库环境变量
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=test_password
export DB_NAME=test_database

# 执行脚本
node scripts/fix-production-database.js
```

## 执行输出示例

```
=== 生产环境数据库修复脚本 ===
数据库配置: {
  host: 'localhost',
  port: 3306,
  user: 'root',
  database: 'hefamily_prod'
}
连接数据库...
✓ 数据库连接成功
✓ 开始事务
开始检查数据库结构...
创建消息表...
✓ 消息表创建成功
添加消息相关权限...
✓ 创建权限: 发布消息 (content:publish)
✓ 创建权限: 管理消息 (content:manage)
✓ 创建权限: 查看消息 (content:view)
为管理员角色分配消息权限...
✓ 为管理员分配权限: content:publish
✓ 为管理员分配权限: content:manage
✓ 为管理员分配权限: content:view
数据库结构检查完成
✓ 事务提交成功
=== 数据库修复完成 ===
✓ 数据库连接已关闭
```

## 验证修复结果

执行脚本后，可以通过以下方式验证修复是否成功：

### 1. 检查表是否创建成功

```sql
-- 检查 messages 表是否存在
SHOW TABLES LIKE 'messages';

-- 查看表结构
DESCRIBE messages;
```

### 2. 检查权限是否添加成功

```sql
-- 查看消息相关权限
SELECT * FROM permissions WHERE code LIKE 'content:%';

-- 查看管理员角色的权限分配
SELECT r.name as role_name, p.name as permission_name, p.code as permission_code
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.name = 'admin' AND p.code LIKE 'content:%';
```

### 3. 测试 API 接口

修复完成后，测试消息列表 API：

```bash
# 测试获取消息列表
curl -X GET "http://your-domain/api/messages" \
  -H "Authorization: Bearer your_token"
```

## 注意事项

1. **备份数据库**: 虽然脚本是安全的，但建议在执行前备份生产数据库
2. **权限检查**: 确保执行脚本的数据库用户有创建表和插入数据的权限
3. **环境变量**: 确保设置了正确的数据库连接环境变量
4. **服务重启**: 修复完成后，建议重启后端服务以确保新的数据库结构生效

## 故障排除

### 常见错误及解决方法

1. **连接失败**
   ```
   错误: 请设置环境变量 DB_PASSWORD 和 DB_NAME
   ```
   解决: 确保设置了正确的环境变量

2. **权限不足**
   ```
   Error: Access denied for user
   ```
   解决: 确保数据库用户有足够的权限

3. **表已存在**
   ```
   ✓ 消息表已存在
   ```
   这是正常情况，表示表已经存在，脚本会跳过创建步骤

## 联系支持

如果在执行过程中遇到问题，请提供：
1. 完整的错误日志
2. 数据库版本信息
3. 执行环境信息

这样可以更快地定位和解决问题。
