/**
 * 添加消息发布权限的脚本
 * 用于在权限系统中添加消息相关的权限
 */

require('dotenv').config();
const { User, Role, Permission, sequelize } = require('../src/models');

/**
 * 添加消息相关权限
 */
async function addMessagePermissions() {
  try {
    console.log('开始添加消息相关权限...');

    // 消息相关权限列表
    const messagePermissions = [
      {
        name: '发布消息',
        code: 'content:publish',
        description: '发布和管理消息内容',
        module: 'content'
      },
      {
        name: '管理消息',
        code: 'content:manage',
        description: '管理所有消息内容（编辑、删除、审核）',
        module: 'content'
      },
      {
        name: '查看消息',
        code: 'content:view',
        description: '查看消息内容',
        module: 'content'
      }
    ];

    // 检查并创建权限
    for (const permissionData of messagePermissions) {
      const [permission, created] = await Permission.findOrCreate({
        where: { code: permissionData.code },
        defaults: permissionData
      });

      if (created) {
        console.log(`✓ 创建权限: ${permission.name} (${permission.code})`);
      } else {
        console.log(`- 权限已存在: ${permission.name} (${permission.code})`);
      }
    }

    // 为管理员角色添加所有消息权限
    const adminRole = await Role.findOne({
      where: { name: '管理员' }
    });

    if (adminRole) {
      console.log('为管理员角色添加消息权限...');

      const allMessagePermissions = await Permission.findAll({
        where: {
          code: ['content:publish', 'content:manage', 'content:view']
        }
      });

      // 添加权限到管理员角色（如果还没有的话）
      for (const permission of allMessagePermissions) {
        try {
          // 检查是否已有此权限
          const existingPermissions = await adminRole.getPermissions({
            where: { id: permission.id }
          });

          if (existingPermissions.length === 0) {
            await adminRole.addPermission(permission);
            console.log(`✓ 为管理员添加权限: ${permission.name}`);
          } else {
            console.log(`- 管理员已有权限: ${permission.name}`);
          }
        } catch (error) {
          console.error(`添加权限失败: ${permission.name}`, error.message);
        }
      }
    } else {
      console.log('警告: 未找到管理员角色');
    }

    // 为访问者角色添加查看权限
    const visitorRole = await Role.findOne({
      where: { name: '访问者' }
    });

    if (visitorRole) {
      console.log('为访问者角色添加消息查看权限...');

      const viewPermission = await Permission.findOne({
        where: { code: 'content:view' }
      });

      if (viewPermission) {
        try {
          // 检查是否已有此权限
          const existingPermissions = await visitorRole.getPermissions({
            where: { id: viewPermission.id }
          });

          if (existingPermissions.length === 0) {
            await visitorRole.addPermission(viewPermission);
            console.log(`✓ 为访问者添加权限: ${viewPermission.name}`);
          } else {
            console.log(`- 访问者已有权限: ${viewPermission.name}`);
          }
        } catch (error) {
          console.error(`为访问者添加权限失败: ${viewPermission.name}`, error.message);
        }
      }
    } else {
      console.log('警告: 未找到访问者角色');
    }

    console.log('消息权限添加完成！');
  } catch (error) {
    console.error('添加消息权限失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 执行脚本
addMessagePermissions();
