/**
 * 添加消息表的数据库迁移脚本
 * 用于创建消息发布功能所需的数据库表
 */

const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'Misaya9987.',
  database: process.env.DB_NAME || 'hefamily_dev'
};

/**
 * 添加消息表
 */
async function addMessagesTable() {
  console.log('开始添加消息表...');
  let connection;

  try {
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 检查消息表是否已存在
    const [tables] = await connection.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'messages'
    `, [dbConfig.database]);

    if (tables.length === 0) {
      console.log('创建消息表...');
      
      // 创建消息表
      await connection.query(`
        CREATE TABLE IF NOT EXISTS messages (
          id INT NOT NULL AUTO_INCREMENT,
          title VARCHAR(255) NOT NULL COMMENT '消息标题',
          content LONGTEXT NULL COMMENT '消息内容（富文本）',
          excerpt TEXT NULL COMMENT '消息摘要（前100字）',
          cover_image VARCHAR(500) NULL COMMENT '首页图片URL',
          author VARCHAR(100) NOT NULL COMMENT '作者名称',
          type ENUM('content', 'link') NOT NULL DEFAULT 'content' COMMENT '消息类型：content=自定义内容，link=外部链接',
          external_url VARCHAR(500) NULL COMMENT '外部链接URL（仅当type=link时使用）',
          status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft' COMMENT '状态：draft=草稿，published=已发布，archived=已归档',
          view_count INT NOT NULL DEFAULT 0 COMMENT '查看次数',
          created_by INT NOT NULL COMMENT '创建者ID',
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY idx_status (status),
          KEY idx_type (type),
          KEY idx_created_at (created_at),
          KEY created_by (created_by),
          CONSTRAINT messages_ibfk_1 FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表'
      `);

      console.log('消息表创建成功');
    } else {
      console.log('消息表已存在，跳过创建步骤');
    }

    console.log('消息表添加完成');
  } catch (error) {
    console.error('添加消息表失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行迁移
addMessagesTable();
