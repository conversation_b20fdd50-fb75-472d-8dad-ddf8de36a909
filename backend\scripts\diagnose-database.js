/**
 * 数据库诊断脚本
 * 
 * 检查生产环境数据库状态，诊断可能的问题
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || process.argv[2] || 'localhost',
  port: process.env.DB_PORT || process.argv[3] || 3306,
  user: process.env.DB_USER || process.argv[4] || 'root',
  password: process.env.DB_PASSWORD || process.argv[5],
  database: process.env.DB_NAME || process.argv[6]
};

/**
 * 诊断数据库状态
 */
async function diagnoseDatabase() {
  console.log('=== 数据库诊断工具 ===');
  
  if (!dbConfig.password || !dbConfig.database) {
    console.error('错误: 缺少数据库密码或数据库名');
    console.log('使用方法: node diagnose-database.js [host] [port] [user] [password] [database]');
    process.exit(1);
  }
  
  console.log('数据库配置:', {
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    database: dbConfig.database
  });
  
  let connection;
  
  try {
    // 连接数据库
    console.log('\n1. 测试数据库连接...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ 数据库连接成功');
    
    // 检查数据库版本
    console.log('\n2. 检查数据库版本...');
    const [versionResult] = await connection.query('SELECT VERSION() as version');
    console.log('✓ MySQL版本:', versionResult[0].version);
    
    // 检查所有表
    console.log('\n3. 检查数据库表...');
    const [tables] = await connection.query(`
      SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ?
      ORDER BY TABLE_NAME
    `, [dbConfig.database]);
    
    console.log('数据库表列表:');
    tables.forEach(table => {
      const dataSize = (table.DATA_LENGTH / 1024).toFixed(2);
      const indexSize = (table.INDEX_LENGTH / 1024).toFixed(2);
      console.log(`  - ${table.TABLE_NAME}: ${table.TABLE_ROWS} 行, 数据: ${dataSize}KB, 索引: ${indexSize}KB`);
    });
    
    // 重点检查 messages 表
    console.log('\n4. 检查 messages 表...');
    const messagesTable = tables.find(t => t.TABLE_NAME === 'messages');
    if (messagesTable) {
      console.log('✓ messages 表存在');
      
      // 检查表结构
      const [columns] = await connection.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'messages'
        ORDER BY ORDINAL_POSITION
      `, [dbConfig.database]);
      
      console.log('  表结构:');
      columns.forEach(col => {
        console.log(`    ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} - ${col.COLUMN_COMMENT}`);
      });
      
      // 检查数据
      const [messageCount] = await connection.query('SELECT COUNT(*) as count FROM messages');
      console.log(`  数据行数: ${messageCount[0].count}`);
      
      if (messageCount[0].count > 0) {
        const [sampleData] = await connection.query('SELECT id, title, status, created_at FROM messages ORDER BY created_at DESC LIMIT 3');
        console.log('  最新数据样本:');
        sampleData.forEach(msg => {
          console.log(`    ID: ${msg.id}, 标题: ${msg.title}, 状态: ${msg.status}, 创建时间: ${msg.created_at}`);
        });
      }
    } else {
      console.log('❌ messages 表不存在 - 这是问题的根源!');
    }
    
    // 检查用户表
    console.log('\n5. 检查 users 表...');
    const usersTable = tables.find(t => t.TABLE_NAME === 'users');
    if (usersTable) {
      const [userCount] = await connection.query('SELECT COUNT(*) as count FROM users');
      console.log(`✓ users 表存在，用户数: ${userCount[0].count}`);
    } else {
      console.log('❌ users 表不存在');
    }
    
    // 检查权限相关表
    console.log('\n6. 检查权限系统...');
    const permissionsTable = tables.find(t => t.TABLE_NAME === 'permissions');
    const rolesTable = tables.find(t => t.TABLE_NAME === 'roles');
    
    if (permissionsTable && rolesTable) {
      console.log('✓ 权限系统表存在');
      
      // 检查消息相关权限
      const [messagePermissions] = await connection.query(`
        SELECT * FROM permissions WHERE code LIKE 'content:%'
      `);
      
      if (messagePermissions.length > 0) {
        console.log('✓ 消息相关权限存在:');
        messagePermissions.forEach(perm => {
          console.log(`    ${perm.code}: ${perm.name}`);
        });
      } else {
        console.log('❌ 缺少消息相关权限');
      }
    } else {
      console.log('❌ 权限系统表缺失');
    }
    
    // 检查外键约束
    console.log('\n7. 检查外键约束...');
    const [constraints] = await connection.query(`
      SELECT 
        CONSTRAINT_NAME,
        TABLE_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = ? AND REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY TABLE_NAME, CONSTRAINT_NAME
    `, [dbConfig.database]);
    
    console.log('外键约束:');
    constraints.forEach(constraint => {
      console.log(`  ${constraint.TABLE_NAME}.${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}`);
    });
    
    // 总结诊断结果
    console.log('\n=== 诊断总结 ===');
    
    const issues = [];
    const recommendations = [];
    
    if (!messagesTable) {
      issues.push('messages 表不存在');
      recommendations.push('运行 quick-fix-messages.js 创建 messages 表');
    }
    
    if (!usersTable) {
      issues.push('users 表不存在');
      recommendations.push('检查数据库初始化是否完整');
    }
    
    if (!permissionsTable || !rolesTable) {
      issues.push('权限系统表缺失');
      recommendations.push('运行完整的数据库迁移脚本');
    }
    
    if (issues.length === 0) {
      console.log('✓ 未发现明显的数据库结构问题');
      console.log('如果仍有API错误，请检查:');
      console.log('  1. 后端服务配置');
      console.log('  2. 数据库连接字符串');
      console.log('  3. 用户权限设置');
      console.log('  4. 应用程序日志');
    } else {
      console.log('❌ 发现以下问题:');
      issues.forEach(issue => console.log(`  - ${issue}`));
      
      console.log('\n建议解决方案:');
      recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
    
  } catch (error) {
    console.error('诊断失败:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('❌ 数据库访问被拒绝，请检查用户名和密码');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('❌ 无法连接到数据库，请检查数据库服务是否运行');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('❌ 数据库不存在，请检查数据库名称');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n✓ 数据库连接已关闭');
    }
  }
}

// 运行诊断
diagnoseDatabase();
