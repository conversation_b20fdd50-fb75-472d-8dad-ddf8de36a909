/**
 * 生产环境数据库修复脚本
 * 
 * 安全地修复生产环境数据库结构，确保与新功能兼容
 * 不会影响现有数据，只添加缺失的表和字段
 */

const mysql = require('mysql2/promise');

// 数据库连接配置 - 从环境变量读取
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

/**
 * 检查表是否存在
 * @param {Object} connection - 数据库连接
 * @param {string} tableName - 表名
 * @returns {boolean} 表是否存在
 */
async function tableExists(connection, tableName) {
  const [tables] = await connection.query(`
    SELECT TABLE_NAME 
    FROM information_schema.TABLES 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
  `, [dbConfig.database, tableName]);
  
  return tables.length > 0;
}

/**
 * 检查字段是否存在
 * @param {Object} connection - 数据库连接
 * @param {string} tableName - 表名
 * @param {string} columnName - 字段名
 * @returns {boolean} 字段是否存在
 */
async function columnExists(connection, tableName, columnName) {
  const [columns] = await connection.query(`
    SELECT COLUMN_NAME 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?
  `, [dbConfig.database, tableName, columnName]);
  
  return columns.length > 0;
}

/**
 * 检查权限是否存在
 * @param {Object} connection - 数据库连接
 * @param {string} permissionCode - 权限代码
 * @returns {boolean} 权限是否存在
 */
async function permissionExists(connection, permissionCode) {
  const [permissions] = await connection.query(`
    SELECT id FROM permissions WHERE code = ?
  `, [permissionCode]);
  
  return permissions.length > 0;
}

/**
 * 创建消息表
 * @param {Object} connection - 数据库连接
 */
async function createMessagesTable(connection) {
  console.log('创建消息表...');
  
  await connection.query(`
    CREATE TABLE IF NOT EXISTS messages (
      id INT NOT NULL AUTO_INCREMENT,
      title VARCHAR(255) NOT NULL COMMENT '消息标题',
      content LONGTEXT NULL COMMENT '消息内容（富文本）',
      excerpt TEXT NULL COMMENT '消息摘要（前100字）',
      cover_image VARCHAR(500) NULL COMMENT '首页图片URL',
      author VARCHAR(100) NOT NULL COMMENT '作者名称',
      type ENUM('content', 'link') NOT NULL DEFAULT 'content' COMMENT '消息类型：content=自定义内容，link=外部链接',
      external_url VARCHAR(500) NULL COMMENT '外部链接URL（仅当type=link时使用）',
      status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft' COMMENT '状态：draft=草稿，published=已发布，archived=已归档',
      view_count INT NOT NULL DEFAULT 0 COMMENT '查看次数',
      created_by INT NOT NULL COMMENT '创建者ID',
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      KEY idx_status (status),
      KEY idx_type (type),
      KEY idx_created_at (created_at),
      KEY created_by (created_by),
      CONSTRAINT messages_ibfk_1 FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表'
  `);
  
  console.log('✓ 消息表创建成功');
}

/**
 * 添加消息相关权限
 * @param {Object} connection - 数据库连接
 */
async function addMessagePermissions(connection) {
  console.log('添加消息相关权限...');
  
  const messagePermissions = [
    {
      name: '发布消息',
      code: 'content:publish',
      description: '发布和管理消息内容',
      module: 'content'
    },
    {
      name: '管理消息',
      code: 'content:manage',
      description: '管理所有消息内容（编辑、删除、审核）',
      module: 'content'
    },
    {
      name: '查看消息',
      code: 'content:view',
      description: '查看消息内容',
      module: 'content'
    }
  ];

  for (const permission of messagePermissions) {
    const exists = await permissionExists(connection, permission.code);
    if (!exists) {
      await connection.query(`
        INSERT INTO permissions (name, code, description, module, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `, [permission.name, permission.code, permission.description, permission.module]);
      
      console.log(`✓ 创建权限: ${permission.name} (${permission.code})`);
    } else {
      console.log(`- 权限已存在: ${permission.name} (${permission.code})`);
    }
  }
}

/**
 * 为管理员角色分配消息权限
 * @param {Object} connection - 数据库连接
 */
async function assignMessagePermissionsToAdmin(connection) {
  console.log('为管理员角色分配消息权限...');
  
  // 获取管理员角色ID
  const [adminRoles] = await connection.query(`
    SELECT id FROM roles WHERE name = 'admin' OR code = 'admin' LIMIT 1
  `);
  
  if (adminRoles.length === 0) {
    console.log('未找到管理员角色，跳过权限分配');
    return;
  }
  
  const adminRoleId = adminRoles[0].id;
  
  // 获取消息相关权限ID
  const [permissions] = await connection.query(`
    SELECT id, code FROM permissions WHERE code LIKE 'content:%'
  `);
  
  for (const permission of permissions) {
    // 检查是否已经分配
    const [existing] = await connection.query(`
      SELECT id FROM role_permissions WHERE role_id = ? AND permission_id = ?
    `, [adminRoleId, permission.id]);
    
    if (existing.length === 0) {
      await connection.query(`
        INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
        VALUES (?, ?, NOW(), NOW())
      `, [adminRoleId, permission.id]);
      
      console.log(`✓ 为管理员分配权限: ${permission.code}`);
    } else {
      console.log(`- 管理员已有权限: ${permission.code}`);
    }
  }
}

/**
 * 检查并修复数据库结构
 * @param {Object} connection - 数据库连接
 */
async function checkAndFixDatabase(connection) {
  console.log('开始检查数据库结构...');
  
  // 检查并创建消息表
  const messagesTableExists = await tableExists(connection, 'messages');
  if (!messagesTableExists) {
    await createMessagesTable(connection);
  } else {
    console.log('✓ 消息表已存在');
  }
  
  // 添加消息相关权限
  await addMessagePermissions(connection);
  
  // 为管理员分配权限
  await assignMessagePermissionsToAdmin(connection);
  
  console.log('数据库结构检查完成');
}

/**
 * 主函数
 */
async function main() {
  console.log('=== 生产环境数据库修复脚本 ===');
  console.log('数据库配置:', {
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    database: dbConfig.database
  });
  
  if (!dbConfig.password || !dbConfig.database) {
    console.error('错误: 请设置环境变量 DB_PASSWORD 和 DB_NAME');
    process.exit(1);
  }
  
  let connection;
  
  try {
    // 连接数据库
    console.log('连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ 数据库连接成功');
    
    // 开始事务
    await connection.beginTransaction();
    console.log('✓ 开始事务');
    
    // 检查并修复数据库
    await checkAndFixDatabase(connection);
    
    // 提交事务
    await connection.commit();
    console.log('✓ 事务提交成功');
    
    console.log('=== 数据库修复完成 ===');
    
  } catch (error) {
    console.error('数据库修复失败:', error);
    
    if (connection) {
      try {
        await connection.rollback();
        console.log('✓ 事务已回滚');
      } catch (rollbackError) {
        console.error('事务回滚失败:', rollbackError);
      }
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('✓ 数据库连接已关闭');
    }
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  main,
  checkAndFixDatabase,
  createMessagesTable,
  addMessagePermissions
};
