/**
 * 快速修复消息表问题
 * 
 * 专门解决生产环境 "获取消息列表失败" 的问题
 * 只创建 messages 表，不涉及其他复杂操作
 */

const mysql = require('mysql2/promise');

// 从环境变量或命令行参数获取数据库配置
const dbConfig = {
  host: process.env.DB_HOST || process.argv[2] || 'localhost',
  port: process.env.DB_PORT || process.argv[3] || 3306,
  user: process.env.DB_USER || process.argv[4] || 'root',
  password: process.env.DB_PASSWORD || process.argv[5],
  database: process.env.DB_NAME || process.argv[6]
};

/**
 * 快速修复函数
 */
async function quickFix() {
  console.log('=== 快速修复消息表问题 ===');
  
  // 检查必要参数
  if (!dbConfig.password || !dbConfig.database) {
    console.error('错误: 缺少数据库密码或数据库名');
    console.log('使用方法:');
    console.log('1. 环境变量方式:');
    console.log('   export DB_PASSWORD=your_password');
    console.log('   export DB_NAME=your_database');
    console.log('   node quick-fix-messages.js');
    console.log('');
    console.log('2. 命令行参数方式:');
    console.log('   node quick-fix-messages.js host port user password database');
    console.log('   例: node quick-fix-messages.js localhost 3306 root mypass hefamily_prod');
    process.exit(1);
  }
  
  console.log('数据库配置:', {
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    database: dbConfig.database
  });
  
  let connection;
  
  try {
    // 连接数据库
    console.log('正在连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ 数据库连接成功');
    
    // 检查 messages 表是否存在
    console.log('检查 messages 表是否存在...');
    const [tables] = await connection.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'messages'
    `, [dbConfig.database]);
    
    if (tables.length > 0) {
      console.log('✓ messages 表已存在，无需创建');
      console.log('问题可能不是缺少表，请检查其他原因：');
      console.log('1. 检查后端服务是否正常运行');
      console.log('2. 检查数据库连接配置');
      console.log('3. 检查用户权限设置');
      return;
    }
    
    // 创建 messages 表
    console.log('创建 messages 表...');
    await connection.query(`
      CREATE TABLE messages (
        id INT NOT NULL AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL COMMENT '消息标题',
        content LONGTEXT NULL COMMENT '消息内容（富文本）',
        excerpt TEXT NULL COMMENT '消息摘要（前100字）',
        cover_image VARCHAR(500) NULL COMMENT '首页图片URL',
        author VARCHAR(100) NOT NULL COMMENT '作者名称',
        type ENUM('content', 'link') NOT NULL DEFAULT 'content' COMMENT '消息类型',
        external_url VARCHAR(500) NULL COMMENT '外部链接URL',
        status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft' COMMENT '状态',
        view_count INT NOT NULL DEFAULT 0 COMMENT '查看次数',
        created_by INT NOT NULL COMMENT '创建者ID',
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_status (status),
        KEY idx_type (type),
        KEY idx_created_at (created_at),
        KEY created_by (created_by),
        CONSTRAINT messages_ibfk_1 FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表'
    `);
    
    console.log('✓ messages 表创建成功');
    
    // 验证表创建
    const [newTables] = await connection.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'messages'
    `, [dbConfig.database]);
    
    if (newTables.length > 0) {
      console.log('✓ 验证成功: messages 表已存在');
    } else {
      console.log('❌ 验证失败: messages 表创建后仍不存在');
    }
    
    console.log('=== 修复完成 ===');
    console.log('请重启后端服务以使更改生效');
    
  } catch (error) {
    console.error('修复失败:', error.message);
    
    // 提供更详细的错误信息
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('');
      console.log('解决方案:');
      console.log('1. 检查数据库用户名和密码是否正确');
      console.log('2. 确保数据库用户有足够的权限');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('解决方案:');
      console.log('1. 检查数据库服务是否运行');
      console.log('2. 检查主机和端口是否正确');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('');
      console.log('解决方案:');
      console.log('1. 检查数据库名是否正确');
      console.log('2. 确保数据库存在');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('✓ 数据库连接已关闭');
    }
  }
}

// 运行修复
quickFix();
