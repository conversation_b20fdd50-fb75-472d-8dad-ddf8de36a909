/**
 * 简单的图片访问测试
 */

const fs = require('fs');
const path = require('path');

function testImageFiles() {
  console.log('=== 测试图片文件访问 ===');
  
  // 检查uploads目录结构
  const uploadsDir = path.join(__dirname, 'public/uploads');
  const imagesDir = path.join(uploadsDir, 'images');
  
  console.log('检查目录结构:');
  console.log('当前目录:', __dirname);
  console.log('uploads目录:', uploadsDir);
  console.log('uploads目录存在:', fs.existsSync(uploadsDir) ? '✅ 是' : '❌ 否');
  console.log('images目录:', imagesDir);
  console.log('images目录存在:', fs.existsSync(imagesDir) ? '✅ 是' : '❌ 否');
  
  if (fs.existsSync(imagesDir)) {
    const files = fs.readdirSync(imagesDir);
    console.log('\nimages目录中的文件数量:', files.length);
    
    // 查找最新的external文件
    const externalFiles = files.filter(file => file.startsWith('external-'));
    console.log('external文件数量:', externalFiles.length);
    
    if (externalFiles.length > 0) {
      console.log('\n最新的external文件:');
      externalFiles.slice(-3).forEach(file => {
        const filePath = path.join(imagesDir, file);
        const stats = fs.statSync(filePath);
        console.log(`  - ${file}`);
        console.log(`    大小: ${stats.size} bytes`);
        console.log(`    修改时间: ${stats.mtime.toISOString()}`);
        
        // 检查文件内容
        if (stats.size > 0) {
          const buffer = fs.readFileSync(filePath);
          const header = buffer.slice(0, 10);
          console.log(`    文件头: ${header.toString('hex')}`);
          
          // 检查图片格式
          const jpegHeader = buffer.slice(0, 3).toString('hex');
          if (jpegHeader === 'ffd8ff') {
            console.log(`    格式: ✅ JPEG`);
          } else {
            console.log(`    格式: ⚠️ 未知`);
          }
        } else {
          console.log(`    状态: ❌ 文件为空`);
        }
        console.log('');
      });
      
      // 测试URL构建
      console.log('=== 测试URL构建 ===');
      const testFile = externalFiles[externalFiles.length - 1];
      const relativePath = `/uploads/images/${testFile}`;
      
      console.log('测试文件:', testFile);
      console.log('相对路径:', relativePath);
      
      // 不同环境的URL
      const urls = [
        `http://localhost:5001${relativePath}`,
        `http://**********:5001${relativePath}`,
        `http://**********${relativePath}`
      ];
      
      console.log('\n可能的访问URL:');
      urls.forEach((url, index) => {
        console.log(`${index + 1}. ${url}`);
      });
    }
  }
}

testImageFiles();
