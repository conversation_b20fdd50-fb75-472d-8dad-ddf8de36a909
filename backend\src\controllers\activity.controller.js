/**
 * 活动控制器
 *
 * 处理活动相关的业务逻辑，如创建、查询、更新、删除活动等
 */

const { Activity, ActivityAttachment, User } = require('../models');
const { Op } = require('sequelize');
const fs = require('fs');
const path = require('path');

/**
 * 获取活动列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getActivities = async (req, res) => {
  try {
    const { status, search } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereConditions = {};

    // 根据状态筛选
    if (status !== undefined) {
      // 如果status是空字符串，不添加状态过滤条件
      if (status === '') {
        console.log('状态参数为空字符串，不添加状态过滤条件');
        // 不添加status条件，查询所有状态
      } else {
        console.log(`状态参数为: "${status}"`);
        whereConditions.status = status;
      }
    } else {
      // 默认只显示已发布的活动
      console.log('状态参数未提供，默认只显示已发布活动');
      whereConditions.status = 'published';
    }

    // 根据标题或描述搜索
    if (search) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 详细记录请求信息
    console.log('活动列表请求详情:');
    console.log('- 请求路径:', req.path);
    console.log('- 请求方法:', req.method);
    console.log('- 请求参数:', req.query);
    console.log('- 请求头:', req.headers);
    console.log('- 用户信息:', req.user || '未登录');

    // 如果是管理员或有管理活动权限的用户，可以查看所有状态的活动
    if (req.user && (req.user.role === 'admin' || req.user.permissions?.includes('manage_activities') || req.user.permissions?.includes('activity:manage'))) {
      console.log('管理员或有权限用户访问活动列表，用户信息:', req.user);

      // 如果没有指定状态参数，则查询所有状态
      if (status === undefined) {
        delete whereConditions.status;
        console.log('管理员查询所有状态活动 (status参数未提供)');
      }
      // 如果status参数为空字符串，也查询所有状态
      else if (status === '') {
        delete whereConditions.status;
        console.log('管理员查询所有状态活动 (status参数为空字符串)');
      }
      // 如果明确指定了状态，则按指定状态查询
      else {
        console.log(`管理员查询指定状态活动: ${status}`);
      }
    } else {
      console.log('普通用户访问活动列表，只能查看已发布活动');
      console.log('用户信息:', req.user || '未登录');
    }

    // 调试信息
    console.log('最终活动查询条件:', whereConditions);

    console.log('活动查询条件:', whereConditions);

    // 查询活动前记录查询条件
    console.log('执行数据库查询，条件:', JSON.stringify(whereConditions));
    console.log('分页参数:', { limit, offset, page });

    try {
      // 查询活动，包含附件信息
      const { count, rows: activities } = await Activity.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username', 'email']
          },
          {
            model: ActivityAttachment,
            as: 'attachments',
            required: false, // 左连接，即使没有附件也返回活动
            attributes: ['id', 'name', 'original_name', 'type', 'size', 'created_at'],
            include: [
              {
                model: User,
                as: 'uploader',
                attributes: ['id', 'username']
              }
            ]
          }
        ],
        limit,
        offset,
        order: [['date', 'DESC']]
      });

      // 记录查询结果
      console.log(`查询结果: 找到 ${count} 条活动记录`);
      console.log('活动ID列表:', activities.map(a => a.id));

      // 格式化文件大小的辅助函数
      const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };

      // 处理活动数据，格式化附件大小
      const processedActivities = activities.map(activity => {
        const plainActivity = activity.get({ plain: true });

        // 处理附件数据
        if (plainActivity.attachments && plainActivity.attachments.length > 0) {
          plainActivity.attachments = plainActivity.attachments.map(attachment => ({
            ...attachment,
            size: formatFileSize(attachment.size)
          }));
        }

        return plainActivity;
      });

      // 如果没有找到活动，尝试不带条件查询一次
      if (count === 0) {
        console.log('未找到活动，尝试不带条件查询所有活动...');
        const allActivities = await Activity.findAll({
          include: [
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'username', 'email']
            }
          ],
          limit: 5,
          order: [['id', 'DESC']]
        });

        console.log(`不带条件查询结果: 找到 ${allActivities.length} 条活动记录`);
        if (allActivities.length > 0) {
          console.log('数据库中存在活动，但当前查询条件未匹配到。');
          console.log('活动示例:', allActivities[0].toJSON());
        } else {
          console.log('数据库中不存在任何活动记录。');
        }
      }

      // 计算总页数
      const totalPages = Math.ceil(count / limit);

      // 返回响应
      const response = {
        success: true,
        data: {
          activities: processedActivities,
          pagination: {
            total: count,
            page,
            limit,
            totalPages
          }
        }
      };

      console.log('返回响应:', {
        success: true,
        activitiesCount: activities.length,
        pagination: {
          total: count,
          page,
          limit,
          totalPages
        }
      });

      res.status(200).json(response);
    } catch (error) {
      console.error('查询活动时发生错误:', error);
      throw error;
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取活动列表失败',
      error: error.message
    });
  }
};

/**
 * 获取活动详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getActivityById = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询活动
    const activity = await Activity.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!activity) {
      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }

    // 如果活动未发布，只有管理员或创建者可以查看
    if (activity.status !== 'published') {
      if (!req.user || (req.user.role !== 'admin' && req.user.id !== activity.creator_id)) {
        return res.status(403).json({
          success: false,
          message: '您没有权限查看该活动'
        });
      }
    }

    // 获取活动附件
    const attachments = await ActivityAttachment.findAll({
      where: { activity_id: id },
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(200).json({
      success: true,
      data: {
        ...activity.toJSON(),
        attachments
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取活动详情失败',
      error: error.message
    });
  }
};

/**
 * 创建活动
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createActivity = async (req, res) => {
  try {
    const { title, date, description, image, status } = req.body;

    if (!title || !date || !description) {
      return res.status(400).json({
        success: false,
        message: '标题、日期和描述不能为空'
      });
    }

    // 创建活动
    const activity = await Activity.create({
      title,
      date: new Date(date),
      description,
      image,
      status: status || 'draft',
      creator_id: req.user.id,
      last_updated_by: req.user.id
    });

    res.status(201).json({
      success: true,
      message: '活动创建成功',
      data: activity
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建活动失败',
      error: error.message
    });
  }
};

/**
 * 上传活动图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadActivityImage = async (req, res) => {
  try {
    // 检查文件是否上传
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未上传图片'
      });
    }

    // 处理文件路径，确保使用相对路径
    // 从完整路径中提取相对于项目根目录的路径
    const relativePath = req.file.path.replace(/\\/g, '/').replace(/^.*[\/\\]uploads[\/\\]/, 'uploads/');

    // 构建URL，确保使用正确的路径
    const fileUrl = `${req.protocol}://${req.get('host')}/${relativePath}`;

    console.log('上传活动图片成功:', {
      originalPath: req.file.path,
      relativePath: relativePath,
      url: fileUrl,
      filename: req.file.filename
    });

    res.status(200).json({
      success: true,
      message: '图片上传成功',
      data: {
        path: relativePath,
        url: fileUrl
      }
    });
  } catch (error) {
    // 如果文件已上传但处理失败，删除文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: '上传图片失败',
      error: error.message
    });
  }
};

/**
 * 更新活动
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, date, description, image, status } = req.body;

    // 查询活动
    const activity = await Activity.findByPk(id);

    if (!activity) {
      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }

    // 更新活动
    await activity.update({
      title: title || activity.title,
      date: date ? new Date(date) : activity.date,
      description: description !== undefined ? description : activity.description,
      image: image !== undefined ? image : activity.image,
      status: status || activity.status,
      last_updated_by: req.user.id
    });

    // 获取更新后的活动信息
    const updatedActivity = await Activity.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: '活动更新成功',
      data: updatedActivity
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新活动失败',
      error: error.message
    });
  }
};

/**
 * 删除活动
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteActivity = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询活动
    const activity = await Activity.findByPk(id);

    if (!activity) {
      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }

    // 查询活动附件
    const attachments = await ActivityAttachment.findAll({
      where: { activity_id: id }
    });

    // 删除附件文件
    for (const attachment of attachments) {
      if (fs.existsSync(attachment.path)) {
        fs.unlinkSync(attachment.path);
      }
    }

    // 删除活动图片
    if (activity.image && fs.existsSync(activity.image)) {
      fs.unlinkSync(activity.image);
    }

    // 删除活动附件记录
    await ActivityAttachment.destroy({
      where: { activity_id: id }
    });

    // 删除活动
    await activity.destroy();

    res.status(200).json({
      success: true,
      message: '活动删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除活动失败',
      error: error.message
    });
  }
};

/**
 * 获取活动附件列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getActivityAttachments = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询活动
    const activity = await Activity.findByPk(id);

    if (!activity) {
      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }

    // 允许所有人查看附件，无论活动状态如何
    console.log('获取活动附件:', {
      activityId: id,
      activityTitle: activity.title,
      activityStatus: activity.status
    });

    // 查询活动附件
    const attachments = await ActivityAttachment.findAll({
      where: { activity_id: id },
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // 处理附件数据，确保文件名正确编码和文件大小格式化
    const processedAttachments = attachments.map(attachment => {
      const plainAttachment = attachment.get({ plain: true });

      // 确保文件名正确显示
      if (plainAttachment.original_name) {
        console.log('原始文件名:', plainAttachment.original_name);
      }

      // 格式化文件大小
      const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };

      return {
        ...plainAttachment,
        size: formatFileSize(plainAttachment.size)
      };
    });

    res.status(200).json({
      success: true,
      data: processedAttachments
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取活动附件列表失败',
      error: error.message
    });
  }
};

/**
 * 上传活动附件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadActivityAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询活动
    const activity = await Activity.findByPk(id);

    if (!activity) {
      // 删除已上传的文件
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }

    // 检查文件是否上传
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未上传文件'
      });
    }

    // 处理文件路径，确保使用相对路径
    const relativePath = req.file.path.replace(/\\/g, '/').replace(/^.*[\/\\]uploads[\/\\]/, 'uploads/');

    console.log('上传附件:', {
      originalPath: req.file.path,
      relativePath: relativePath
    });

    // 确保文件名正确编码
    const decodedOriginalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
    console.log('文件名编码转换:', {
      original: req.file.originalname,
      decoded: decodedOriginalName
    });

    // 创建附件记录
    const attachment = await ActivityAttachment.create({
      activity_id: id,
      name: req.file.filename,
      original_name: decodedOriginalName, // 使用解码后的文件名
      path: relativePath,
      type: path.extname(decodedOriginalName).substring(1),
      mime_type: req.file.mimetype,
      size: req.file.size,
      uploader_id: req.user.id
    });

    // 获取完整的附件信息
    const fullAttachment = await ActivityAttachment.findByPk(attachment.id, {
      include: [
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '附件上传成功',
      data: fullAttachment
    });
  } catch (error) {
    // 如果文件已上传但创建记录失败，删除文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: '上传附件失败',
      error: error.message
    });
  }
};

/**
 * 下载活动附件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.downloadActivityAttachment = async (req, res) => {
  try {
    const { attachmentId } = req.params;

    // 查询附件
    const attachment = await ActivityAttachment.findByPk(attachmentId, {
      include: [
        {
          model: Activity,
          as: 'activity'
        }
      ]
    });

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: '附件不存在'
      });
    }

    // 允许所有人下载附件，无论活动状态如何
    console.log('下载附件:', {
      attachmentId: attachmentId,
      fileName: attachment.original_name,
      filePath: attachment.path,
      activityStatus: attachment.activity.status
    });

    // 检查文件是否存在
    if (!fs.existsSync(attachment.path)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在或已被删除'
      });
    }

    // 设置响应头
    res.setHeader('Content-Type', attachment.mime_type);

    // 使用RFC 5987编码文件名，解决中文文件名问题
    const encodedFilename = encodeURIComponent(attachment.original_name).replace(/['()]/g, escape);
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);

    console.log('下载文件名编码:', {
      original: attachment.original_name,
      encoded: encodedFilename
    });

    // 发送文件
    const fileStream = fs.createReadStream(attachment.path);
    fileStream.pipe(res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '下载附件失败',
      error: error.message
    });
  }
};

/**
 * 预览活动附件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.previewActivityAttachment = async (req, res) => {
  try {
    const { attachmentId } = req.params;

    // 查询附件
    const attachment = await ActivityAttachment.findByPk(attachmentId, {
      include: [
        {
          model: Activity,
          as: 'activity'
        }
      ]
    });

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: '附件不存在'
      });
    }

    // 允许所有人预览附件，无论活动状态如何
    console.log('预览附件:', {
      attachmentId: attachmentId,
      fileName: attachment.original_name,
      filePath: attachment.path,
      mimeType: attachment.mime_type,
      activityStatus: attachment.activity.status
    });

    // 检查文件是否存在
    if (!fs.existsSync(attachment.path)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在或已被删除'
      });
    }

    // 设置响应头用于预览（不强制下载）
    res.setHeader('Content-Type', attachment.mime_type);

    // 对于PDF文件，设置inline显示
    if (attachment.mime_type === 'application/pdf') {
      res.setHeader('Content-Disposition', 'inline');
      // 添加CORS头以支持跨域预览
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    } else {
      // 对于其他文件类型，也设置为inline
      res.setHeader('Content-Disposition', 'inline');
    }

    // 添加缓存控制
    res.setHeader('Cache-Control', 'public, max-age=3600');

    console.log('预览文件响应头:', {
      contentType: attachment.mime_type,
      contentDisposition: res.getHeader('Content-Disposition')
    });

    // 发送文件
    const fileStream = fs.createReadStream(attachment.path);
    fileStream.pipe(res);
  } catch (error) {
    console.error('预览附件失败:', error);
    res.status(500).json({
      success: false,
      message: '预览附件失败',
      error: error.message
    });
  }
};

/**
 * 删除活动附件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteActivityAttachment = async (req, res) => {
  try {
    const { attachmentId } = req.params;

    // 查询附件
    const attachment = await ActivityAttachment.findByPk(attachmentId, {
      include: [
        {
          model: Activity,
          as: 'activity'
        }
      ]
    });

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: '附件不存在'
      });
    }

    // 删除文件
    if (fs.existsSync(attachment.path)) {
      fs.unlinkSync(attachment.path);
    }

    // 删除附件记录
    await attachment.destroy();

    res.status(200).json({
      success: true,
      message: '附件删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除附件失败',
      error: error.message
    });
  }
};

/**
 * 更新活动状态
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateActivityStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 验证状态值
    if (!status || !['published', 'draft', 'archived'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值，只允许 published、draft 或 archived'
      });
    }

    // 查询活动
    const activity = await Activity.findByPk(id);

    if (!activity) {
      return res.status(404).json({
        success: false,
        message: '活动不存在'
      });
    }

    // 更新活动状态
    await activity.update({
      status,
      last_updated_by: req.user.id
    });

    // 获取更新后的活动信息
    const updatedActivity = await Activity.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'lastUpdatedBy',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: `活动状态已更新为 ${status}`,
      data: updatedActivity
    });
  } catch (error) {
    console.error('更新活动状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新活动状态失败',
      error: error.message
    });
  }
};
