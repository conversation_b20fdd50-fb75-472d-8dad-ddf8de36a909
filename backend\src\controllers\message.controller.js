/**
 * 消息控制器
 *
 * 处理消息相关的HTTP请求，包括创建、查询、更新、删除等操作
 */

const { Message, User } = require('../models');
const { checkPermission } = require('../middlewares/improvedPermissionMiddleware');
const axios = require('axios');
const cheerio = require('cheerio');

/**
 * 获取消息列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getMessages = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      status,
      search,
      author
    } = req.query;

    console.log('【消息列表】收到请求参数:', { page, limit, type, status, search, author });
    console.log('【消息列表】status参数类型:', typeof status, '值:', JSON.stringify(status));
    console.log('【消息列表】用户信息:', req.user ? { id: req.user.id, role: req.user.role, permissions: req.user.permissions } : '未登录');
    console.log('【消息列表】req.query原始对象:', req.query);

    const offset = (page - 1) * limit;
    const where = {};

    // 权限检查：只有有发布权限的用户才能看到草稿和归档消息
    if (!req.user) {
      // 未登录用户只能看到已发布的消息
      where.status = 'published';
    } else {
      // 检查用户是否有发布权限
      const hasPublishPermission = req.user.role === 'admin' ||
        (req.user.permissions && req.user.permissions.includes('content:publish'));

      if (!hasPublishPermission) {
        // 没有发布权限的用户只能看到已发布的消息
        where.status = 'published';
      } else {
        // 有权限的用户，参考活动管理的逻辑
        if (status !== undefined) {
          // 如果明确指定了状态参数
          if (status === '') {
            // 空字符串表示查询所有状态，不设置status条件
            console.log('管理员查询所有状态消息 (status参数为空字符串)');
          } else if (status.includes(',')) {
            // 处理多状态查询（例如：'published,draft'）
            const { Op } = require('sequelize');
            const statusList = status.split(',').map(s => s.trim());
            where.status = {
              [Op.in]: statusList
            };
            console.log('管理员查询多状态消息:', statusList);
          } else {
            // 单一状态查询
            where.status = status;
            console.log('管理员查询指定状态消息:', status);
          }
        } else {
          // 没有指定状态参数，查询所有状态的消息
          console.log('管理员查询所有状态消息 (status参数未提供)');
        }
      }
    }

    // 按类型筛选
    if (type) {
      where.type = type;
    }

    // 按作者筛选
    if (author) {
      where.author = { [require('sequelize').Op.like]: `%${author}%` };
    }

    // 搜索功能
    if (search) {
      const { Op } = require('sequelize');
      where[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { content: { [Op.like]: `%${search}%` } },
        { author: { [Op.like]: `%${search}%` } }
      ];
    }

    console.log('【消息列表】最终查询条件:', JSON.stringify(where, null, 2));
    console.log('【消息列表】查询参数:', { limit: parseInt(limit), offset: parseInt(offset) });
    console.log('【消息列表】开始执行数据库查询...');

    const { count, rows: messages } = await Message.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    console.log('【消息列表】查询结果总数:', count);
    console.log('【消息列表】返回的消息:', messages.map(m => ({ id: m.id, title: m.title, status: m.status, created_at: m.created_at })));
    console.log('【消息列表】数据库查询完成，准备返回响应...');

    // 为每个消息生成摘要（如果还没有的话）
    const messagesWithExcerpt = messages.map(message => {
      const messageData = message.toJSON();
      if (!messageData.excerpt && messageData.content) {
        messageData.excerpt = message.generateExcerpt();
      }
      return messageData;
    });

    res.status(200).json({
      success: true,
      data: {
        messages: messagesWithExcerpt,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取消息列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取消息列表失败',
      error: error.message
    });
  }
};

/**
 * 获取单个消息详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getMessageById = async (req, res) => {
  try {
    const { id } = req.params;

    const message = await Message.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    if (!message) {
      return res.status(404).json({
        success: false,
        message: '消息不存在'
      });
    }

    // 权限检查：只有有发布权限的用户才能访问非已发布的消息
    if (message.status !== 'published') {
      if (!req.user) {
        return res.status(403).json({
          success: false,
          message: '无权访问此消息'
        });
      }

      const hasPublishPermission = req.user.role === 'admin' ||
        (req.user.permissions && req.user.permissions.includes('content:publish'));

      if (!hasPublishPermission) {
        return res.status(403).json({
          success: false,
          message: '无权访问此消息'
        });
      }
    }

    // 增加查看次数（仅对已发布的消息）
    if (message.status === 'published') {
      await message.incrementViewCount();
    }

    const messageData = message.toJSON();
    if (!messageData.excerpt && messageData.content) {
      messageData.excerpt = message.generateExcerpt();
    }

    res.status(200).json({
      success: true,
      data: messageData
    });
  } catch (error) {
    console.error('获取消息详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取消息详情失败',
      error: error.message
    });
  }
};

/**
 * 创建消息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const createMessage = async (req, res) => {
  try {
    const {
      title,
      content,
      author,
      type = 'content',
      external_url,
      cover_image,
      status = 'draft'
    } = req.body;

    console.log('创建消息请求数据:', { title, author, type, status, cover_image });

    // 验证必填字段
    if (!title || !author) {
      return res.status(400).json({
        success: false,
        message: '标题和作者为必填字段'
      });
    }

    // 验证消息类型
    if (type === 'link' && !external_url) {
      return res.status(400).json({
        success: false,
        message: '外部链接类型的消息必须提供URL'
      });
    }

    if (type === 'content' && !content) {
      return res.status(400).json({
        success: false,
        message: '自定义内容类型的消息必须提供内容'
      });
    }

    // 如果是外部链接且没有提供封面图片，尝试自动抓取
    let finalCoverImage = cover_image;
    if (type === 'link' && !cover_image && external_url) {
      try {
        console.log('尝试自动抓取网页图片...');
        finalCoverImage = await extractImageFromUrl(external_url);

        // 如果主要方法失败，尝试备用方法
        if (!finalCoverImage) {
          console.log('主要方法失败，尝试备用图片提取方法...');
          finalCoverImage = await extractImageFromUrlFallback(external_url);
        }

        if (finalCoverImage) {
          console.log('成功提取到图片:', finalCoverImage);

          // 将外部图片下载到本地（生产环境特别重要）
          try {
            console.log('开始下载图片到本地...');
            const localImageUrl = await downloadImageToLocal(finalCoverImage);
            if (localImageUrl) {
              console.log('图片下载成功，使用本地URL:', localImageUrl);
              finalCoverImage = localImageUrl;
            } else {
              console.log('图片下载失败，保持使用外部URL');
            }
          } catch (downloadError) {
            console.warn('下载图片到本地时出错:', downloadError.message);
            // 下载失败时仍然使用外部URL
          }
        } else {
          console.log('所有图片提取方法都失败了');
        }
      } catch (error) {
        console.warn('自动抓取网页图片失败:', error.message);
      }
    }

    // 创建消息
    const message = await Message.create({
      title,
      content: type === 'content' ? content : null,
      author,
      type,
      external_url: type === 'link' ? external_url : (external_url === '' ? null : external_url),
      cover_image: finalCoverImage,
      status,
      created_by: req.user.id
    });

    // 生成摘要
    if (content) {
      const excerpt = message.generateExcerpt();
      await message.update({ excerpt });
    }

    // 获取完整的消息信息
    const fullMessage = await Message.findByPk(message.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '消息创建成功',
      data: fullMessage
    });
  } catch (error) {
    console.error('创建消息失败:', error);
    res.status(500).json({
      success: false,
      message: '创建消息失败',
      error: error.message
    });
  }
};

/**
 * 从URL中提取图片
 * @param {string} url - 网页URL
 * @returns {Promise<string|null>} 图片URL
 */
const extractImageFromUrl = async (url) => {
  try {
    console.log('开始提取图片，URL:', url);

    // 生产环境使用更长的超时时间和更宽松的配置
    const isProduction = process.env.NODE_ENV === 'production';

    const axiosConfig = {
      timeout: isProduction ? 30000 : 15000, // 生产环境使用30秒超时
      maxRedirects: 5,
      validateStatus: function (status) {
        return status >= 200 && status < 400; // 允许重定向
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Connection': 'keep-alive'
      }
    };

    // 生产环境添加额外的配置
    if (isProduction) {
      // 忽略SSL证书错误（仅在必要时使用）
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
      axiosConfig.httpsAgent = new (require('https').Agent)({
        rejectUnauthorized: false
      });
    }

    console.log('发送HTTP请求...');
    const response = await axios.get(url, axiosConfig);

    const $ = cheerio.load(response.data);
    let imageUrl = null;

    // 检查是否是微信公众号文章
    const isWechatArticle = url.includes('mp.weixin.qq.com');

    if (isWechatArticle) {
      console.log('检测到微信公众号文章，使用专门的提取策略');

      // 微信公众号专用提取策略
      // 1. 尝试获取文章封面图
      imageUrl = $('meta[property="og:image"]').attr('content');

      // 2. 尝试获取微信特有的图片标签
      if (!imageUrl) {
        imageUrl = $('meta[name="twitter:image"]').attr('content');
      }

      // 3. 查找文章内容中的第一张图片
      if (!imageUrl) {
        const contentSelectors = [
          '.rich_media_content img',
          '#js_content img',
          '.rich_media_area_primary img',
          'img[data-src]',
          'img[src*="mmbiz"]'
        ];

        for (const selector of contentSelectors) {
          const img = $(selector).first();
          if (img.length) {
            imageUrl = img.attr('data-src') || img.attr('src');
            if (imageUrl) break;
          }
        }
      }

      // 4. 查找任何包含mmbiz的图片（微信图片服务器）
      if (!imageUrl) {
        $('img').each((i, elem) => {
          const src = $(elem).attr('src') || $(elem).attr('data-src');
          if (src && src.includes('mmbiz')) {
            imageUrl = src;
            return false; // 跳出循环
          }
        });
      }
    } else {
      // 通用网站提取策略
      // 1. Open Graph图片
      imageUrl = $('meta[property="og:image"]').attr('content');

      // 2. Twitter卡片图片
      if (!imageUrl) {
        imageUrl = $('meta[name="twitter:image"]').attr('content');
      }

      // 3. 其他meta标签
      if (!imageUrl) {
        const metaSelectors = [
          'meta[property="og:image:url"]',
          'meta[name="image"]',
          'meta[itemprop="image"]'
        ];

        for (const selector of metaSelectors) {
          const content = $(selector).attr('content');
          if (content) {
            imageUrl = content;
            break;
          }
        }
      }

      // 4. 查找有意义的图片
      if (!imageUrl) {
        const imgs = $('img');
        for (let i = 0; i < imgs.length; i++) {
          const img = $(imgs[i]);
          const src = img.attr('src') || img.attr('data-src');
          const alt = img.attr('alt') || '';
          const className = img.attr('class') || '';

          // 跳过明显的装饰性图片
          if (src &&
              !src.includes('icon') &&
              !src.includes('logo') &&
              !src.includes('avatar') &&
              !className.includes('icon') &&
              !className.includes('logo') &&
              !alt.toLowerCase().includes('icon') &&
              !alt.toLowerCase().includes('logo')) {

            // 检查图片尺寸
            const width = img.attr('width') || img.css('width');
            const height = img.attr('height') || img.css('height');

            if (!width || !height ||
                (parseInt(width) > 200 && parseInt(height) > 150)) {
              imageUrl = src;
              break;
            }
          }
        }
      }

      // 5. 最后尝试任何图片
      if (!imageUrl) {
        const firstImg = $('img').first();
        imageUrl = firstImg.attr('src') || firstImg.attr('data-src');
      }
    }

    // 处理相对URL和包含相对路径的绝对URL
    if (imageUrl) {
      try {
        // 使用URL构造函数正确解析相对路径
        const resolvedUrl = new URL(imageUrl, url);
        imageUrl = resolvedUrl.href;
      } catch (error) {
        console.warn('URL解析失败:', error.message);
        // 如果解析失败，保持原始URL
      }
    }

    console.log('提取到的图片URL:', imageUrl);
    return imageUrl || null;
  } catch (error) {
    console.error('提取网页图片失败:', {
      url,
      error: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      isProduction: process.env.NODE_ENV === 'production'
    });

    // 在生产环境中提供更详细的错误信息
    if (process.env.NODE_ENV === 'production') {
      if (error.code === 'ENOTFOUND') {
        console.error('DNS解析失败，可能是网络连接问题');
      } else if (error.code === 'ECONNREFUSED') {
        console.error('连接被拒绝，目标服务器可能不可达');
      } else if (error.code === 'ETIMEDOUT') {
        console.error('请求超时，网络可能较慢');
      } else if (error.response?.status === 403) {
        console.error('访问被禁止，可能需要特殊的请求头或认证');
      } else if (error.response?.status === 404) {
        console.error('页面不存在');
      }
    }

    return null;
  }
};

/**
 * 备用图片提取方法（简化版本，适用于生产环境）
 * @param {string} url - 网页URL
 * @returns {Promise<string|null>} 图片URL
 */
const extractImageFromUrlFallback = async (url) => {
  try {
    console.log('使用备用方法提取图片，URL:', url);

    // 使用更简单的配置
    const response = await axios.get(url, {
      timeout: 10000,
      maxRedirects: 3,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ImageExtractor/1.0)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      // 生产环境忽略SSL错误
      httpsAgent: process.env.NODE_ENV === 'production' ?
        new (require('https').Agent)({ rejectUnauthorized: false }) : undefined
    });

    const $ = cheerio.load(response.data);
    let imageUrl = null;

    // 简化的图片提取策略
    // 1. 优先查找 Open Graph 图片
    imageUrl = $('meta[property="og:image"]').attr('content');

    // 2. 查找 Twitter 卡片图片
    if (!imageUrl) {
      imageUrl = $('meta[name="twitter:image"]').attr('content');
    }

    // 3. 查找第一张有意义的图片
    if (!imageUrl) {
      const imgs = $('img');
      for (let i = 0; i < Math.min(imgs.length, 10); i++) { // 只检查前10张图片
        const img = $(imgs[i]);
        const src = img.attr('src') || img.attr('data-src');

        if (src &&
            !src.includes('icon') &&
            !src.includes('logo') &&
            !src.includes('avatar') &&
            src.length > 10) { // 基本的URL长度检查
          imageUrl = src;
          break;
        }
      }
    }

    // 处理相对URL
    if (imageUrl && !imageUrl.startsWith('http')) {
      try {
        const resolvedUrl = new URL(imageUrl, url);
        imageUrl = resolvedUrl.href;
      } catch (error) {
        console.warn('备用方法URL解析失败:', error.message);
        imageUrl = null;
      }
    }

    console.log('备用方法提取结果:', imageUrl);
    return imageUrl || null;
  } catch (error) {
    console.error('备用图片提取方法也失败了:', {
      url,
      error: error.message,
      code: error.code
    });
    return null;
  }
};

/**
 * 更新消息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const updateMessage = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      author,
      type,
      external_url,
      cover_image,
      status
    } = req.body;

    const message = await Message.findByPk(id);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: '消息不存在'
      });
    }

    // 检查权限：只有创建者或管理员可以编辑
    if (message.created_by !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权编辑此消息'
      });
    }

    // 验证必填字段
    if (title !== undefined && !title) {
      return res.status(400).json({
        success: false,
        message: '标题不能为空'
      });
    }

    if (author !== undefined && !author) {
      return res.status(400).json({
        success: false,
        message: '作者不能为空'
      });
    }

    // 准备更新数据
    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (author !== undefined) updateData.author = author;
    if (type !== undefined) updateData.type = type;
    if (external_url !== undefined) {
      // 将空字符串转换为null，避免URL验证错误
      updateData.external_url = external_url === '' ? null : external_url;
    }
    if (cover_image !== undefined) updateData.cover_image = cover_image;
    if (status !== undefined) updateData.status = status;

    // 如果更新了内容，重新生成摘要
    if (content !== undefined) {
      // 基于新内容生成摘要
      if (content) {
        // 移除HTML标签
        const plainText = content.replace(/<[^>]*>/g, '');
        // 截取前100字符作为摘要
        const newExcerpt = plainText.length <= 100 ? plainText : plainText.substring(0, 100) + '...';
        updateData.excerpt = newExcerpt;

      } else {
        updateData.excerpt = null;

      }
    }

    // 更新消息
    await message.update(updateData);

    // 获取更新后的完整消息信息
    const updatedMessage = await Message.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: '消息更新成功',
      data: updatedMessage
    });
  } catch (error) {
    console.error('更新消息失败:', error);
    res.status(500).json({
      success: false,
      message: '更新消息失败',
      error: error.message
    });
  }
};

/**
 * 删除消息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const deleteMessage = async (req, res) => {
  try {
    const { id } = req.params;

    const message = await Message.findByPk(id);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: '消息不存在'
      });
    }

    // 检查权限：只有创建者或管理员可以删除
    if (message.created_by !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权删除此消息'
      });
    }

    await message.destroy();

    res.status(200).json({
      success: true,
      message: '消息删除成功'
    });
  } catch (error) {
    console.error('删除消息失败:', error);
    res.status(500).json({
      success: false,
      message: '删除消息失败',
      error: error.message
    });
  }
};

/**
 * 发布消息（将状态改为published）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const publishMessage = async (req, res) => {
  try {
    const { id } = req.params;

    const message = await Message.findByPk(id);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: '消息不存在'
      });
    }

    // 检查权限：只有创建者或管理员可以发布
    if (message.created_by !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权发布此消息'
      });
    }

    await message.update({ status: 'published' });

    res.status(200).json({
      success: true,
      message: '消息发布成功',
      data: message
    });
  } catch (error) {
    console.error('发布消息失败:', error);
    res.status(500).json({
      success: false,
      message: '发布消息失败',
      error: error.message
    });
  }
};

/**
 * 归档消息（将状态改为archived）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const archiveMessage = async (req, res) => {
  try {
    const { id } = req.params;

    const message = await Message.findByPk(id);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: '消息不存在'
      });
    }

    // 检查权限：只有创建者或管理员可以归档
    if (message.created_by !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权归档此消息'
      });
    }

    await message.update({ status: 'archived' });

    res.status(200).json({
      success: true,
      message: '消息归档成功',
      data: message
    });
  } catch (error) {
    console.error('归档消息失败:', error);
    res.status(500).json({
      success: false,
      message: '归档消息失败',
      error: error.message
    });
  }
};

/**
 * 下架消息（将已发布改为草稿）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const unpublishMessage = async (req, res) => {
  try {
    const { id } = req.params;

    const message = await Message.findByPk(id);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: '消息不存在'
      });
    }

    // 检查权限：只有创建者或管理员可以下架
    if (message.created_by !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权下架此消息'
      });
    }

    // 只有已发布的消息才能下架
    if (message.status !== 'published') {
      return res.status(400).json({
        success: false,
        message: '只有已发布的消息才能下架'
      });
    }

    await message.update({ status: 'draft' });

    res.status(200).json({
      success: true,
      message: '消息下架成功',
      data: message
    });
  } catch (error) {
    console.error('下架消息失败:', error);
    res.status(500).json({
      success: false,
      message: '下架消息失败',
      error: error.message
    });
  }
};

/**
 * 下载外部图片到本地
 * @param {string} imageUrl - 外部图片URL
 * @returns {Promise<string|null>} 本地图片URL
 */
const downloadImageToLocal = async (imageUrl) => {
  try {
    const fs = require('fs');
    const path = require('path');

    console.log('开始下载图片到本地:', imageUrl);

    // 确保目录存在
    const uploadDir = path.join(__dirname, '../../uploads/images');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('创建上传目录:', uploadDir);
    }

    // 生产环境使用更宽松的配置
    const isProduction = process.env.NODE_ENV === 'production';
    const axiosConfig = {
      responseType: 'stream',
      timeout: isProduction ? 60000 : 30000, // 生产环境使用更长超时
      maxRedirects: 5,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
      }
    };

    // 根据图片来源设置特殊的请求头
    if (imageUrl.includes('mp.weixin.qq.com') || imageUrl.includes('mmbiz')) {
      axiosConfig.headers['Referer'] = 'https://mp.weixin.qq.com/';
    }

    // 生产环境添加SSL配置
    if (isProduction) {
      axiosConfig.httpsAgent = new (require('https').Agent)({
        rejectUnauthorized: false
      });
    }

    console.log('发送图片下载请求...');
    const response = await axios.get(imageUrl, axiosConfig);

    // 检查响应类型
    const contentType = response.headers['content-type'];
    if (!contentType || !contentType.startsWith('image/')) {
      console.warn('响应不是图片类型:', contentType);
      return null;
    }

    // 生成文件名
    let ext = path.extname(new URL(imageUrl).pathname);
    if (!ext || ext.length > 5) {
      // 根据content-type确定扩展名
      if (contentType.includes('jpeg') || contentType.includes('jpg')) {
        ext = '.jpg';
      } else if (contentType.includes('png')) {
        ext = '.png';
      } else if (contentType.includes('gif')) {
        ext = '.gif';
      } else if (contentType.includes('webp')) {
        ext = '.webp';
      } else {
        ext = '.jpg'; // 默认
      }
    }

    const filename = `external-${Date.now()}-${Math.round(Math.random() * 1E9)}${ext}`;
    const filePath = path.join(uploadDir, filename);

    console.log('保存图片到:', filePath);

    // 保存文件
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      let timeout;

      // 设置超时
      if (isProduction) {
        timeout = setTimeout(() => {
          writer.destroy();
          reject(new Error('图片下载超时'));
        }, 60000);
      }

      writer.on('finish', () => {
        if (timeout) clearTimeout(timeout);

        // 检查文件大小
        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
          fs.unlinkSync(filePath); // 删除空文件
          reject(new Error('下载的文件为空'));
          return;
        }

        // 返回本地URL
        const localUrl = `/uploads/images/${filename}`;
        console.log('图片下载成功:', {
          originalUrl: imageUrl,
          localUrl,
          fileSize: stats.size,
          contentType
        });
        resolve(localUrl);
      });

      writer.on('error', (error) => {
        if (timeout) clearTimeout(timeout);
        console.error('写入文件失败:', error.message);

        // 清理失败的文件
        try {
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        } catch (cleanupError) {
          console.warn('清理失败文件时出错:', cleanupError.message);
        }

        reject(error);
      });
    });
  } catch (error) {
    console.error('下载图片到本地失败:', {
      imageUrl,
      error: error.message,
      code: error.code,
      status: error.response?.status
    });
    return null;
  }
};

/**
 * 获取网页内容的API端点（用于代理访问）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const getWebPageContent = async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        message: '请提供URL'
      });
    }

    console.log('代理获取网页内容:', url);

    // 检测是否是微信公众号
    const isWechatArticle = url.includes('mp.weixin.qq.com');

    // 对微信公众号使用特殊的请求策略
    let response;
    if (isWechatArticle) {
      console.log('尝试获取微信公众号内容，URL:', url);

      // 尝试多种策略获取内容
      const strategies = [
        // 策略1: 模拟微信内置浏览器
        {
          name: '微信内置浏览器',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 12; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/120.0.0.0 Mobile Safari/537.36 MicroMessenger/8.0.47.2560(0x28002F30) Process/tools WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://mp.weixin.qq.com/',
            'X-Requested-With': 'com.tencent.mm',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Upgrade-Insecure-Requests': '1'
          }
        },
        // 策略2: 模拟桌面微信
        {
          name: '桌面微信',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 MicroMessenger/*********',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://mp.weixin.qq.com/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Upgrade-Insecure-Requests': '1'
          }
        },
        // 策略3: 标准浏览器（备用）
        {
          name: '标准浏览器',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://mp.weixin.qq.com/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Upgrade-Insecure-Requests': '1'
          }
        }
      ];

      let lastError;
      for (const strategy of strategies) {
        try {
          console.log(`尝试策略: ${strategy.name}`);
          response = await axios.get(url, {
            timeout: 30000,
            maxRedirects: 5,
            headers: strategy.headers,
            validateStatus: function (status) {
              return status >= 200 && status < 400;
            }
          });

          // 检查是否获取到有效内容
          if (response.data && response.data.length > 1000 &&
              !response.data.includes('微信扫一扫可打开此内容') &&
              !response.data.includes('jump_wx_qrcode')) {
            console.log(`策略 ${strategy.name} 成功，内容长度:`, response.data.length);
            break;
          } else {
            console.log(`策略 ${strategy.name} 返回跳转页面，尝试下一个策略`);
            continue;
          }
        } catch (error) {
          console.log(`策略 ${strategy.name} 失败:`, error.message);
          lastError = error;
          continue;
        }
      }

      // 如果所有策略都失败，抛出最后一个错误
      if (!response) {
        throw lastError || new Error('所有获取策略都失败');
      }
    } else {
      // 其他网站使用标准请求
      response = await axios.get(url, {
        timeout: 60000,
        maxRedirects: 5,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
          'Upgrade-Insecure-Requests': '1',
          'Connection': 'keep-alive'
        }
      });
    }

    let content = response.data;

    // 对微信公众号内容进行特殊处理
    if (isWechatArticle) {
      console.log('处理微信公众号内容，原始长度:', content.length);

      // 检查是否包含微信的跳转提示，但仍然尝试显示内容
      const hasJumpPrompt = content.includes('微信扫一扫可打开此内容') ||
                           content.includes('使用完整服务') ||
                           content.includes('jump_wx_qrcode') ||
                           content.includes('微信已打开并开始内容');

      if (hasJumpPrompt) {
        console.log('检测到微信跳转页面，尝试替换为可显示的内容...');

        // 如果是跳转页面，尝试替换为一个基本的显示页面
        content = content.replace(/微信扫一扫可打开此内容[\s\S]*?使用完整服务/gi, '');
        content = content.replace(/jump_wx_qrcode/gi, '');

        // 如果内容太少，创建一个基本的显示框架
        if (content.length < 5000) {
          content = `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>微信文章内容</title>
            </head>
            <body>
              <div style="padding: 20px; font-family: Arial, sans-serif; line-height: 1.6;">
                <h2>微信公众号文章</h2>
                <p>正在尝试加载文章内容...</p>
                <div id="content-area">
                  ${content}
                </div>
                <p><a href="${url}" target="_blank" style="color: #07c160;">点击查看原文</a></p>
              </div>
            </body>
            </html>
          `;
        }
      }

      // 无论是否有跳转提示，都尝试处理和显示内容
      // 移除可能阻止显示的脚本，但保留内容相关的脚本
      content = content.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, (match) => {
        // 保留包含内容数据的脚本
        if (match.includes('rich_media_content') || match.includes('msg_content')) {
          return match;
        }
        return '';
      });

      // 修复相对路径的资源
      content = content.replace(/src="\/\//g, 'src="https://');
      content = content.replace(/href="\/\//g, 'href="https://');
      content = content.replace(/url\(\/\//g, 'url(https://');

      // 强制显示隐藏的内容 - 特别处理微信的隐藏样式
      content = content.replace(/style="visibility:\s*hidden;\s*opacity:\s*0;[^"]*"/gi, 'style="visibility: visible !important; opacity: 1 !important;"');
      content = content.replace(/style="[^"]*visibility:\s*hidden[^"]*"/gi, (match) => {
        return match.replace(/visibility:\s*hidden/gi, 'visibility: visible !important');
      });
      content = content.replace(/style="[^"]*opacity:\s*0[^"]*"/gi, (match) => {
        return match.replace(/opacity:\s*0/gi, 'opacity: 1 !important');
      });
      content = content.replace(/style="[^"]*display:\s*none[^"]*"/gi, (match) => {
        return match.replace(/display:\s*none/gi, 'display: block !important');
      });

      // 添加优化的显示样式
      const forceShowStyle = `
        <style>
          /* 基础样式重置 */
          body {
            margin: 0 !important;
            padding: 15px !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
            background: white !important;
            line-height: 1.6 !important;
            color: #333 !important;
            overflow-x: hidden !important;
          }

          /* 主要内容区域 */
          .rich_media_content, #js_content {
            visibility: visible !important;
            opacity: 1 !important;
            display: block !important;
            max-width: 100% !important;
            width: 100% !important;
            overflow: visible !important;
            position: relative !important;
          }

          .rich_media_area_primary {
            visibility: visible !important;
            opacity: 1 !important;
            display: block !important;
            width: 100% !important;
            max-width: 100% !important;
          }

          .rich_media_wrp {
            width: 100% !important;
            max-width: 100% !important;
            overflow: visible !important;
          }

          /* 文章标题 */
          .rich_media_title {
            visibility: visible !important;
            opacity: 1 !important;
            display: block !important;
            font-size: 22px !important;
            font-weight: bold !important;
            line-height: 1.4 !important;
            margin: 20px 0 !important;
            color: #333 !important;
          }

          /* 文章内容 */
          .rich_media_content p,
          .rich_media_content div,
          .rich_media_content section {
            visibility: visible !important;
            opacity: 1 !important;
            display: block !important;
            line-height: 1.8 !important;
            margin: 15px 0 !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
          }

          /* 图片样式 */
          img {
            max-width: 100% !important;
            height: auto !important;
            visibility: visible !important;
            display: block !important;
            margin: 10px auto !important;
          }

          /* 隐藏不必要的元素 */
          .rich_media_tool, .bottom_bar_wrp, .wx_follow_context,
          .interaction_bar__wrap, #js_pc_qr_code, .qr_code_pc_outer,
          .jump_wx_qrcode_dialog, #js_jump_wx_qrcode_dialog,
          .wx_expand_article, .stream_bottom_bar_wrp,
          .weui-dialog__wrp, .weui-mask, .weui-dialog,
          .preview_tips, .preview-tips, .tag_tips, .tag-tips,
          [class*="preview"], [class*="tag_tip"], [class*="tag-tip"],
          .rich_media_meta_tips, .appmsg_tag_tips,
          .tips, .notice, .warning, .alert,
          #js_tags_preview_toast, .article-tag__error-tips,
          .analyze_btn_wrap, #js_analyze_btn, .go-button, .close-button,
          .wx_tap_card, .js_wx_tap_highlight {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            width: 0 !important;
            overflow: hidden !important;
          }

          /* 修复可能的布局问题 */
          * {
            box-sizing: border-box !important;
          }

          /* 确保文本正常显示 */
          span, p, div, section, article {
            white-space: normal !important;
            word-break: break-word !important;
            overflow: visible !important;
            visibility: visible !important;
            opacity: 1 !important;
          }

          /* 修复固定定位的元素 */
          .rich_media_content *[style*="position: fixed"],
          .rich_media_content *[style*="position: absolute"] {
            position: static !important;
          }

          /* 只针对特定的提示元素进行隐藏，不影响整体滚动 */
          #js_tags_preview_toast,
          .article-tag__error-tips,
          [class*="preview_tips"],
          [class*="tag_tips"],
          [id*="preview"],
          [id*="tag_tip"],
          .analyze_btn_wrap,
          #js_analyze_btn,
          .go-button,
          .close-button,
          .wx_tap_card,
          .js_wx_tap_highlight {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            width: 0 !important;
            overflow: hidden !important;
          }

          /* 确保页面可以正常滚动 */
          html, body {
            overflow: auto !important;
            height: auto !important;
            max-height: none !important;
          }

          /* 重置所有可能影响滚动的样式 */
          * {
            overflow: visible !important;
            height: auto !important;
            max-height: none !important;
            min-height: auto !important;
            position: static !important;
          }

          /* 特别处理微信内容区域 */
          .rich_media_content,
          .rich_media_area_primary,
          #js_content,
          .rich_media_wrp,
          .rich_media_inner {
            overflow: visible !important;
            height: auto !important;
            max-height: none !important;
            min-height: auto !important;
            position: static !important;
            display: block !important;
          }

          /* 确保body和html不限制高度 */
          html, body {
            overflow: visible !important;
            height: auto !important;
            max-height: none !important;
            min-height: auto !important;
            position: static !important;
          }

          /* 移除所有固定定位和绝对定位 */
          [style*="position: fixed"],
          [style*="position: absolute"],
          [style*="position:fixed"],
          [style*="position:absolute"] {
            position: static !important;
          }

          /* 移除所有高度限制 */
          [style*="height"],
          [style*="max-height"],
          [style*="min-height"] {
            height: auto !important;
            max-height: none !important;
            min-height: auto !important;
          }
        </style>
        <script>
          // 移除包含"预览时标签不可点"的元素
          function removePreviewTips() {
            // 直接移除已知的提示元素和分析按钮
            const tipSelectors = [
              '#js_tags_preview_toast',
              '.article-tag__error-tips',
              '[class*="preview_tips"]',
              '[class*="tag_tips"]',
              '[id*="preview"]',
              '[id*="tag_tip"]',
              '.analyze_btn_wrap',
              '#js_analyze_btn',
              '.go-button',
              '.close-button',
              '.wx_tap_card',
              '.js_wx_tap_highlight'
            ];

            tipSelectors.forEach(function(selector) {
              const elements = document.querySelectorAll(selector);
              elements.forEach(function(el) {
                el.style.display = 'none !important';
                el.remove();
              });
            });

            // 查找包含特定文字的元素
            const allElements = document.querySelectorAll('*');
            allElements.forEach(function(element) {
              if (element.textContent && element.textContent.trim() === '预览时标签不可点') {
                element.style.display = 'none !important';
                element.remove();
              }
            });

            // 移除包含提示文字的父元素
            const textNodes = document.evaluate(
              "//text()[contains(., '预览时标签不可点')]",
              document,
              null,
              XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,
              null
            );

            for (let i = 0; i < textNodes.snapshotLength; i++) {
              const textNode = textNodes.snapshotItem(i);
              if (textNode && textNode.parentElement) {
                textNode.parentElement.style.display = 'none !important';
                textNode.parentElement.remove();
              }
            }
          }

          // 修复滚动问题的函数
          function fixScrolling() {
            try {
              // 只修复微信文章的主要容器
              const contentSelectors = [
                '.rich_media_content',
                '.rich_media_area_primary',
                '#js_content',
                '.rich_media_wrp',
                '.rich_media_inner'
              ];

              contentSelectors.forEach(function(selector) {
                const elements = document.querySelectorAll(selector);
                elements.forEach(function(element) {
                  if (element.style) {
                    element.style.overflow = 'visible';
                    element.style.height = 'auto';
                    element.style.maxHeight = 'none';
                  }
                });
              });

              // 确保body和html可以滚动
              if (document.documentElement && document.documentElement.style) {
                document.documentElement.style.overflow = 'auto';
                document.documentElement.style.height = 'auto';
              }

              if (document.body && document.body.style) {
                document.body.style.overflow = 'auto';
                document.body.style.height = 'auto';
                document.body.style.position = 'static';
              }
            } catch (e) {
              console.log('fixScrolling error:', e);
            }
          }

          // 立即执行
          removePreviewTips();
          fixScrolling();

          // DOM加载完成后执行
          document.addEventListener('DOMContentLoaded', function() {
            removePreviewTips();
            fixScrolling();
            // 延迟执行，确保动态内容也被处理
            setTimeout(function() {
              removePreviewTips();
              fixScrolling();
            }, 500);
            setTimeout(function() {
              removePreviewTips();
              fixScrolling();
            }, 1000);
            setTimeout(function() {
              removePreviewTips();
              fixScrolling();
            }, 2000);
          });

          // 监听DOM变化
          if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
              let shouldCheck = false;
              mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                  shouldCheck = true;
                }
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
                  shouldCheck = true;
                }
              });
              if (shouldCheck) {
                setTimeout(function() {
                  removePreviewTips();
                  fixScrolling();
                }, 100);
              }
            });

            observer.observe(document.body, {
              childList: true,
              subtree: true,
              attributes: true,
              attributeFilter: ['style', 'class']
            });
          }
        </script>
      `;

      if (content.includes('</head>')) {
        content = content.replace('</head>', forceShowStyle + '</head>');
      } else {
        content = forceShowStyle + content;
      }

      console.log('处理后内容长度:', content.length);
      console.log('是否为微信文章:', isWechatArticle);
      console.log('URL:', url);
    }

    res.status(200).json({
      success: true,
      data: {
        content: content,
        url: url,
        isWechat: isWechatArticle
      }
    });

  } catch (error) {
    console.error('获取网页内容失败:', error.message);
    res.status(500).json({
      success: false,
      message: '获取网页内容失败',
      error: error.message
    });
  }
};

/**
 * 提取URL图片的API端点
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const extractImageFromUrlEndpoint = async (req, res) => {
  try {
    const { url } = req.body;

    console.log('开始提取图片，URL:', url);

    if (!url) {
      console.log('提取图片失败: 未提供URL');
      return res.status(400).json({
        success: false,
        message: '请提供URL'
      });
    }

    // 验证URL格式
    try {
      new URL(url);
    } catch (urlError) {
      console.log('提取图片失败: URL格式无效', url);
      return res.status(400).json({
        success: false,
        message: 'URL格式无效'
      });
    }

    // 先提取外部图片URL
    const externalImageUrl = await extractImageFromUrl(url);

    if (!externalImageUrl) {
      console.log('提取图片结果: 未找到合适的图片', url);
      return res.status(200).json({
        success: false,
        message: '该网页没有找到合适的图片，可能是纯文本页面或图片被保护',
        data: {
          imageUrl: null,
          originalUrl: null,
          isLocal: false
        }
      });
    }

    console.log('找到外部图片URL:', externalImageUrl);

    // 尝试下载图片到本地
    const localImageUrl = await downloadImageToLocal(externalImageUrl);

    let finalImageUrl = externalImageUrl;
    let isLocal = false;

    if (localImageUrl) {
      // 构建完整的本地URL
      // 在生产环境中使用正确的域名
      const isProduction = process.env.NODE_ENV === 'production';
      if (isProduction) {
        // 生产环境使用固定域名
        finalImageUrl = `http://hefuf.com${localImageUrl}`;
      } else {
        // 开发环境使用请求的host
        finalImageUrl = `${req.protocol}://${req.get('host')}${localImageUrl}`;
      }
      isLocal = true;
      console.log('图片下载成功，使用本地URL:', finalImageUrl);
    } else {
      console.log('图片下载失败，使用原始URL:', externalImageUrl);
    }

    console.log('图片提取成功:', {
      originalUrl: externalImageUrl,
      finalImageUrl: finalImageUrl,
      isLocal: isLocal,
      environment: process.env.NODE_ENV
    });

    res.status(200).json({
      success: true,
      message: '图片提取成功',
      data: {
        imageUrl: finalImageUrl,
        originalUrl: externalImageUrl,
        isLocal: isLocal
      }
    });
  } catch (error) {
    console.error('提取图片异常:', {
      url: req.body.url,
      error: error.message,
      stack: error.stack
    });

    // 根据错误类型返回不同的错误信息
    let errorMessage = '提取图片失败';
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorMessage = '无法访问该网页，请检查URL是否正确';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = '网页访问超时，请稍后重试';
    } else if (error.message.includes('timeout')) {
      errorMessage = '网页响应超时，请稍后重试';
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getMessages,
  getMessageById,
  createMessage,
  updateMessage,
  deleteMessage,
  publishMessage,
  archiveMessage,
  unpublishMessage,
  extractImageFromUrl,
  extractImageFromUrlEndpoint,
  getWebPageContent
};
