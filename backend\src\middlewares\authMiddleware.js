/**
 * 认证中间件
 *
 * 验证用户JWT令牌，确保API路由的安全访问
 */

const jwt = require('jsonwebtoken');
const { User, Role, Permission } = require('../models');

/**
 * 验证用户是否已认证
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
/**
 * 认证中间件函数
 * 验证用户JWT令牌，并将用户信息添加到请求对象
 */
const authMiddleware = async (req, res, next) => {
  try {
    // 获取请求头中的Authorization
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '未授权'
      });
    }

    // 提取令牌
    const token = authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'dev_jwt_secret_key');

    // 查找用户
    const user = await User.findByPk(decoded.id, {
      include: [
        {
          model: Role,
          as: 'userRole',
          attributes: ['id', 'name'],
          include: [
            {
              model: Permission,
              as: 'permissions',
              attributes: ['id', 'code', 'name']
            }
          ]
        }
      ]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在或令牌无效'
      });
    }

    // 检查用户是否被禁用
    if (!user.is_active) {
      return res.status(403).json({
        success: false,
        message: '用户账号已被禁用'
      });
    }

    // 提取权限信息
    const permissions = user.userRole ? user.userRole.permissions.map(p => p.code) : [];

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      // 优先使用数据库中的role字段，如果为admin则直接使用，否则根据角色名称判断
      role: user.role === 'admin' ? 'admin' : (user.userRole ? user.userRole.name === '管理员' ? 'admin' : 'basic_user' : 'basic_user'),
      role_id: user.role_id,
      permissions: permissions
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的令牌'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '令牌已过期'
      });
    }

    console.error('认证中间件错误:', error);
    res.status(500).json({
      success: false,
      message: '认证失败',
      error: error.message
    });
  }
};

/**
 * 检查特定权限的中间件
 * @param {String|Array} permissionCode - 权限代码或权限代码数组（需要满足任一权限）
 * @param {Object} options - 配置选项
 * @param {Boolean} options.requireAll - 是否需要满足所有权限（默认为false，即满足任一权限即可）
 * @returns {Function} 中间件函数
 */
const checkPermission = (permissionCode, options = {}) => {
  return (req, res, next) => {
    // 如果用户是管理员，直接通过
    if (req.user.role === 'admin') {
      return next();
    }

    // 如果未提供权限代码，拒绝访问
    if (!permissionCode) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 将单个权限代码转换为数组
    const permissionCodes = Array.isArray(permissionCode) ? permissionCode : [permissionCode];

    // 如果需要满足所有权限
    if (options.requireAll) {
      const hasAllPermissions = permissionCodes.every(code =>
        req.user.permissions.includes(code)
      );

      if (hasAllPermissions) {
        return next();
      }
    } else {
      // 默认情况：满足任一权限即可
      const hasAnyPermission = permissionCodes.some(code =>
        req.user.permissions.includes(code)
      );

      if (hasAnyPermission) {
        return next();
      }
    }

    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  };
};

/**
 * 检查用户是否有权限访问资源
 * 用于控制器内部的权限检查
 *
 * @param {Object} user - 用户对象
 * @param {String|Array} permissionCode - 权限代码或权限代码数组
 * @param {Object} options - 配置选项
 * @param {Boolean} options.requireAll - 是否需要满足所有权限
 * @param {Boolean} options.allowOwner - 是否允许资源所有者访问
 * @param {Number|String} options.ownerId - 资源所有者ID
 * @returns {Boolean} 是否有权限
 */
const hasPermission = (user, permissionCode, options = {}) => {
  // 如果用户是管理员，直接通过
  if (user.role === 'admin') {
    return true;
  }

  // 如果允许资源所有者访问，且用户是资源所有者
  if (options.allowOwner && options.ownerId && user.id === parseInt(options.ownerId)) {
    return true;
  }

  // 如果未提供权限代码，拒绝访问
  if (!permissionCode) {
    return false;
  }

  // 将单个权限代码转换为数组
  const permissionCodes = Array.isArray(permissionCode) ? permissionCode : [permissionCode];

  // 如果需要满足所有权限
  if (options.requireAll) {
    return permissionCodes.every(code => user.permissions.includes(code));
  } else {
    // 默认情况：满足任一权限即可
    return permissionCodes.some(code => user.permissions.includes(code));
  }
};

/**
 * 可选认证中间件
 * 如果有token则解析用户信息，没有token也不会报错
 */
const optionalAuthMiddleware = async (req, res, next) => {
  try {
    // 获取请求头中的Authorization
    const authHeader = req.headers.authorization;

    // 如果没有认证头，直接继续，不设置用户信息
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    // 提取令牌
    const token = authHeader.split(' ')[1];

    if (!token) {
      return next();
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'dev_jwt_secret_key');

    // 查找用户
    const user = await User.findByPk(decoded.id, {
      include: [
        {
          model: Role,
          as: 'userRole',
          attributes: ['id', 'name'],
          include: [
            {
              model: Permission,
              as: 'permissions',
              attributes: ['id', 'code', 'name']
            }
          ]
        }
      ]
    });

    if (!user || !user.is_active) {
      // 如果用户不存在或被禁用，不设置用户信息，但继续执行
      return next();
    }

    // 提取权限信息
    const permissions = user.userRole ? user.userRole.permissions.map(p => p.code) : [];

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role === 'admin' ? 'admin' : (user.userRole ? user.userRole.name === '管理员' ? 'admin' : 'basic_user' : 'basic_user'),
      role_id: user.role_id,
      permissions: permissions
    };

    next();
  } catch (error) {
    // 如果token解析失败，不报错，直接继续
    console.warn('可选认证中间件警告:', error.message);
    next();
  }
};

module.exports = authMiddleware;
module.exports.optionalAuthMiddleware = optionalAuthMiddleware;
module.exports.checkPermission = checkPermission;
module.exports.hasPermission = hasPermission;
