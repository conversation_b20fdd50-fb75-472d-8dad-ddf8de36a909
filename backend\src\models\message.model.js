/**
 * 消息模型
 *
 * 定义消息数据结构，包括消息标题、内容、类型等信息
 * @param {Object} sequelize - Sequelize实例
 * @param {Object} DataTypes - Sequelize数据类型
 * @returns {Object} Message模型
 */
module.exports = (sequelize, DataTypes) => {
  const Message = sequelize.define('Message', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      },
      comment: '消息标题'
    },
    content: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
      comment: '消息内容（富文本）'
    },
    excerpt: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '消息摘要（前100字）'
    },
    cover_image: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '首页图片URL'
    },
    author: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      },
      comment: '作者名称'
    },
    type: {
      type: DataTypes.ENUM('content', 'link'),
      allowNull: false,
      defaultValue: 'content',
      comment: '消息类型：content=自定义内容，link=外部链接'
    },
    external_url: {
      type: DataTypes.STRING(500),
      allowNull: true,
      validate: {
        isUrlOrEmpty(value) {
          // 如果值为空字符串、null或undefined，则通过验证
          if (!value || value === '') {
            return;
          }
          // 如果有值，则验证是否为有效URL
          const urlPattern = /^https?:\/\/.+/;
          if (!urlPattern.test(value)) {
            throw new Error('外部链接必须是有效的URL');
          }
        }
      },
      comment: '外部链接URL（仅当type=link时使用）'
    },
    status: {
      type: DataTypes.ENUM('draft', 'published', 'archived'),
      allowNull: false,
      defaultValue: 'draft',
      comment: '状态：draft=草稿，published=已发布，archived=已归档'
    },
    view_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '查看次数'
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '创建者ID'
    }
  }, {
    tableName: 'messages',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['status']
      },
      {
        fields: ['type']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['created_by']
      }
    ]
  });

  // 实例方法
  /**
   * 生成消息摘要
   * @param {number} length - 摘要长度，默认100字
   * @returns {string} 摘要文本
   */
  Message.prototype.generateExcerpt = function(length = 100) {
    if (!this.content) return '';

    // 移除HTML标签
    const plainText = this.content.replace(/<[^>]*>/g, '');

    // 截取指定长度
    if (plainText.length <= length) {
      return plainText;
    }

    return plainText.substring(0, length) + '...';
  };

  /**
   * 检查是否为已发布状态
   * @returns {boolean} 是否已发布
   */
  Message.prototype.isPublished = function() {
    return this.status === 'published';
  };

  /**
   * 检查是否为外部链接类型
   * @returns {boolean} 是否为外部链接
   */
  Message.prototype.isExternalLink = function() {
    return this.type === 'link';
  };

  /**
   * 增加查看次数
   * @returns {Promise} 更新结果
   */
  Message.prototype.incrementViewCount = async function() {
    this.view_count += 1;
    return await this.save();
  };

  // 静态方法
  /**
   * 获取已发布的消息
   * @param {Object} options - 查询选项
   * @returns {Promise} 消息列表
   */
  Message.getPublished = async function(options = {}) {
    const { limit = 10, offset = 0, order = [['created_at', 'DESC']] } = options;

    return await this.findAll({
      where: {
        status: 'published'
      },
      limit,
      offset,
      order,
      include: [
        {
          model: sequelize.models.User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });
  };

  /**
   * 根据类型获取消息
   * @param {string} type - 消息类型
   * @param {Object} options - 查询选项
   * @returns {Promise} 消息列表
   */
  Message.getByType = async function(type, options = {}) {
    const { limit = 10, offset = 0, order = [['created_at', 'DESC']] } = options;

    return await this.findAll({
      where: {
        type,
        status: 'published'
      },
      limit,
      offset,
      order,
      include: [
        {
          model: sequelize.models.User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });
  };

  /**
   * 搜索消息
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 查询选项
   * @returns {Promise} 消息列表
   */
  Message.search = async function(keyword, options = {}) {
    const { limit = 10, offset = 0, order = [['created_at', 'DESC']] } = options;
    const { Op } = require('sequelize');

    return await this.findAll({
      where: {
        status: 'published',
        [Op.or]: [
          {
            title: {
              [Op.like]: `%${keyword}%`
            }
          },
          {
            content: {
              [Op.like]: `%${keyword}%`
            }
          },
          {
            author: {
              [Op.like]: `%${keyword}%`
            }
          }
        ]
      },
      limit,
      offset,
      order,
      include: [
        {
          model: sequelize.models.User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });
  };

  // 关联关系
  Message.associate = (models) => {
    // 消息与用户的多对一关系（创建者）
    Message.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });
  };

  return Message;
};
