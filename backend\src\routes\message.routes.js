/**
 * 消息相关路由
 *
 * 处理消息的创建、查询、更新、删除等请求
 */

const express = require('express');
const router = express.Router();
const messageController = require('../controllers/message.controller');
const authMiddleware = require('../middlewares/authMiddleware');
const { optionalAuthMiddleware } = require('../middlewares/authMiddleware');
const { checkPermission } = require('../middlewares/improvedPermissionMiddleware');

// 获取消息列表 (支持可选认证，根据用户权限返回不同内容)
router.get('/', optionalAuthMiddleware, messageController.getMessages);

// 获取消息详情 (公开接口，所有人可访问)
router.get('/:id', messageController.getMessageById);

// 创建消息 (需要登录和发布权限)
router.post('/',
  authMiddleware,
  checkPermission('content:publish'),
  messageController.createMessage
);

// 更新消息 (需要登录和发布权限)
router.put('/:id',
  authMiddleware,
  checkPermission('content:publish'),
  messageController.updateMessage
);

// 删除消息 (需要登录和管理权限)
router.delete('/:id',
  authMiddleware,
  checkPermission(['content:publish', 'content:manage'], { requireAll: false }),
  messageController.deleteMessage
);

// 发布消息 (需要登录和发布权限)
router.patch('/:id/publish',
  authMiddleware,
  checkPermission('content:publish'),
  messageController.publishMessage
);

// 归档消息 (需要登录和发布权限)
router.patch('/:id/archive',
  authMiddleware,
  checkPermission('content:publish'),
  messageController.archiveMessage
);

// 下架消息 (需要登录和发布权限)
router.patch('/:id/unpublish',
  authMiddleware,
  checkPermission('content:publish'),
  messageController.unpublishMessage
);

// 从URL提取图片 (需要登录和发布权限)
router.post('/extract-image',
  authMiddleware,
  checkPermission('content:publish'),
  messageController.extractImageFromUrlEndpoint
);

// 获取网页内容 (代理访问，需要登录)
router.post('/get-webpage-content',
  authMiddleware,
  messageController.getWebPageContent
);

module.exports = router;
