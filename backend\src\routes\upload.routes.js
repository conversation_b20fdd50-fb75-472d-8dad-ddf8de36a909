/**
 * 上传相关路由
 *
 * 处理文件上传的请求
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const authMiddleware = require('../middlewares/authMiddleware');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../public/uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 确保头像目录存在
const avatarDir = path.join(uploadDir, 'avatars');
if (!fs.existsSync(avatarDir)) {
  fs.mkdirSync(avatarDir, { recursive: true });
}

// 确保其他图片目录存在
const imagesDir = path.join(uploadDir, 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// 确保封面图片目录存在
const coverDir = '/soft/hefu/images';
if (!fs.existsSync(coverDir)) {
  fs.mkdirSync(coverDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 根据上传类型选择不同的目录
    const type = req.body.type || 'other';
    let destDir = imagesDir;

    console.log('=== 上传目录选择调试 ===');
    console.log('上传类型:', type);
    console.log('默认目录:', imagesDir);
    console.log('封面目录:', coverDir);

    if (type === 'avatar') {
      destDir = avatarDir;
      console.log('选择头像目录:', destDir);
    } else if (type === 'cover') {
      // 封面图片保存到 /soft/hefu/images
      destDir = coverDir;
      console.log('选择封面目录:', destDir);
    } else {
      console.log('选择默认目录:', destDir);
    }

    console.log('最终目录:', destDir);
    console.log('=== 调试结束 ===');

    cb(null, destDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'file-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的图片类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/svg+xml'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，只允许上传JPG、PNG、GIF和SVG图片'), false);
  }
};

// 配置multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB - 增加文件大小限制以支持更大的图片文件
  }
});

// 上传图片
router.post('/image',
  authMiddleware,
  upload.single('file'),
  (req, res) => {
    try {
      // 检查文件是否上传
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '未上传图片'
        });
      }

      // 处理文件路径，根据类型生成相对路径
      const filename = path.basename(req.file.path);
      const type = req.body.type || 'other';
      let relativePath;

      if (type === 'cover') {
        // 封面图片使用 /images/ 路径
        relativePath = `images/${filename}`;
      } else if (type === 'avatar') {
        relativePath = `images/avatars/${filename}`;
      } else {
        // 其他类型保持原有逻辑
        relativePath = req.file.path.replace(/\\/g, '/').replace(/^.*[\/\\]public[\/\\]/, '');
      }

      // 构建URL
      const fileUrl = `${req.protocol}://${req.get('host')}/${relativePath}`;

      console.log('上传图片成功:', {
        originalPath: req.file.path,
        relativePath: relativePath,
        url: fileUrl
      });

      res.status(200).json({
        success: true,
        message: '图片上传成功',
        data: {
          path: relativePath,
          url: fileUrl
        }
      });
    } catch (error) {
      console.error('上传图片失败:', error);
      res.status(500).json({
        success: false,
        message: '上传图片失败',
        error: error.message
      });
    }
  }
);

// 上传文件
router.post('/file',
  authMiddleware,
  upload.single('file'),
  (req, res) => {
    try {
      // 检查文件是否上传
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '未上传文件'
        });
      }

      // 处理文件路径，确保使用相对路径
      const relativePath = req.file.path.replace(/\\/g, '/').replace(/^.*[\/\\]public[\/\\]/, '');

      // 构建URL
      const fileUrl = `${req.protocol}://${req.get('host')}/${relativePath}`;

      console.log('上传文件成功:', {
        originalPath: req.file.path,
        relativePath: relativePath,
        url: fileUrl
      });

      res.status(200).json({
        success: true,
        message: '文件上传成功',
        data: {
          id: Date.now().toString(), // 生成一个临时ID
          path: relativePath,
          url: fileUrl
        }
      });
    } catch (error) {
      console.error('上传文件失败:', error);
      res.status(500).json({
        success: false,
        message: '上传文件失败',
        error: error.message
      });
    }
  }
);

// 删除文件
router.delete('/file/:id',
  authMiddleware,
  (req, res) => {
    try {
      // 这里应该根据ID查找文件并删除
      // 由于没有实际的文件管理系统，这里只返回成功
      res.status(200).json({
        success: true,
        message: '文件删除成功'
      });
    } catch (error) {
      console.error('删除文件失败:', error);
      res.status(500).json({
        success: false,
        message: '删除文件失败',
        error: error.message
      });
    }
  }
);

module.exports = router;
