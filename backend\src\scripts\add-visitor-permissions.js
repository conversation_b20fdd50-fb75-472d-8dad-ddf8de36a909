/**
 * 为访问者角色添加必要的权限
 *
 * 这个脚本用于确保访问者角色拥有所有必要的基础权限，
 * 特别是导航栏所需的权限，如home:access和family:access
 */

require('dotenv').config();
const { Role, Permission } = require('../models');
const { Op } = require('sequelize');

(async () => {
  try {
    console.log('开始为访问者角色添加必要权限...');

    // 查找访问者角色
    let visitorRole = await Role.findOne({
      where: { name: '初级访问者' }
    });

    // 如果找不到初级访问者角色，尝试查找访问者角色
    if (!visitorRole) {
      console.log('未找到"初级访问者"角色，尝试查找"访问者"角色');
      visitorRole = await Role.findOne({
        where: { name: '访问者' }
      });
    }

    // 如果仍然找不到，尝试查找ID为4的角色
    if (!visitorRole) {
      console.log('未找到"访问者"角色，尝试查找ID为4的角色');
      visitorRole = await Role.findOne({
        where: { id: 4 }
      });
    }

    // 如果仍然找不到，创建一个新的访问者角色
    if (!visitorRole) {
      console.log('未找到任何访问者角色，创建新的访问者角色');
      visitorRole = await Role.create({
        name: '初级访问者',
        description: '新注册用户的默认角色',
        is_system: true
      });
    }

    console.log(`找到访问者角色: ${visitorRole.name} (ID: ${visitorRole.id})`);

    // 访问者应该拥有的权限代码列表 - 由管理员通过界面配置
    // 这里不再硬编码权限列表，而是从命令行参数获取
    const requiredPermissionCodes = process.argv.slice(2);

    console.log('将为访问者角色添加以下权限:', requiredPermissionCodes);

    // 如果没有提供权限代码，提示用户
    if (requiredPermissionCodes.length === 0) {
      console.log('未提供任何权限代码。请通过命令行参数指定要添加的权限代码。');
      console.log('例如: node add-visitor-permissions.js home:access knowledge:access file:upload');
      process.exit(0);
    }

    // 查找这些权限在数据库中的记录
    const permissions = await Permission.findAll({
      where: {
        code: {
          [Op.in]: requiredPermissionCodes
        }
      }
    });

    // 检查是否找到了所有必要的权限
    const foundPermissionCodes = permissions.map(p => p.code);
    const missingPermissionCodes = requiredPermissionCodes.filter(code => !foundPermissionCodes.includes(code));

    if (missingPermissionCodes.length > 0) {
      console.log('以下权限在数据库中不存在，需要创建:', missingPermissionCodes);

      // 创建缺失的权限
      for (const code of missingPermissionCodes) {
        let name, description, module;

        // 根据权限代码设置权限信息
        switch (code) {
          case 'home:access':
            name = '访问首页';
            description = '允许访问系统首页';
            module = 'basic';
            break;
          case 'family:access':
            name = '访问家族专题';
            description = '允许访问家族专题页面';
            module = 'basic';
            break;
          case 'personal:access':
            name = '访问个人专题';
            description = '允许访问个人专题页面';
            module = 'personal';
            break;
          case 'personal:ai_use':
            name = '使用个人专题AI助手';
            description = '允许在个人专题中使用AI助手';
            module = 'personal';
            break;
          case 'knowledge:access':
            name = '访问知识库';
            description = '允许访问知识库页面';
            module = 'knowledge';
            break;
          case 'knowledge:create_user':
            name = '创建用户知识库';
            description = '允许创建用户知识库';
            module = 'knowledge';
            break;
          case 'file:upload':
            name = '上传文件';
            description = '允许上传文件到知识库';
            module = 'file';
            break;
          case 'data:access':
            name = '访问数据查询';
            description = '允许访问数据查询页面';
            module = 'data';
            break;
          case 'data:ai_query':
            name = '使用数据查询AI';
            description = '允许在数据查询中使用AI';
            module = 'data';
            break;
          case 'assistant:use':
            name = '使用AI研究助手';
            description = '允许使用AI研究助手';
            module = 'assistant';
            break;
          default:
            name = code;
            description = `权限 ${code}`;
            module = 'other';
        }

        // 创建权限
        const newPermission = await Permission.create({
          name,
          code,
          description,
          module
        });

        console.log(`已创建权限: ${newPermission.name} (${newPermission.code})`);
        permissions.push(newPermission);
      }
    }

    // 获取角色当前的权限
    const currentPermissions = await visitorRole.getPermissions();
    const currentPermissionCodes = currentPermissions.map(p => p.code);

    // 找出需要添加的权限
    const permissionsToAdd = permissions.filter(p => !currentPermissionCodes.includes(p.code));

    if (permissionsToAdd.length > 0) {
      console.log('为访问者角色添加以下权限:', permissionsToAdd.map(p => p.code));
      await visitorRole.addPermissions(permissionsToAdd);
      console.log('权限添加成功!');
    } else {
      console.log('访问者角色已拥有所有必要权限，无需添加');
    }

    console.log('脚本执行完成');
    process.exit(0);
  } catch (error) {
    console.error('执行脚本时发生错误:', error);
    process.exit(1);
  }
})();
