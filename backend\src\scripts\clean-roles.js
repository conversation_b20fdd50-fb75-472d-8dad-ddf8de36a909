/**
 * 清理角色
 * 
 * 这个脚本用于清理数据库中不需要的角色，只保留管理员和访问者角色
 */

require('dotenv').config();
const { Role, User } = require('../models');
const { Op } = require('sequelize');

(async () => {
  try {
    console.log('开始清理角色...');

    // 查询所有角色
    const allRoles = await Role.findAll();
    console.log(`数据库中共有 ${allRoles.length} 个角色`);

    // 要保留的角色名称
    const keepRoleNames = ['admin', 'basic_user', '管理员', '访问者', '初级访问者'];

    // 查找要保留的角色
    const rolesToKeep = await Role.findAll({
      where: {
        [Op.or]: [
          { name: { [Op.in]: keepRoleNames } },
          { is_system: true }
        ]
      }
    });

    console.log(`将保留 ${rolesToKeep.length} 个角色:`);
    rolesToKeep.forEach(role => {
      console.log(`- ${role.name} (ID: ${role.id})`);
    });

    // 查找要删除的角色
    const rolesToDelete = await Role.findAll({
      where: {
        [Op.and]: [
          { name: { [Op.notIn]: keepRoleNames } },
          { is_system: false }
        ]
      }
    });

    if (rolesToDelete.length === 0) {
      console.log('没有需要删除的角色');
      process.exit(0);
    }

    console.log(`将删除 ${rolesToDelete.length} 个角色:`);
    for (const role of rolesToDelete) {
      console.log(`- ${role.name} (ID: ${role.id})`);
      
      // 查找使用此角色的用户数量
      const userCount = await User.count({
        where: { role_id: role.id }
      });
      
      if (userCount > 0) {
        console.log(`  警告: 有 ${userCount} 个用户使用此角色`);
        
        // 查找默认的访问者角色
        const visitorRole = await Role.findOne({
          where: {
            [Op.or]: [
              { name: '初级访问者' },
              { name: '访问者' },
              { name: 'basic_user' }
            ]
          }
        });
        
        if (visitorRole) {
          // 询问是否要将用户迁移到访问者角色
          console.log(`  是否要将这些用户迁移到"${visitorRole.name}"角色? (y/n)`);
          const readline = require('readline').createInterface({
            input: process.stdin,
            output: process.stdout
          });
          
          const answer = await new Promise(resolve => {
            readline.question('', resolve);
          });
          readline.close();
          
          if (answer.toLowerCase() === 'y') {
            // 更新用户角色
            await User.update(
              { role_id: visitorRole.id },
              { where: { role_id: role.id } }
            );
            console.log(`  已将 ${userCount} 个用户迁移到"${visitorRole.name}"角色`);
          } else {
            console.log(`  跳过删除角色"${role.name}"`);
            continue;
          }
        } else {
          console.log(`  未找到访问者角色，跳过删除角色"${role.name}"`);
          continue;
        }
      }
      
      // 删除角色
      await role.destroy();
      console.log(`  已删除角色"${role.name}"`);
    }

    console.log('角色清理完成');
    process.exit(0);
  } catch (error) {
    console.error('执行脚本时发生错误:', error);
    process.exit(1);
  }
})();
