/**
 * 清理测试角色脚本
 * 
 * 该脚本用于清理系统中的测试角色，并将使用这些角色的用户迁移到访问者角色
 */

require('dotenv').config();
const { User, Role } = require('../models');
const { Op } = require('sequelize');

async function cleanupTestRoles() {
  try {
    console.log('开始清理测试角色...');

    // 查找管理员角色
    const adminRole = await Role.findOne({
      where: { 
        [Op.or]: [
          { name: '管理员', is_system: true },
          { name: 'admin', is_system: true }
        ]
      }
    });
    
    // 查找访问者角色
    const visitorRole = await Role.findOne({
      where: { 
        [Op.or]: [
          { name: '访问者', is_system: true },
          { name: '初级访问者', is_system: true },
          { name: 'basic_user', is_system: true }
        ]
      }
    });
    
    if (!adminRole) {
      console.error('未找到管理员角色，无法继续');
      return;
    }

    if (!visitorRole) {
      console.error('未找到访问者角色，无法继续');
      return;
    }
    
    console.log(`找到管理员角色: ${adminRole.name} (ID: ${adminRole.id})`);
    console.log(`找到访问者角色: ${visitorRole.name} (ID: ${visitorRole.id})`);
    
    // 查找所有测试角色
    const testRoles = await Role.findAll({
      where: {
        [Op.and]: [
          {
            [Op.or]: [
              { name: { [Op.like]: 'test_%' } },
              { name: { [Op.like]: '%_test' } },
              { name: { [Op.like]: '%_role_%' } }
            ]
          },
          { is_system: false } // 只处理非系统角色
        ]
      }
    });
    
    console.log(`找到 ${testRoles.length} 个测试角色`);
    
    // 处理每个测试角色
    for (const role of testRoles) {
      // 查找使用该角色的用户
      const users = await User.findAll({
        where: { role_id: role.id }
      });
      
      if (users.length > 0) {
        console.log(`角色 "${role.name}" (ID: ${role.id}) 有 ${users.length} 个用户使用`);
        
        // 将用户迁移到访问者角色
        await User.update(
          { role_id: visitorRole.id },
          { where: { role_id: role.id } }
        );
        
        console.log(`已将 ${users.length} 个用户迁移到访问者角色`);
      } else {
        console.log(`角色 "${role.name}" (ID: ${role.id}) 没有用户使用`);
      }
      
      // 删除角色
      await role.destroy();
      console.log(`已删除角色 "${role.name}" (ID: ${role.id})`);
    }
    
    console.log('测试角色清理完成');
  } catch (error) {
    console.error('清理测试角色时出错:', error);
  }
}

// 执行清理
cleanupTestRoles().then(() => {
  console.log('脚本执行完成');
  process.exit(0);
}).catch(err => {
  console.error('脚本执行失败:', err);
  process.exit(1);
});
