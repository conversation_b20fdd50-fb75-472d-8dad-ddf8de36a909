/**
 * 修复管理员角色脚本
 * 
 * 该脚本用于修复系统中的多个管理员角色问题，确保admin账号使用正确的管理员角色
 */

require('dotenv').config();
const { User, Role, Permission } = require('../models');
const { Op } = require('sequelize');

async function fixAdminRoles() {
  try {
    console.log('开始修复管理员角色...');

    // 查找所有管理员角色
    const adminRoles = await Role.findAll({
      where: { 
        [Op.or]: [
          { name: '管理员' },
          { name: 'admin' }
        ]
      },
      include: [
        {
          model: Permission,
          as: 'permissions'
        }
      ]
    });
    
    console.log(`找到 ${adminRoles.length} 个管理员角色`);
    
    if (adminRoles.length === 0) {
      console.log('未找到管理员角色，无需修复');
      return;
    }
    
    if (adminRoles.length === 1) {
      console.log(`只有一个管理员角色 (ID: ${adminRoles[0].id})，无需修复`);
      
      // 确保该角色被标记为系统角色
      if (!adminRoles[0].is_system) {
        await adminRoles[0].update({ is_system: true });
        console.log(`已将管理员角色 (ID: ${adminRoles[0].id}) 标记为系统角色`);
      }
      
      // 查找admin用户并确保关联到该角色
      const adminUser = await User.findOne({
        where: { username: 'admin' }
      });
      
      if (adminUser && adminUser.role_id !== adminRoles[0].id) {
        await adminUser.update({ role_id: adminRoles[0].id });
        console.log(`已将admin用户关联到管理员角色 (ID: ${adminRoles[0].id})`);
      }
      
      return;
    }
    
    // 找出权限最多的管理员角色作为主要角色
    let mainAdminRole = adminRoles[0];
    let maxPermissions = adminRoles[0].permissions.length;
    
    for (const role of adminRoles) {
      console.log(`角色 "${role.name}" (ID: ${role.id}) 有 ${role.permissions.length} 个权限`);
      
      if (role.permissions.length > maxPermissions) {
        mainAdminRole = role;
        maxPermissions = role.permissions.length;
      } else if (role.permissions.length === maxPermissions && role.is_system) {
        // 如果权限数量相同，优先选择系统角色
        mainAdminRole = role;
      }
    }
    
    console.log(`选择 "${mainAdminRole.name}" (ID: ${mainAdminRole.id}) 作为主要管理员角色，它有 ${mainAdminRole.permissions.length} 个权限`);
    
    // 确保该角色被标记为系统角色
    if (!mainAdminRole.is_system) {
      await mainAdminRole.update({ is_system: true });
      console.log(`已将主要管理员角色标记为系统角色`);
    }
    
    // 查找admin用户
    const adminUser = await User.findOne({
      where: { username: 'admin' }
    });
    
    if (adminUser) {
      // 更新admin用户的角色ID
      await adminUser.update({
        role_id: mainAdminRole.id,
        role: 'admin' // 确保role字段也设置为admin
      });
      
      console.log(`已将admin用户关联到主要管理员角色 (ID: ${mainAdminRole.id})`);
    } else {
      console.log('未找到admin用户');
    }
    
    // 处理其他管理员角色
    for (const role of adminRoles) {
      if (role.id !== mainAdminRole.id) {
        // 查找使用该角色的用户
        const users = await User.findAll({
          where: { role_id: role.id }
        });
        
        if (users.length > 0) {
          console.log(`角色 "${role.name}" (ID: ${role.id}) 有 ${users.length} 个用户使用`);
          
          // 将使用该角色的用户迁移到主要管理员角色
          await User.update(
            { role_id: mainAdminRole.id },
            { where: { role_id: role.id } }
          );
          
          console.log(`已将 ${users.length} 个用户迁移到主要管理员角色`);
        }
        
        // 删除角色
        await role.destroy();
        console.log(`已删除角色 "${role.name}" (ID: ${role.id})`);
      }
    }
    
    console.log('管理员角色修复完成');
  } catch (error) {
    console.error('修复管理员角色时出错:', error);
  }
}

// 执行修复
fixAdminRoles().then(() => {
  console.log('脚本执行完成');
  process.exit(0);
}).catch(err => {
  console.error('脚本执行失败:', err);
  process.exit(1);
});
