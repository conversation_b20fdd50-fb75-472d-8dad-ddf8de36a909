/**
 * 列出所有角色
 * 
 * 这个脚本用于列出数据库中的所有角色
 */

require('dotenv').config();
const { Role } = require('../models');

(async () => {
  try {
    console.log('正在获取所有角色...');

    // 查询所有角色
    const roles = await Role.findAll({
      order: [['id', 'ASC']]
    });

    console.log('数据库中共有 ' + roles.length + ' 个角色:');
    console.log('--------------------------------------');
    console.log('ID\t名称\t\t描述\t\t系统角色');
    console.log('--------------------------------------');
    
    roles.forEach(role => {
      console.log(`${role.id}\t${role.name}\t\t${role.description || '无描述'}\t\t${role.is_system ? '是' : '否'}`);
    });

    console.log('--------------------------------------');
    console.log('脚本执行完成');
    process.exit(0);
  } catch (error) {
    console.error('执行脚本时发生错误:', error);
    process.exit(1);
  }
})();
