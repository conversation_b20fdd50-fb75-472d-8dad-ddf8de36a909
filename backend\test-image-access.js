/**
 * 图片访问测试脚本
 * 用于测试图片下载和访问功能
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * 测试图片下载功能
 */
async function testImageDownload() {
  console.log('=== 测试图片下载功能 ===');

  const testImageUrl = 'http://hefuf.com/uploads/images/external-1749087017321-59762865.jpg';

  try {
    // 检查图片文件是否存在
    const imagePath = path.join(__dirname, 'public/uploads/images/external-1749087017321-59762865.jpg');
    console.log('检查图片文件路径:', imagePath);

    if (fs.existsSync(imagePath)) {
      const stats = fs.statSync(imagePath);
      console.log('✅ 图片文件存在');
      console.log('文件大小:', stats.size, 'bytes');
      console.log('创建时间:', stats.birthtime);
      console.log('修改时间:', stats.mtime);

      // 检查文件是否为空
      if (stats.size === 0) {
        console.log('❌ 图片文件为空');
        return false;
      }

      // 尝试读取文件头部，验证是否为有效图片
      const buffer = fs.readFileSync(imagePath);
      const header = buffer.slice(0, 10);
      console.log('文件头部:', header.toString('hex'));

      // 检查常见图片格式的文件头
      const jpegHeader = buffer.slice(0, 3).toString('hex');
      const pngHeader = buffer.slice(0, 8).toString('hex');

      if (jpegHeader === 'ffd8ff') {
        console.log('✅ 检测到JPEG格式图片');
      } else if (pngHeader === '89504e470d0a1a0a') {
        console.log('✅ 检测到PNG格式图片');
      } else {
        console.log('⚠️ 未识别的图片格式');
      }

      return true;
    } else {
      console.log('❌ 图片文件不存在:', imagePath);
      return false;
    }
  } catch (error) {
    console.error('❌ 测试图片下载失败:', error.message);
    return false;
  }
}

/**
 * 测试图片URL访问
 */
async function testImageUrlAccess() {
  console.log('\n=== 测试图片URL访问 ===');

  const testUrls = [
    'http://localhost:5001/uploads/images/external-1749087017321-59762865.jpg',
    'http://**********:5001/uploads/images/external-1749087017321-59762865.jpg',
    'http://**********/uploads/images/external-1749087017321-59762865.jpg'
  ];

  for (const url of testUrls) {
    try {
      console.log(`\n测试URL: ${url}`);

      const response = await axios.head(url, {
        timeout: 5000,
        validateStatus: function (status) {
          return status < 500; // 接受所有小于500的状态码
        }
      });

      console.log('状态码:', response.status);
      console.log('Content-Type:', response.headers['content-type']);
      console.log('Content-Length:', response.headers['content-length']);

      if (response.status === 200) {
        console.log('✅ URL访问成功');
      } else {
        console.log('⚠️ URL返回非200状态码');
      }

    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ 连接被拒绝 - 服务器可能未运行');
      } else if (error.code === 'ENOTFOUND') {
        console.log('❌ 域名解析失败');
      } else if (error.code === 'ETIMEDOUT') {
        console.log('❌ 连接超时');
      } else {
        console.log('❌ 访问失败:', error.message);
      }
    }
  }
}

/**
 * 测试静态文件服务配置
 */
async function testStaticFileService() {
  console.log('\n=== 测试静态文件服务配置 ===');

  // 检查uploads目录结构
  const uploadsDir = path.join(__dirname, 'public/uploads');
  const imagesDir = path.join(uploadsDir, 'images');

  console.log('检查目录结构:');
  console.log('uploads目录:', fs.existsSync(uploadsDir) ? '✅ 存在' : '❌ 不存在');
  console.log('images目录:', fs.existsSync(imagesDir) ? '✅ 存在' : '❌ 不存在');

  if (fs.existsSync(imagesDir)) {
    const files = fs.readdirSync(imagesDir);
    console.log('images目录中的文件数量:', files.length);

    if (files.length > 0) {
      console.log('最近的几个文件:');
      files.slice(0, 5).forEach(file => {
        const filePath = path.join(imagesDir, file);
        const stats = fs.statSync(filePath);
        console.log(`  - ${file} (${stats.size} bytes, ${stats.mtime.toISOString()})`);
      });
    }
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('开始图片访问测试...\n');

  const downloadTest = await testImageDownload();
  await testImageUrlAccess();
  await testStaticFileService();

  console.log('\n=== 测试总结 ===');
  console.log('图片下载测试:', downloadTest ? '✅ 通过' : '❌ 失败');

  console.log('\n=== 建议的解决方案 ===');
  if (!downloadTest) {
    console.log('1. 检查图片下载功能是否正常工作');
    console.log('2. 确认uploads目录权限设置正确');
    console.log('3. 检查网络连接和防火墙设置');
  }

  console.log('4. 确认nginx配置正确转发/uploads路径');
  console.log('5. 检查Docker容器间的网络连接');
  console.log('6. 验证环境变量BASE_URL设置正确');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testImageDownload,
  testImageUrlAccess,
  testStaticFileService
};
