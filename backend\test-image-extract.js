/**
 * 测试外部链接图片提取和下载功能
 */

const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicm9sZSI6ImFkbWluIiwicm9sZV9uYW1lIjoi566h55CG5ZGYIiwiaWF0IjoxNzQ4NTExNjc1LCJleHAiOjE3NDkxMTY0NzV9.PBIrIHagfXMJMEhR2iGe_rGSuDKFKJ_yOyQakqoRxhU';

async function testImageExtract() {
  try {
    console.log('=== 测试外部链接图片提取功能 ===\n');
    
    // 测试URL
    const testUrl = 'https://paper.people.com.cn/rmzk/html/2024-05/28/content_26061067.htm';
    
    console.log('1. 测试URL:', testUrl);
    
    // 调用图片提取API
    console.log('\n2. 调用图片提取API...');
    const response = await axios.post(`${API_BASE}/messages/extract-image`, {
      url: testUrl
    }, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('提取结果:');
    console.log('- 成功:', response.data.success);
    console.log('- 图片URL:', response.data.data.imageUrl);
    console.log('- 原始URL:', response.data.data.originalUrl);
    console.log('- 是否本地化:', response.data.data.isLocal);
    
    // 如果图片被下载到本地，测试访问
    if (response.data.data.isLocal) {
      console.log('\n3. 测试本地图片访问...');
      const localImageUrl = response.data.data.imageUrl;
      
      try {
        const imageResponse = await axios.get(localImageUrl, {
          timeout: 5000
        });
        console.log('本地图片访问成功:');
        console.log('- 状态码:', imageResponse.status);
        console.log('- 内容类型:', imageResponse.headers['content-type']);
        console.log('- 内容大小:', imageResponse.headers['content-length'], 'bytes');
      } catch (error) {
        console.log('本地图片访问失败:', error.message);
      }
    }

  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

testImageExtract();
