/**
 * 测试消息查询逻辑
 */

const { Message, User } = require('./src/models');

async function testMessageQuery() {
  try {
    console.log('测试消息查询逻辑...\n');

    // 1. 查询所有消息（不带条件）
    console.log('1. 查询所有消息（不带条件）:');
    const allMessages = await Message.findAll({
      order: [['created_at', 'DESC']],
      limit: 10,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    console.log(`找到 ${allMessages.length} 条消息:`);
    allMessages.forEach((message, index) => {
      console.log(`${index + 1}. ID: ${message.id}, 标题: ${message.title}, 状态: ${message.status}, 创建时间: ${message.created_at}`);
    });

    // 2. 查询草稿消息
    console.log('\n2. 查询草稿消息:');
    const draftMessages = await Message.findAll({
      where: { status: 'draft' },
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    console.log(`找到 ${draftMessages.length} 条草稿:`);
    draftMessages.forEach((message, index) => {
      console.log(`${index + 1}. ID: ${message.id}, 标题: ${message.title}, 状态: ${message.status}, 创建时间: ${message.created_at}`);
    });

    // 3. 查询已发布消息
    console.log('\n3. 查询已发布消息:');
    const publishedMessages = await Message.findAll({
      where: { status: 'published' },
      order: [['created_at', 'DESC']],
      limit: 10,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    console.log(`找到 ${publishedMessages.length} 条已发布消息:`);
    publishedMessages.forEach((message, index) => {
      console.log(`${index + 1}. ID: ${message.id}, 标题: ${message.title}, 状态: ${message.status}, 创建时间: ${message.created_at}`);
    });

    // 4. 模拟前端的查询（无状态参数）
    console.log('\n4. 模拟前端的查询（无状态参数，limit=8）:');
    const frontendQuery = await Message.findAndCountAll({
      where: {}, // 空的where条件，应该返回所有状态的消息
      limit: 8,
      offset: 0,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    console.log(`前端查询结果: 总数 ${frontendQuery.count}, 返回 ${frontendQuery.rows.length} 条:`);
    frontendQuery.rows.forEach((message, index) => {
      console.log(`${index + 1}. ID: ${message.id}, 标题: ${message.title}, 状态: ${message.status}, 创建时间: ${message.created_at}`);
    });

    // 5. 查找特定ID的消息
    console.log('\n5. 查找ID为22的消息:');
    const message22 = await Message.findByPk(22);
    if (message22) {
      console.log(`找到消息22: 标题=${message22.title}, 状态=${message22.status}, 创建时间=${message22.created_at}`);
    } else {
      console.log('未找到ID为22的消息');
    }

  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    process.exit(0);
  }
}

testMessageQuery();
