/**
 * 测试URL解析逻辑
 */

// 测试原始URL和图片URL
const originalUrl = 'https://paper.people.com.cn/rmzk/html/2024-05/28/content_26061067.htm';
const imageUrl = 'https://paper.people.com.cn/../../images/2024-05/28/06/Page_b.jpg';

console.log('原始URL:', originalUrl);
console.log('图片URL:', imageUrl);

// 测试当前的处理逻辑
function processImageUrl(url, imageUrl) {
  // 处理相对URL
  if (imageUrl && !imageUrl.startsWith('http')) {
    const urlObj = new URL(url);
    if (imageUrl.startsWith('//')) {
      imageUrl = urlObj.protocol + imageUrl;
    } else if (imageUrl.startsWith('/')) {
      imageUrl = urlObj.origin + imageUrl;
    } else {
      imageUrl = urlObj.origin + '/' + imageUrl;
    }
  }
  return imageUrl;
}

const processedUrl = processImageUrl(originalUrl, imageUrl);
console.log('处理后的URL:', processedUrl);

// 测试正确的URL解析方法
function correctProcessImageUrl(baseUrl, imageUrl) {
  try {
    // 如果图片URL已经是完整的URL，直接返回
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    
    // 使用URL构造函数正确解析相对路径
    const resolvedUrl = new URL(imageUrl, baseUrl);
    return resolvedUrl.href;
  } catch (error) {
    console.error('URL解析失败:', error.message);
    return imageUrl;
  }
}

const correctUrl = correctProcessImageUrl(originalUrl, imageUrl);
console.log('正确解析的URL:', correctUrl);

// 测试一些其他的相对路径情况
const testCases = [
  '../../images/test.jpg',
  '/images/test.jpg',
  'images/test.jpg',
  '//example.com/image.jpg',
  'https://example.com/image.jpg'
];

console.log('\n测试其他相对路径:');
testCases.forEach(testUrl => {
  const result = correctProcessImageUrl(originalUrl, testUrl);
  console.log(`${testUrl} -> ${result}`);
});
