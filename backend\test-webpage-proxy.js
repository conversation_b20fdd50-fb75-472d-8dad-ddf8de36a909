/**
 * 测试网页代理功能
 */

const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicm9sZSI6ImFkbWluIiwicm9sZV9uYW1lIjoi566h55CG5ZGYIiwiaWF0IjoxNzQ4NTExNjc1LCJleHAiOjE3NDkxMTY0NzV9.PBIrIHagfXMJMEhR2iGe_rGSuDKFKJ_yOyQakqoRxhU';

async function testWebPageProxy() {
  try {
    console.log('=== 测试网页代理功能 ===\n');
    
    // 测试微信公众号URL
    const wechatUrl = 'https://mp.weixin.qq.com/s/kaskpROqju-8cNFhQJBPcQ';
    
    console.log('1. 测试微信公众号URL:', wechatUrl);
    
    // 调用网页代理API
    console.log('\n2. 调用网页代理API...');
    const response = await axios.post(`${API_BASE}/messages/get-webpage-content`, {
      url: wechatUrl
    }, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });
    
    console.log('代理结果:');
    console.log('- 成功:', response.data.success);
    console.log('- URL:', response.data.data.url);
    console.log('- 是否微信:', response.data.data.isWechat);
    console.log('- 内容长度:', response.data.data.content.length, '字符');
    
    // 检查内容是否包含关键信息
    const content = response.data.data.content;
    const hasTitle = content.includes('<title>') || content.includes('title');
    const hasContent = content.includes('rich_media_content') || content.includes('js_content');
    
    console.log('- 包含标题:', hasTitle);
    console.log('- 包含内容:', hasContent);
    
    // 保存内容到文件以便检查
    const fs = require('fs');
    fs.writeFileSync('wechat-content.html', content, 'utf8');
    console.log('- 内容已保存到 wechat-content.html');

  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

testWebPageProxy();
