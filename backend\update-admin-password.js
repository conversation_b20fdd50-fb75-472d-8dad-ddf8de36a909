/**
 * 更新管理员密码脚本
 * 
 * 该脚本用于重置管理员账号的密码
 */

const { User, sequelize } = require('./src/models');
const bcrypt = require('bcryptjs');

async function updateAdminPassword() {
  try {
    console.log('开始更新管理员密码...');

    // 查找管理员用户
    const adminUser = await User.findOne({
      where: { username: 'admin' }
    });

    if (!adminUser) {
      console.log('未找到管理员用户，无法更新密码');
      return;
    }

    console.log('找到管理员用户，ID:', adminUser.id);

    // 生成新密码哈希
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash('admin123', salt);

    // 直接更新数据库中的密码，绕过模型的beforeUpdate钩子
    await sequelize.query(
      'UPDATE users SET password = ? WHERE id = ?',
      {
        replacements: [passwordHash, adminUser.id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    console.log('管理员密码已更新');
    console.log('新密码: admin123');

    // 验证密码是否正确
    const updatedAdmin = await User.findByPk(adminUser.id);
    const isPasswordValid = await bcrypt.compare('admin123', updatedAdmin.password);
    
    console.log('密码验证结果:', isPasswordValid ? '成功' : '失败');
    
    return updatedAdmin;
  } catch (error) {
    console.error('更新管理员密码失败:', error);
    throw error;
  }
}

// 执行更新密码
updateAdminPassword()
  .then(admin => {
    if (admin) {
      console.log('管理员密码更新成功');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('更新密码时发生错误:', error);
    process.exit(1);
  });
