/**
 * 检查数据库中的消息数据
 */

const mysql = require('mysql2/promise');

async function checkDatabase() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'hefamily_platform'
    });

    console.log('数据库连接成功\n');

    // 查询所有消息
    const [messages] = await connection.execute(`
      SELECT 
        id, 
        title, 
        author, 
        type, 
        status, 
        cover_image,
        created_at,
        updated_at,
        created_by
      FROM messages 
      ORDER BY created_at DESC
    `);

    console.log(`数据库中总共有 ${messages.length} 条消息:\n`);

    messages.forEach((message, index) => {
      console.log(`${index + 1}. ID: ${message.id}`);
      console.log(`   标题: ${message.title}`);
      console.log(`   作者: ${message.author}`);
      console.log(`   状态: ${message.status}`);
      console.log(`   类型: ${message.type}`);
      console.log(`   封面图片: ${message.cover_image || '无'}`);
      console.log(`   创建时间: ${message.created_at}`);
      console.log(`   更新时间: ${message.updated_at}`);
      console.log(`   创建者ID: ${message.created_by}`);
      console.log('   ---');
    });

    // 按状态分组统计
    const statusCounts = messages.reduce((acc, message) => {
      acc[message.status] = (acc[message.status] || 0) + 1;
      return acc;
    }, {});

    console.log('\n状态统计:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count} 条`);
    });

    // 查找最近创建的草稿
    const drafts = messages.filter(m => m.status === 'draft');
    console.log(`\n草稿消息详情 (${drafts.length} 条):`);
    drafts.forEach((draft, index) => {
      console.log(`${index + 1}. ${draft.title} (ID: ${draft.id})`);
      console.log(`   创建时间: ${draft.created_at}`);
      console.log(`   封面图片: ${draft.cover_image || '无'}`);
    });

    // 查找最近5分钟内创建的消息
    const recentMessages = messages.filter(m => {
      const createdTime = new Date(m.created_at);
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      return createdTime > fiveMinutesAgo;
    });

    console.log(`\n最近5分钟内创建的消息 (${recentMessages.length} 条):`);
    recentMessages.forEach((message, index) => {
      console.log(`${index + 1}. ${message.title} (ID: ${message.id}) - 状态: ${message.status} - 时间: ${message.created_at}`);
    });

  } catch (error) {
    console.error('数据库查询失败:', error.message);
    
    // 如果是连接错误，尝试其他可能的配置
    if (error.code === 'ER_ACCESS_DENIED_ERROR' || error.code === 'ECONNREFUSED') {
      console.log('\n尝试其他数据库配置...');
      
      try {
        connection = await mysql.createConnection({
          host: 'localhost',
          user: 'root',
          password: '',
          database: 'hefamily_platform'
        });
        
        console.log('使用空密码连接成功');
        
        const [messages] = await connection.execute('SELECT COUNT(*) as count FROM messages');
        console.log('消息总数:', messages[0].count);
        
      } catch (error2) {
        console.error('备用连接也失败:', error2.message);
      }
    }
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

checkDatabase();
