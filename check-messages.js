/**
 * 使用现有模型检查数据库中的消息
 */

const { Message, User } = require('./backend/src/models');

async function checkMessages() {
  try {
    console.log('正在查询数据库中的消息...\n');

    // 获取所有消息
    const messages = await Message.findAll({
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    console.log(`数据库中总共有 ${messages.length} 条消息:\n`);

    messages.forEach((message, index) => {
      const messageData = message.toJSON();
      console.log(`${index + 1}. ID: ${messageData.id}`);
      console.log(`   标题: ${messageData.title}`);
      console.log(`   作者: ${messageData.author}`);
      console.log(`   状态: ${messageData.status}`);
      console.log(`   类型: ${messageData.type}`);
      console.log(`   封面图片: ${messageData.cover_image || '无'}`);
      console.log(`   创建时间: ${messageData.created_at}`);
      console.log(`   更新时间: ${messageData.updated_at}`);
      console.log(`   创建者: ${messageData.creator ? messageData.creator.username : '未知'}`);
      console.log('   ---');
    });

    // 按状态分组统计
    const statusCounts = messages.reduce((acc, message) => {
      acc[message.status] = (acc[message.status] || 0) + 1;
      return acc;
    }, {});

    console.log('\n状态统计:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count} 条`);
    });

    // 查找草稿消息
    const drafts = messages.filter(m => m.status === 'draft');
    console.log(`\n草稿消息详情 (${drafts.length} 条):`);
    drafts.forEach((draft, index) => {
      const draftData = draft.toJSON();
      console.log(`${index + 1}. ${draftData.title} (ID: ${draftData.id})`);
      console.log(`   作者: ${draftData.author}`);
      console.log(`   创建时间: ${draftData.created_at}`);
      console.log(`   封面图片: ${draftData.cover_image || '无'}`);
      if (draftData.content) {
        console.log(`   内容预览: ${draftData.content.substring(0, 50)}...`);
      }
    });

    // 查找最近10分钟内创建的消息
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
    const recentMessages = messages.filter(m => {
      return new Date(m.created_at) > tenMinutesAgo;
    });

    console.log(`\n最近10分钟内创建的消息 (${recentMessages.length} 条):`);
    recentMessages.forEach((message, index) => {
      const messageData = message.toJSON();
      console.log(`${index + 1}. ${messageData.title} (ID: ${messageData.id})`);
      console.log(`   状态: ${messageData.status}`);
      console.log(`   作者: ${messageData.author}`);
      console.log(`   创建时间: ${messageData.created_at}`);
      console.log(`   封面图片: ${messageData.cover_image || '无'}`);
    });

    // 特别查找包含"草稿"或"测试"关键词的消息
    const draftKeywordMessages = messages.filter(m =>
      m.title.includes('草稿') || m.title.includes('测试') || m.title.includes('最后')
    );

    console.log(`\n包含"草稿"、"测试"或"最后"关键词的消息 (${draftKeywordMessages.length} 条):`);
    draftKeywordMessages.forEach((message, index) => {
      const messageData = message.toJSON();
      console.log(`${index + 1}. ${messageData.title} (ID: ${messageData.id})`);
      console.log(`   状态: ${messageData.status}`);
      console.log(`   创建时间: ${messageData.created_at}`);
    });

  } catch (error) {
    console.error('查询数据库失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    process.exit(0);
  }
}

checkMessages();
