#!/bin/bash

echo "=== Nginx上传目录排查脚本 ==="
echo ""

echo "1. 检查项目目录结构："
ls -la /soft/hefu/
echo ""

echo "2. 检查具体项目目录："
if [ -d "/soft/hefu/2025060502/HF-production" ]; then
    echo "✓ 项目目录存在"
    ls -la /soft/hefu/2025060502/HF-production/backend/
else
    echo "✗ 项目目录不存在，请检查路径"
fi
echo ""

echo "3. 检查uploads目录："
if [ -d "/soft/hefu/2025060502/HF-production/backend/uploads" ]; then
    echo "✓ uploads目录存在"
    ls -la /soft/hefu/2025060502/HF-production/backend/uploads/
else
    echo "✗ uploads目录不存在"
fi
echo ""

echo "4. 检查images目录："
if [ -d "/soft/hefu/2025060502/HF-production/backend/uploads/images" ]; then
    echo "✓ images目录存在"
    ls -la /soft/hefu/2025060502/HF-production/backend/uploads/images/ | head -10
else
    echo "✗ images目录不存在"
fi
echo ""

echo "5. 检查目录权限："
ls -ld /soft/hefu/2025060502/HF-production/backend/uploads/
ls -ld /soft/hefu/2025060502/HF-production/backend/uploads/images/
echo ""

echo "6. 检查nginx进程用户："
ps aux | grep nginx | head -3
echo ""

echo "7. 测试nginx配置："
nginx -t
echo ""

echo "8. 检查nginx错误日志最后10行："
tail -10 /var/log/nginx/error.log
echo ""

echo "9. 创建测试文件："
echo "这是一个测试文件" > /soft/hefu/2025060502/HF-production/backend/uploads/images/test.txt
echo "测试文件创建完成，请访问: https://hefuf.com/uploads/images/test.txt"
echo ""

echo "10. 检查SELinux状态（如果适用）："
if command -v getenforce &> /dev/null; then
    getenforce
else
    echo "SELinux未安装或不适用"
fi
echo ""

echo "=== 排查完成 ==="
