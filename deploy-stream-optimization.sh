#!/bin/bash

# 生产环境流式输出优化部署脚本
# 解决生产环境流式输出断断续续的问题

echo "🚀 开始部署流式输出优化..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    exit 1
fi

# 1. 备份当前Nginx配置
echo "📦 备份当前Nginx配置..."
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)
if [ -f /etc/nginx/sites-available/default ]; then
    cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup.$(date +%Y%m%d_%H%M%S)
fi

# 2. 应用优化的Nginx配置
echo "⚙️ 应用优化的Nginx配置..."

# 创建优化的站点配置
cat > /etc/nginx/sites-available/hefamily-optimized << 'EOF'
server {
    listen 80;
    server_name _;  # 接受所有域名

    # 禁用缓冲，确保流式数据立即传输
    proxy_buffering off;
    proxy_cache off;
    proxy_request_buffering off;

    # 设置较小的缓冲区大小
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;

    # 禁用gzip压缩（对于流式数据）
    gzip off;

    # 前端静态文件
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 后端API - 特别优化流式端点
    location /api/ai/data-query/stream {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;

        # 禁用所有缓冲
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;

        # 设置超时
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;  # 5分钟，适应AI响应时间

        # 设置头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection '';

        # 禁用Nginx缓冲（重要！）
        proxy_set_header X-Accel-Buffering no;

        # 立即刷新
        proxy_flush_interval 1s;
    }

    # 其他流式端点
    location ~ ^/api/ai/assistant/.*/stream$ {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;

        # 禁用所有缓冲
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;

        # 设置超时
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # 设置头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection '';

        # 禁用Nginx缓冲
        proxy_set_header X-Accel-Buffering no;

        # 立即刷新
        proxy_flush_interval 1s;
    }

    # 普通API端点
    location /api/ {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 普通API可以使用缓冲
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
}
EOF

# 3. 启用新配置
echo "🔗 启用新的Nginx配置..."
ln -sf /etc/nginx/sites-available/hefamily-optimized /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 4. 优化Nginx主配置
echo "⚙️ 优化Nginx主配置..."
cat > /etc/nginx/nginx.conf << 'EOF'
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # MIME类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    # 访问日志
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # Gzip设置（对流式端点禁用）
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 客户端设置
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF

# 5. 测试Nginx配置
echo "🧪 测试Nginx配置..."
nginx -t
if [ $? -ne 0 ]; then
    echo "❌ Nginx配置测试失败，恢复备份..."
    cp /etc/nginx/nginx.conf.backup.* /etc/nginx/nginx.conf
    exit 1
fi

# 6. 重启Nginx
echo "🔄 重启Nginx..."
systemctl restart nginx
if [ $? -ne 0 ]; then
    echo "❌ Nginx重启失败"
    exit 1
fi

# 7. 检查服务状态
echo "📊 检查服务状态..."
systemctl status nginx --no-pager -l

# 8. 优化系统参数
echo "⚙️ 优化系统参数..."

# 创建系统优化配置
cat > /etc/sysctl.d/99-stream-optimization.conf << 'EOF'
# 网络优化
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216

# TCP优化
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0
net.ipv4.tcp_no_metrics_save = 1

# 文件描述符限制
fs.file-max = 65536
EOF

# 应用系统参数
sysctl -p /etc/sysctl.d/99-stream-optimization.conf

# 9. 创建监控脚本
echo "📊 创建监控脚本..."
cat > /usr/local/bin/stream-monitor.sh << 'EOF'
#!/bin/bash
# 流式输出监控脚本

echo "=== 流式输出监控报告 $(date) ==="

echo "1. Nginx状态:"
systemctl is-active nginx

echo "2. 前端服务状态 (端口3000):"
netstat -tlnp | grep :3000 || echo "前端服务未运行"

echo "3. 后端服务状态 (端口5001):"
netstat -tlnp | grep :5001 || echo "后端服务未运行"

echo "4. 活跃连接数:"
netstat -an | grep ESTABLISHED | wc -l

echo "5. Nginx错误日志 (最近10行):"
tail -n 10 /var/log/nginx/error.log

echo "6. 系统负载:"
uptime

echo "7. 内存使用:"
free -h

echo "================================"
EOF

chmod +x /usr/local/bin/stream-monitor.sh

# 10. 创建测试脚本
echo "🧪 创建测试脚本..."
cat > /usr/local/bin/test-stream.sh << 'EOF'
#!/bin/bash
# 流式输出测试脚本

echo "测试流式输出功能..."

# 测试数据查询助手流式端点
echo "测试数据查询助手流式端点..."
curl -X POST http://localhost/api/ai/data-query/stream \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{"query":"测试","conversation_id":null}' \
  --max-time 10 \
  -v

echo "测试完成"
EOF

chmod +x /usr/local/bin/test-stream.sh

echo "✅ 流式输出优化部署完成！"
echo ""
echo "📋 部署总结:"
echo "- ✅ Nginx配置已优化，禁用了流式端点的缓冲"
echo "- ✅ 系统参数已优化，提升网络性能"
echo "- ✅ 创建了监控脚本: /usr/local/bin/stream-monitor.sh"
echo "- ✅ 创建了测试脚本: /usr/local/bin/test-stream.sh"
echo ""
echo "🔧 使用方法:"
echo "- 监控服务状态: /usr/local/bin/stream-monitor.sh"
echo "- 测试流式输出: /usr/local/bin/test-stream.sh"
echo ""
echo "📝 注意事项:"
echo "- 请确保前端和后端服务正在运行"
echo "- 如果问题仍然存在，请检查Dify API的响应速度"
echo "- 可以通过监控脚本查看详细的服务状态"
