"use client"
import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { DataQueryAssistant } from "@/components/data-query"
import { DataQueryContent } from "@/components/data-query-content"
import { ModalProvider } from "@/components/modal-provider"
import { Button } from "@/components/ui/button"
import { AlertTriangle } from "lucide-react"
import { useRouter } from "next/navigation"
import { logger, sanitizeData } from "@/utils/logger"

export default function DataMiningPage() {
  const router = useRouter()
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  // 添加共享的知识库选择状态
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<string[]>([])
  // 添加AI助手配置状态
  const [aiConfig, setAiConfig] = useState<{
    apiKey: string;
    apiEndpoint: string;
    appId: string;
    appCode: string;
  } | null>(null)
  const [aiConfigLoading, setAiConfigLoading] = useState(false)
  const [aiConfigError, setAiConfigError] = useState<string | null>(null)

  // 检查用户是否已登录
  useEffect(() => {
    const checkLoginStatus = () => {
      const isUserLoggedIn = localStorage.getItem("isLoggedIn") === "true"
      setIsLoggedIn(isUserLoggedIn)
    }

    checkLoginStatus()
  }, [])

  // 加载AI助手配置
  useEffect(() => {
    const loadAiConfig = async () => {
      try {
        setAiConfigLoading(true)
        setAiConfigError(null)

        // 导入AI助手服务
        const aiAssistantService = await import('@/services/ai-assistant-service')

        // 获取数据查询助手配置
        const assistants = await aiAssistantService.getAIAssistants('data-query')
        logger.debug('获取到的数据查询助手:', assistants.map(a => ({ id: a.id, name: a.name, status: a.status })))

        if (assistants && assistants.length > 0) {
          // 使用第一个数据查询助手
          const assistant = assistants[0]

          logger.debug('找到数据查询助手:', {
            id: assistant.id,
            name: assistant.name,
            status: assistant.status,
            apiEndpoint: assistant.apiEndpoint,
            hasApiKey: !!assistant.apiKey,
            appId: assistant.appId || '未设置',
            appCode: assistant.appCode || '未设置'
          })

          setAiConfig({
            apiKey: assistant.apiKey,
            apiEndpoint: assistant.apiEndpoint,
            appId: assistant.appId || '',
            appCode: assistant.appCode || ''
          })

          logger.debug('成功加载数据查询助手配置:', assistant.name)
        } else {
          setAiConfigError('未找到数据查询助手配置')
          logger.warn('未找到数据查询助手配置，请在系统管理-AI管理中配置')
        }
      } catch (error) {
        logger.error('加载AI助手配置失败:', sanitizeData(error))
        setAiConfigError('加载AI助手配置失败，请稍后再试')
      } finally {
        setAiConfigLoading(false)
      }
    }

    if (isLoggedIn) {
      loadAiConfig()
    }
  }, [isLoggedIn])

  return (
    <ModalProvider>
      <div className="min-h-screen bg-white">
        <Navbar />
        {/* 移动端：上下布局，桌面端：左右布局 */}
        <div className="flex flex-col md:flex-row pt-16">
          {/* AI助手区域 - 移动端在上方，桌面端在左侧 */}
          <div className="w-full md:w-[40%] h-[50vh] md:h-[calc(100vh-64px)] md:fixed md:top-16 md:left-0 border-b md:border-b-0 md:border-r border-gray-200">
            {aiConfigLoading ? (
              <div className="flex justify-center items-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1e7a43] mx-auto mb-4"></div>
                  <p className="text-gray-600">正在加载AI助手配置...</p>
                </div>
              </div>
            ) : aiConfigError ? (
              <div className="flex justify-center items-center h-full">
                <div className="text-center p-6 bg-red-50 rounded-lg max-w-md">
                  <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-red-800 mb-2">配置错误</h3>
                  <p className="text-red-600 mb-4">{aiConfigError}</p>
                  <p className="text-sm text-gray-600">请联系管理员在系统管理-AI管理中配置数据查询助手</p>
                </div>
              </div>
            ) : (
              <DataQueryAssistant
                id="data-query-assistant"
                name="数据查询助手"
                description="智能学术助手，可以帮您检索相关资料，提供专业的研究支持和学术建议"
                dataSourceId="default-data-source"
                tags={["数据查询", "学术研究", "资料检索"]}
                isNew={false}
                isPopular={true}
                enabled={true}
                initialMessage="您好，我是您的智能学术助手。我可以帮您检索相关资料，提供专业的研究支持和学术建议。请问您需要查找什么资料？"
                selectedKnowledgeBases={selectedKnowledgeBases}
                apiKey={aiConfig?.apiKey}
                apiEndpoint={aiConfig?.apiEndpoint}
                appId={aiConfig?.appId}
                appCode={aiConfig?.appCode}
              />
            )}
          </div>

          {/* 数据查询内容区域 - 移动端在下方，桌面端在右侧 */}
          <div className="w-full md:w-[60%] md:ml-[40%] min-h-[50vh] md:min-h-0">
            {!isLoggedIn ? (
              <div className="p-4 md:p-8">
                <div className="bg-white p-6 md:p-8 rounded-lg shadow-md max-w-md mx-auto">
                  <div className="flex flex-col items-center text-center">
                    <AlertTriangle className="h-12 w-12 md:h-16 md:w-16 text-yellow-500 mb-4" />
                    <h2 className="text-xl md:text-2xl font-bold mb-4">需要登录</h2>
                    <p className="text-gray-600 mb-6 text-sm md:text-base">
                      您需要登录后才能使用数据查询功能。请先登录或注册一个账号。
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                      <Button
                        onClick={() => {
                          // 触发登录弹窗
                          const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
                          window.dispatchEvent(event);
                        }}
                        className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 w-full sm:w-auto"
                      >
                        登录
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          // 触发注册弹窗
                          const event = new CustomEvent('open-login-modal', { detail: { isRegister: true } });
                          window.dispatchEvent(event);
                        }}
                        className="w-full sm:w-auto"
                      >
                        注册
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <DataQueryContent
                selectedKnowledgeBases={selectedKnowledgeBases}
                onKnowledgeBasesChange={setSelectedKnowledgeBases}
              />
            )}
          </div>
        </div>
      </div>
    </ModalProvider>
  )
}
