@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 145 60% 30%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-muted: 210 40% 96.1%;
    --sidebar-muted-foreground: 215.4 16.3% 46.9%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-primary: 145 60% 30%;
    --sidebar-primary-foreground: 210 40% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 145 60% 30%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-muted: 217.2 32.6% 17.5%;
    --sidebar-muted-foreground: 215 20.2% 65.1%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-primary: 145 60% 30%;
    --sidebar-primary-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-size: 1.125rem; /* 增大基础字体大小，相当于增加2号 */
  }

  /* 防止移动端水平滚动 */
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* 确保所有元素不会超出视口宽度 */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* 特殊处理可能导致水平滚动的元素 */
  img, video, iframe, table, pre, code {
    max-width: 100% !important;
    height: auto;
  }

  /* 确保表格在移动端可以水平滚动而不是整个页面 */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端特殊处理 */
  @media (max-width: 768px) {
    /* 确保flex容器不会导致水平滚动 */
    .flex {
      flex-wrap: wrap;
    }

    /* 处理可能过长的文本 */
    .whitespace-nowrap {
      white-space: normal !important;
    }

    /* 除了弹幕动画外，其他元素都不应该使用nowrap */
    *:not(.comment-item) {
      white-space: normal !important;
    }

    /* 确保最小宽度不会导致水平滚动 */
    [class*="min-w-"] {
      min-width: auto !important;
    }

    /* 特殊处理按钮的最小宽度 */
    button[class*="min-w-"] {
      min-width: auto !important;
      width: auto !important;
    }
  }
}

/* 弹幕动画 */
@keyframes scrollComment {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.comment-item {
  position: absolute;
  white-space: nowrap;
  animation: scrollComment linear infinite;
  animation-play-state: running; /* 默认状态为滚动 */
  max-width: none; /* 弹幕动画需要超出容器宽度 */
}

/* 添加淡入上升动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* 添加文本缩进样式 */
.text-indent-2em {
  text-indent: 2em;
}

/* 微信内容样式重置 - 简化版本 */
.wechat-content {
  max-width: 100% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* 确保微信内容不会阻止滚动 */
.wechat-content * {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.wechat-content img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 1rem auto !important;
}

.wechat-content p {
  margin: 1rem 0 !important;
  line-height: 1.6 !important;
}

/* 隐藏微信特有的底部元素 */
.wechat-content [id*="js_stream_bottom"],
.wechat-content .wx_follow_context,
.wechat-content .wx_expand_article_button_wrap,
.wechat-content .function_mod,
.wechat-content .stream_bottom_bar_wrp {
  display: none !important;
}

/* 稳定滚动条样式 - 避免布局变化 */
.scrollbar-gutter-stable {
  scrollbar-gutter: stable;
}

/* 自定义滚动条样式 */
.overflow-y-scroll::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.overflow-y-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.overflow-y-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保滚动条始终占用空间，避免布局变化 */
.stable-scrollbar {
  overflow-y: scroll;
  scrollbar-gutter: stable;
}
