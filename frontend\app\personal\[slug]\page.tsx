"use client"

import { Navbar } from "@/components/navbar"
import { PersonalProfile } from "@/components/personal-profile"
import { PersonalAssistant } from "@/components/personal"
import { useEffect, useState } from "react"

interface PersonalPageProps {
  params: {
    slug: string
  }
}

export default function PersonalPage({ params }: PersonalPageProps) {
  // Get the slug from params
  const { slug } = params
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  // 检查用户是否已登录
  useEffect(() => {
    // 在实际应用中，这里应该检查用户的登录状态
    // 这里简单地从localStorage中获取用户信息
    const user = localStorage.getItem("user")
    setIsLoggedIn(!!user)
  }, [])

  // In a real application, you would fetch data from an API or database based on the slug
  // For now, we'll use a simple mapping of slugs to person data
  const personDataMap: Record<string, any> = {
    "cai-hesen": {
      id: "cai-hesen",
      name: "蔡和森",
      nameEn: "<PERSON><PERSON>",
      birthDate: "1895年3月30日",
      deathDate: "1931年8月4日",
      birthPlace: "上海市（原籍湖南省湘乡县永丰镇，今属双峰县）",
      education: "湖南省立第一师范学校",
      portrait: "/images/cai-hesen.png",
      biography:
        "蔡和森（1895年3月30日—1931年8月4日），字润寰，号泽膺，原籍湖南省湘乡县永丰镇（今属双峰县），出生于上海。中国共产党早期的重要领导人，杰出的共产主义战士，无产阶级革命家、理论家和宣传家。",
      timeline: [
        {
          year: "1895年3月30日",
          event: "出生于上海，后随母亲回到家乡湖南双峰。",
        },
        {
          year: "1913年",
          event: "考入湖南省立第一师范学校，与毛泽东同窗。",
        },
        {
          year: "1920年8月",
          event: '在给毛泽东的信中首次明确提出"明目张胆正式成立一个中国共产党"的主张。',
        },
        {
          year: "1931年8月4日",
          event: "在广州军政监狱牺牲，年仅36岁。",
        },
      ],
      materials: [
        {
          id: 1,
          title: "《建党问题》信件",
          description: "1920年蔡和森写给毛泽东的信，首次明确提出建立中国共产党的主张，是中共党史的重要文献。",
          image: "/placeholder.svg?height=200&width=300",
          pdfUrl: "/documents/cai-hesen-letter.pdf", // 添加PDF链接
        },
      ],
      comments: [
        {
          id: 1,
          user: "陈思远",
          content: "感谢分享这些珍贵的历史资料！蔡和森同志对中国共产党的创建有着不可磨灭的贡献。",
          time: "2分钟前",
        },
      ],
    },
    "li-fuchun": {
      id: "li-fuchun",
      name: "李富春",
      nameEn: "Li Fuchun",
      birthDate: "1900年5月22日",
      deathDate: "1975年1月9日",
      birthPlace: "湖南省长沙市",
      education: "莫斯科东方大学",
      portrait: "/images/li-fuchun.png",
      biography:
        "李富春（1900年5月22日-1975年1月9日），湖南长沙人，中国共产党创建时期入党的老党员，杰出的无产阶级革命家，忠诚的马克思主义者，党和国家的卓越领导人，中国社会主义经济建设的奠基者和组织者之一。",
      timeline: [
        {
          year: "1900年5月22日",
          event: "出生于湖南省长沙市一个教师家庭。",
        },
        {
          year: "1922年",
          event: "在法国加入中国共产党，成为党的早期成员。",
        },
        {
          year: "1923年",
          event: "与蔡畅在巴黎结婚，共同投身革命事业。",
        },
        {
          year: "1975年1月9日",
          event: "在北京逝世，享年75岁。",
        },
      ],
      materials: [
        {
          id: 1,
          title: "《李富春选集》",
          description: "收录了李富春同志关于经济建设和计划工作的重要文章和讲话，展现了他的经济思想和实践经验。",
          image: "/placeholder.svg?height=200&width=300",
          pdfUrl: "/documents/li-fuchun-collection.pdf", // 添加PDF链接
        },
      ],
      comments: [
        {
          id: 1,
          user: "王建国",
          content: "李富春同志为新中国经济建设做出了巨大贡献，他领导制定的第一个五年计划奠定了中国工业化的基础！",
          time: "3分钟前",
        },
      ],
    },
    "xiang-jingyu": {
      id: "xiang-jingyu",
      name: "向警予",
      nameEn: "Xiang Jingyu",
      birthDate: "1895年9月4日",
      deathDate: "1928年5月1日",
      birthPlace: "湖南省长沙县",
      education: "湖南省立第一女子师范学校",
      portrait: "/images/xiang-jingyu.png",
      biography:
        "向警予（1895年9月4日-1928年5月1日），湖南长沙人，中国共产党早期重要领导人，中国妇女运动的先驱，杰出的无产阶级革命家。",
      timeline: [
        {
          year: "1895年9月4日",
          event: "出生于湖南省长沙县一个知识分子家庭。",
        },
        {
          year: "1919年",
          event: "赴法国勤工俭学，与蔡和森结为伉俪。",
        },
        {
          year: "1928年5月1日",
          event: "在武汉被国民党反动派杀害，年仅33岁。",
        },
      ],
      materials: [
        {
          id: 1,
          title: "《向警予文集》",
          description: "收录了向警予同志关于妇女解放和革命斗争的重要文章和讲话。",
          image: "/placeholder.svg?height=200&width=300",
          pdfUrl: "/documents/xiang-jingyu-collection.pdf", // 添加PDF链接
        },
      ],
      comments: [
        {
          id: 1,
          user: "李明",
          content: "向警予同志是中国妇女运动的先驱，她的革命精神永远激励着我们！",
          time: "5分钟前",
        },
      ],
    },
    "cai-chang": {
      id: "cai-chang",
      name: "蔡畅",
      nameEn: "Cai Chang",
      birthDate: "1900年5月14日",
      deathDate: "1990年9月11日",
      birthPlace: "湖南省湘乡县",
      education: "莫斯科东方大学",
      portrait: "/images/cai-chang.png",
      biography:
        "蔡畅（1900年5月14日-1990年9月11日），湖南湘乡人，中国共产党早期重要领导人，杰出的无产阶级革命家，中国妇女运动的先驱和卓越领导人。",
      timeline: [
        {
          year: "1900年5月14日",
          event: "出生于湖南省湘乡县一个知识分子家庭。",
        },
        {
          year: "1923年",
          event: "与李富春在巴黎结婚，共同投身革命事业。",
        },
        {
          year: "1990年9月11日",
          event: "在北京逝世，享年90岁。",
        },
      ],
      materials: [
        {
          id: 1,
          title: "《蔡畅回忆录》",
          description: "记录了蔡畅同志的革命生涯和对中国妇女运动的贡献。",
          image: "/placeholder.svg?height=200&width=300",
          pdfUrl: "/documents/cai-chang-memoir.pdf", // 添加PDF链接
        },
      ],
      comments: [
        {
          id: 1,
          user: "张华",
          content: "蔡畅同志是中国妇女运动的杰出领导人，她的一生是为人民服务的一生！",
          time: "7分钟前",
        },
      ],
    },
    "ge-jianhao": {
      id: "ge-jianhao",
      name: "葛健豪",
      nameEn: "Ge Jianhao",
      birthDate: "1903年6月12日",
      deathDate: "1967年8月30日",
      birthPlace: "湖南省湘潭县",
      education: "莫斯科中山大学",
      portrait: "/images/ge-jianhao.png",
      biography:
        "葛健豪（1903年6月12日-1967年8月30日），湖南湘潭人，中国共产党优秀党员，杰出的革命家和军事指挥员，为中国人民的解放事业作出了重要贡献。",
      timeline: [
        {
          year: "1903年6月12日",
          event: "出生于湖南省湘潭县一个农民家庭。",
        },
        {
          year: "1925年",
          event: "加入中国共产党，投身革命事业。",
        },
        {
          year: "1967年8月30日",
          event: "在北京逝世，享年64岁。",
        },
      ],
      materials: [
        {
          id: 1,
          title: "《葛健豪革命事迹》",
          description: "记录了葛健豪同志的革命历程和重要贡献。",
          image: "/placeholder.svg?height=200&width=300",
          pdfUrl: "/documents/ge-jianhao-history.pdf", // 添加PDF链接
        },
      ],
      comments: [
        {
          id: 1,
          user: "刘强",
          content: "葛健豪同志的革命精神值得我们永远学习！",
          time: "10分钟前",
        },
      ],
    },
  }

  // Get the person data based on the slug, or use a default if not found
  const personData = personDataMap[slug] || personDataMap["cai-hesen"]

  return (
    <div className="min-h-screen bg-[#fdf9f1]">
      <Navbar />
      <div className="flex flex-col md:flex-row pt-16">
        {/* 左侧AI助手区域 - 固定宽度40% */}
        <div className="w-full md:w-[40%] h-[70vh] md:h-[calc(100vh-64px)] md:fixed md:top-16 md:left-0 border-r border-[#1e7a43]/20">
          <PersonalAssistant
            name={personData.name}
            personalId={personData.id}
            description={`${personData.name}专题助手`}
            initialMessage={`您好！我是${personData.name}研究助手，很高兴为您提供关于${personData.name}生平、思想和贡献的信息。请问有什么可以帮助您的吗？`}
          />
        </div>

        {/* 右侧内容区域 - 宽度60%，可滚动 */}
        <div className="w-full md:w-[60%] md:ml-[40%]">
          <PersonalProfile person={personData} isLoggedIn={isLoggedIn} />
        </div>
      </div>
    </div>
  )
}
