"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { PersonalProfile } from "@/components/personal-profile"
import { PersonalAssistant } from "@/components/personal/PersonalAssistant"
import { logger, sanitizeData } from "@/utils/logger"

export default function CaiHesenPage() {
  // 登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  // AI助手配置
  const [aiConfig, setAiConfig] = useState<{
    apiKey: string;
    apiEndpoint: string;
    appId: string;
    appCode: string;
  } | null>(null)
  const [aiConfigLoading, setAiConfigLoading] = useState(true)
  const [aiConfigError, setAiConfigError] = useState<string | null>(null)

  // 检查登录状态
  useEffect(() => {
    const checkLoginStatus = () => {
      // 检查token是否存在，这是真正的登录凭证
      const token = localStorage.getItem("hefamily_token");
      const loggedIn = !!token && localStorage.getItem("isLoggedIn") === "true";

      logger.debug("检查登录状态:", {
        hasToken: !!token,
        isLoggedInFlag: localStorage.getItem("isLoggedIn") === "true",
        finalLoginState: loggedIn
      });

      setIsLoggedIn(loggedIn);
    }

    checkLoginStatus();
    // 监听登录状态变化
    window.addEventListener("storage", checkLoginStatus);

    return () => {
      window.removeEventListener("storage", checkLoginStatus);
    }
  }, [])

  // 加载AI助手配置
  useEffect(() => {
    const loadAiConfig = async () => {
      try {
        setAiConfigLoading(true)
        setAiConfigError(null)

        // 导入AI助手服务
        const aiAssistantService = await import('@/services/ai-assistant-service')

        // 获取个人专题助手配置
        const assistants = await aiAssistantService.getAIAssistants('personal')
        logger.debug('获取到的所有个人专题助手:', assistants.map(a => ({ id: a.id, name: a.name, status: a.status })))

        if (assistants && assistants.length > 0) {
          // 直接查找ID为5的蔡和森专题助手
          const caiHesenAssistant = assistants.find(a => a.id === 5)

          if (caiHesenAssistant) {
            logger.debug('找到蔡和森专题助手(ID=5):', {
              id: caiHesenAssistant.id,
              name: caiHesenAssistant.name,
              status: caiHesenAssistant.status,
              apiEndpoint: caiHesenAssistant.apiEndpoint,
              hasApiKey: !!caiHesenAssistant.apiKey,
              appId: caiHesenAssistant.appId || '未设置',
              appCode: caiHesenAssistant.appCode || '未设置'
            })

            setAiConfig({
              apiKey: caiHesenAssistant.apiKey,
              apiEndpoint: caiHesenAssistant.apiEndpoint,
              appId: caiHesenAssistant.appId || '',
              appCode: caiHesenAssistant.appCode || ''
            })

            logger.debug('成功加载蔡和森专题助手配置:', caiHesenAssistant.name)
          } else {
            // 如果找不到ID为5的助手，尝试通过名称查找
            const assistantByName = assistants.find(a => a.name.includes('蔡和森'))

            if (assistantByName) {
              logger.debug('通过名称找到蔡和森专题助手:', {
                id: assistantByName.id,
                name: assistantByName.name,
                status: assistantByName.status
              })

              setAiConfig({
                apiKey: assistantByName.apiKey,
                apiEndpoint: assistantByName.apiEndpoint,
                appId: assistantByName.appId || '',
                appCode: assistantByName.appCode || ''
              })

              logger.debug('成功加载蔡和森专题助手配置:', assistantByName.name)
            } else {
              // 如果仍然找不到，设置错误
              logger.error('未找到蔡和森专题助手，请确保数据库中存在ID为5的蔡和森专题助手')
              setAiConfigError('未找到蔡和森专题助手，请联系管理员配置')
            }
          }
        } else {
          setAiConfigError('未找到个人专题助手配置')
          logger.warn('未找到个人专题助手配置')
        }
      } catch (err) {
        logger.error('加载AI助手配置失败:', sanitizeData(err))
        setAiConfigError('加载AI助手配置失败')
      } finally {
        setAiConfigLoading(false)
      }
    }

    if (isLoggedIn) {
      loadAiConfig()
    } else {
      // 未登录状态下，不加载配置，直接设置加载完成
      setAiConfigLoading(false)
    }
  }, [isLoggedIn])

  // 蔡和森的个人数据
  const personData = {
    id: 1, // 使用数字ID，与数据库模型匹配
    name: "蔡和森",
    nameEn: "Cai Hesen",
    birthDate: "1895年3月30日",
    deathDate: "1931年8月4日",
    birthPlace: "上海市（原籍湖南省湘乡县永丰镇，今属双峰县）",
    education: "湖南省立第一师范学校",
    portrait: "/images/cai-hesen.png",
    biography:
      `中国共产党的创立者——蔡和森

蔡和森（1895-1931），中国无产阶级杰出的革命家、中国共产党早期卓越领导人之一，著名政治活动家、理论家、宣传家，1895年3月30日生于上海。1913年进入湖南省立第一师范读书，期间，同毛泽东等人一起组织进步团体新民学会，创办《湘江评论》，参加五四运动。
1919年底赴法国勤工俭学，"猛看猛译"马克思主义著作，认真研究俄国十月革命的经验，成为坚定的马克思主义者；第一次旗帜鲜明地提出"正式成立一个中国共产党"的主张，明确提出共产党是无产阶级革命运动的"发动者、宣传者、先锋队、作战部"；并对中国共产党建党的理论、方针及组织原则也作了系统的阐述，为党的创建和早期党的建设作出重要贡献。
1921年底回国，从事党的理论宣传工作，于1922年中共"二大"上当选中央委员，主办中共机关刊物《向导》，运用马克思列宁主义积极探索中国革命基本问题，对中国革命的性质、任务、前途、中国社会各阶级在革命中的地位和作用等问题，提出了许多正确观点。在党的三大、四大上，他当选为中央局委员，参与中央领导工作。1925年参与领导五卅反帝爱国运动。同年10月，受中共中央委派，赴莫斯科参加共产国际第五届执行委员会第六次扩大会议，会后任中共驻共产国际代表。1927年春回国，在5月举行的中共五届一中全会上当选为中央政治局委员、常委，随后又兼任中共中央秘书长。
在党的"八七会议"上，他支持毛泽东的正确意见，主张土地革命和独立开展武装斗争，为党确立土地革命和武装反抗国民党反动派的总方针起了重要作用。1928年6月至7月在莫斯科举行的中共六大上，蔡和森认真总结土地革命战争初期的经验教训，指出中国社会具有不平衡的特点，阐明在农村开展武装斗争、建立红军、开辟割据局面的可能性，并当选为中央政治局委员、常委，兼任中央宣传部部长。1931年3月，中共中央决定派蔡和森到香港担任两广省委书记，不会说粤语的蔡和森明知危险，但还是义无反顾地投身于其中。他说："干革命，哪里需要就去哪里，不能只考虑个人的安危。"结果在香港出席海员工会时遭叛徒顾顺章指认被捕。8月牺牲于广州军政监狱酷刑之下，终年36岁。`,
    timeline: [
      {
        year: "1895年3月30日",
        event: "出生于上海，后随母亲回到家乡湖南双峰。",
      },
      {
        year: "1913年",
        event: "考入湖南省立第一师范学校，与毛泽东同窗。",
      },
      {
        year: "1916年",
        event: "与毛泽东等人组织进步团体新民学会筹备会，开始探索救国救民道路。",
      },
      {
        year: "1918年4月",
        event: "与毛泽东、何叔衡等人正式成立新民学会，任总务干事。",
      },
      {
        year: "1918年",
        event: "参与创办《湘江评论》，宣传进步思想。",
      },
      {
        year: "1919年",
        event: "积极参与五四运动，开始接触马克思主义。",
      },
      {
        year: "1919年11月",
        event: "赴法国勤工俭学，与向警予结为伉俪，共同研究马克思主义。",
      },
      {
        year: "1920年8月",
        event: '在给毛泽东的信中首次明确提出"明目张胆正式成立一个中国共产党"的主张。',
      },
      {
        year: "1921年",
        event: "在法国组织旅欧中国少年共产党，后改为旅欧中国共产主义青年团。",
      },
      {
        year: "1921年10月",
        event: "从法国归来，参与中央领导工作。",
      },
      {
        year: "1922年",
        event: "在中共二大上当选为中央委员。",
      },
      {
        year: "1923年",
        event: "在中共三大上当选为中央局委员，任中共中央宣传部部长，主编《向导》周报。",
      },
      {
        year: "1925年",
        event: "在中共四大上再次当选为中央局委员，参与领导五卅运动。",
      },
      {
        year: "1927年",
        event: "在中共五届一中全会上当选为中央政治局委员、常委，兼任中共中央秘书长。",
      },
      {
        year: "1927年",
        event: "参与领导南昌起义，任中共前敌委员会委员。",
      },
      {
        year: "1928年",
        event: "妻子向警予在武汉被捕牺牲，他悲痛之余撰写《向警予同志传》。",
      },
      {
        year: "1928年11月",
        event: "在中央政治局会议上被解除中央政治局委员和常委职务。",
      },
      {
        year: "1931年",
        event: "在组织广州地下工人运动时遭叛徒出卖被捕。",
      },
      {
        year: "1931年8月4日",
        event: "在广州军政监狱牺牲，年仅36岁。",
      },
    ],
    // 添加详细时间线数据
    detailedTimeline: [
      {
        year: 1919,
        title: "赴法勤工俭学，开始接触马克思主义",
        months: [
          {
            month: 11,
            title: "赴法勤工俭学",
            days: [
              {
                day: 5,
                content: "蔡和森与向警予等人从上海出发，乘船前往法国勤工俭学。这次旅程标志着他革命生涯的重要转折点。",
              },
              {
                day: 15,
                content: "抵达马赛港，开始了在法国的生活。初到异国他乡，面对语言不通和生活困难，但革命信念坚定不移。",
              },
              { day: 20, content: "抵达巴黎，开始在当地工厂打工，同时利用业余时间学习法语和马克思主义理论。" },
              {
                day: 28,
                content: "与其他中国留学生一起参加了在巴黎举行的纪念十月革命二周年活动，进一步了解社会主义革命理论。",
              },
            ],
          },
          {
            month: 12,
            title: "初到法国，开始学习和工作",
            days: [
              { day: 3, content: "在巴黎郊区一家橡胶厂找到工作，开始了艰苦的勤工俭学生活。" },
              { day: 10, content: "与在法国的其他中国留学生一起组织学习小组，共同研读马克思主义著作。" },
              { day: 15, content: "写信给国内的毛泽东，详细描述了在法国的见闻和对社会主义思想的新认识。" },
              { day: 25, content: "参加了巴黎中国留学生举办的圣诞联欢会，与更多志同道合的朋友建立联系。" },
            ],
          },
        ],
      },
      {
        year: 1920,
        title: "深入研究马克思主义，提出建党主张",
        months: [
          {
            month: 1,
            title: "积极参与留法学生活动",
            days: [
              { day: 5, content: "与旅法华人一起组织了新年联欢会，讨论中国革命前途。" },
              { day: 15, content: "开始系统阅读《共产党宣言》和列宁的著作，思想上有了质的飞跃。" },
              { day: 25, content: "与向警予一起参加了巴黎的工人示威游行，亲身体验了西方工人运动。" },
            ],
          },
          {
            month: 6,
            title: "思想逐渐成熟",
            days: [
              { day: 10, content: "在巴黎与李立三、周恩来等人多次讨论马克思主义理论与中国革命实践问题。" },
              { day: 20, content: "开始撰写关于建立中国共产党的构想，思考中国革命的组织形式问题。" },
            ],
          },
          {
            month: 8,
            title: "提出建党主张",
            days: [
              {
                day: 10,
                content:
                  '给毛泽东写信，首次明确提出"明目张胆正式成立一个中国共产党"的主张，成为中国共产党名称的首倡者。这封信对中国共产党的创建产生了重要影响。',
              },
              { day: 15, content: "继续深入研究马克思列宁主义，并将研究成果与中国实际相结合。" },
              { day: 25, content: "与法国共产党人建立联系，了解国际共产主义运动的最新发展。" },
            ],
          },
          {
            month: 12,
            title: "组织旅欧中国少年共产党",
            days: [
              { day: 5, content: "与旅法进步青年一起筹备组建旅欧中国少年共产党。" },
              { day: 20, content: "参与起草旅欧中国少年共产党的章程和纲领。" },
              { day: 31, content: "在巴黎举行年终总结会，回顾一年来的思想进步和组织工作成果。" },
            ],
          },
        ],
      },
      {
        year: 1921,
        title: "组建旅欧共产主义组织，为建党做准备",
        months: [
          {
            month: 3,
            title: "组织工作深入开展",
            days: [
              { day: 10, content: "正式成立旅欧中国少年共产党，蔡和森担任重要职务。" },
              { day: 20, content: "组织留法学生学习马克思主义理论，培养革命骨干。" },
            ],
          },
          {
            month: 7,
            title: "中国共产党成立",
            days: [
              { day: 1, content: "收到中国共产党在上海成立的消息，欣喜若狂，立即写信表示祝贺。" },
              { day: 15, content: "组织旅法中国留学生学习中国共产党的纲领和章程。" },
            ],
          },
          {
            month: 10,
            title: "回国参与党的工作",
            days: [
              { day: 5, content: "从法国启程回国，准备直接参与中国共产党的工作。" },
              { day: 25, content: "抵达上海，立即与陈独秀等党的领导人联系，汇报旅欧工作情况。" },
            ],
          },
        ],
      },
      {
        year: 1931,
        title: "英勇就义",
        months: [
          {
            month: 7,
            title: "被捕",
            days: [
              { day: 25, content: "在广州组织工人运动时，不幸被叛徒出卖，遭国民党特务逮捕。" },
              { day: 28, content: "在狱中受尽酷刑，但坚贞不屈，没有透露任何组织秘密。" },
            ],
          },
          {
            month: 8,
            title: "牺牲",
            days: [
              {
                day: 4,
                content:
                  "在广州军政监狱英勇就义，年仅36岁。蔡和森的牺牲是中国共产党和中国革命的重大损失，但他的革命精神和理论贡献永远铭刻在中国革命的历史丰碑上。",
              },
            ],
          },
        ],
      },
    ],
    materials: [
      {
        id: 1,
        title: "《建党问题》信件",
        description: "1920年蔡和森写给毛泽东的信，首次明确提出建立中国共产党的主张，是中共党史的重要文献。",
        image: "/placeholder.svg?height=200&width=300",
      },
      {
        id: 2,
        title: "《向警予同志传》",
        description: "蔡和森为纪念牺牲的妻子向警予所写的传记，展现了革命伴侣的深厚感情和崇高革命精神。",
        image: "/placeholder.svg?height=200&width=300",
      },
      {
        id: 3,
        title: "《湘江评论》创刊号",
        description: "蔡和森参与创办的进步刊物，宣传新思想、新文化，对五四运动产生了重要影响。",
        image: "/placeholder.svg?height=200&width=300",
      },
      {
        id: 4,
        title: "《向导》周报合集",
        description: "蔡和森担任中共中央宣传部部长期间主编的党报，是传播马克思主义和党的方针政策的重要阵地。",
        image: "/placeholder.svg?height=200&width=300",
      },
    ],
    comments: [
      {
        id: 1,
        user: "陈思远",
        content: "感谢分享这些珍贵的历史资料！蔡和森同志对中国共产党的创建有着不可磨灭的贡献。",
        time: "2分钟前",
      },
      {
        id: 2,
        user: "林雨清",
        content: "作为新时代的青年，我们要继承蔡和森同志的革命精神和理论探索精神。",
        time: "5分钟前",
      },
      {
        id: 3,
        user: "张文博",
        content: "蔡和森同志的理论贡献和革命实践为中国革命事业奠定了重要基础。",
        time: "8分钟前",
      },
      {
        id: 4,
        user: "吴梓萱",
        content: "通过这些资料，让我更深入地了解了蔡和森同志的生平和对党的贡献。",
        time: "12分钟前",
      },
    ],
  }

  return (
    <div className="min-h-screen bg-[#fdf9f1]">
      <Navbar />
      <div className="flex flex-col md:flex-row pt-16">
        {/* 左侧AI助手区域 - 固定宽度40% */}
        <div className="w-full md:w-[40%] h-[70vh] md:h-[calc(100vh-64px)] md:fixed md:top-16 md:left-0 border-r border-[#1e7a43]/20">
          {aiConfigLoading ? (
            <div className="text-center p-4">
              <p>正在加载AI助手配置...</p>
            </div>
          ) : aiConfigError && isLoggedIn ? (
            <div className="text-center p-4 text-red-500">
              <p>{aiConfigError}</p>
              <p className="text-sm mt-2">请联系管理员在系统设置-AI管理中配置个人专题助手</p>
            </div>
          ) : (
            <PersonalAssistant
              id="cai-hesen-assistant"
              name={personData.name}
              personalId={personData.id}
              description="蔡和森专题研究助手，可以回答关于蔡和森生平、思想和贡献的问题"
              tags={["个人专题", "蔡和森", "中共创建"]}
              isNew={false}
              isPopular={true}
              enabled={true}
              apiKey={isLoggedIn ? aiConfig?.apiKey : undefined}
              apiEndpoint={isLoggedIn ? aiConfig?.apiEndpoint : undefined}
              appId={isLoggedIn ? aiConfig?.appId : undefined}
              appCode={isLoggedIn ? aiConfig?.appCode : undefined}
            />
          )}
        </div>

        {/* 右侧内容区域 - 宽度60%，可滚动 */}
        <div className="w-full md:w-[60%] md:ml-[40%]">
          <PersonalProfile person={personData} isLoggedIn={isLoggedIn} />
        </div>
      </div>
    </div>
  )
}
