// 葛健豪个人专题页面
"use client"

import { useEffect, useState } from "react"
import { Navbar } from "@/components/navbar"
import { PersonalProfile } from "@/components/personal-profile"
import { PersonalAssistant } from "@/components/personal/PersonalAssistant"
import { logger, sanitizeData } from "@/utils/logger"
export default function GeJianhaoPage() {
  // 登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  // 检查登录状态
  useEffect(() => {
    const checkLoginStatus = () => {
      // 检查token是否存在，这是真正的登录凭证
      const token = localStorage.getItem("hefamily_token");
      const loggedIn = !!token && localStorage.getItem("isLoggedIn") === "true";

      console.log("检查登录状态:", {
        hasToken: !!token,
        isLoggedInFlag: localStorage.getItem("isLoggedIn") === "true",
        finalLoginState: loggedIn
      });

      setIsLoggedIn(loggedIn);
    }

    checkLoginStatus();
    // 监听登录状态变化
    window.addEventListener("storage", checkLoginStatus);

    return () => {
      window.removeEventListener("storage", checkLoginStatus);
    }
  }, [])
  const [aiConfig, setAiConfig] = useState<any>(null)
  const [aiConfigLoading, setAiConfigLoading] = useState(true)
  const [aiConfigError, setAiConfigError] = useState<string | null>(null)

  // 加载AI助手配置
  useEffect(() => {
    const loadAiConfig = async () => {
      try {
        setAiConfigLoading(true)
        setAiConfigError(null)

        // 导入AI助手服务
        const aiAssistantService = await import('@/services/ai-assistant-service')

        // 获取个人专题助手配置
        const assistants = await aiAssistantService.getAIAssistants('personal')
        logger.debug('获取到的所有个人专题助手:', assistants.map(a => ({ id: a.id, name: a.name, status: a.status })))

        if (assistants && assistants.length > 0) {
          // 直接查找ID为4的葛健豪专题助手
          const geJianhaoAssistant = assistants.find(a => a.id === 4)

          if (geJianhaoAssistant) {
            logger.debug('找到葛健豪专题助手(ID=4):', {
              id: geJianhaoAssistant.id,
              name: geJianhaoAssistant.name,
              status: geJianhaoAssistant.status,
              apiEndpoint: geJianhaoAssistant.apiEndpoint,
              hasApiKey: !!geJianhaoAssistant.apiKey,
              appId: geJianhaoAssistant.appId || '未设置',
              appCode: geJianhaoAssistant.appCode || '未设置'
            })

            setAiConfig({
              apiKey: geJianhaoAssistant.apiKey,
              apiEndpoint: geJianhaoAssistant.apiEndpoint,
              appId: geJianhaoAssistant.appId || '',
              appCode: geJianhaoAssistant.appCode || ''
            })

            logger.debug('成功加载葛健豪专题助手配置:', geJianhaoAssistant.name)
          } else {
            // 如果找不到ID为4的助手，尝试通过名称查找
            const assistantByName = assistants.find(a => a.name.includes('葛健豪'))

            if (assistantByName) {
              logger.debug('通过名称找到葛健豪专题助手:', {
                id: assistantByName.id,
                name: assistantByName.name,
                status: assistantByName.status
              })

              setAiConfig({
                apiKey: assistantByName.apiKey,
                apiEndpoint: assistantByName.apiEndpoint,
                appId: assistantByName.appId || '',
                appCode: assistantByName.appCode || ''
              })

              logger.debug('成功加载葛健豪专题助手配置:', assistantByName.name)
            } else {
              // 如果仍然找不到，设置错误
              logger.error('未找到葛健豪专题助手，请确保数据库中存在ID为4的葛健豪专题助手')
              setAiConfigError('未找到葛健豪专题助手，请联系管理员配置')
            }
          }
        } else {
          setAiConfigError('未找到个人专题助手配置')
          logger.warn('未找到个人专题助手配置')
        }
      } catch (err) {
        logger.error('加载AI助手配置失败:', sanitizeData(err))
        setAiConfigError('加载AI助手配置失败')
      } finally {
        setAiConfigLoading(false)
      }
    }

    if (isLoggedIn) {
      loadAiConfig()
    } else {
      // 未登录状态下，不加载配置，直接设置加载完成
      setAiConfigLoading(false)
    }
  }, [isLoggedIn])

  // 葛健豪的个人数据
  const personData = {
    id: 4, // 使用数字ID，与数据库模型匹配
    name: "葛健豪",
    nameEn: "Ge Jianhao",
    birthDate: "1865年",
    deathDate: "1943年",
    birthPlace: "湖南省湘乡县（今双峰县）",
    education: "私塾教育",
    portrait: "/images/ge-jianhao.png",
    biography:
      `革命的母亲——葛健豪

葛健豪（1865-1943），湖南省湘乡县（今双峰县）人，是蔡和森和蔡畅的母亲。自幼敢于冲破封建桎梏，倡导男女平等，热衷女子教育，是湖南著名的女子教育家；与中国同盟会第一个女委员唐群英和"鉴湖女侠"秋瑾，并称为"女界三杰"。年过五十还去长沙求学，后来又伴子女赴法勤工俭学，被舆论界称誉为廿世纪"惊人的妇人"。

一生始终心系革命，支持毛泽东、蔡和森创办新民学会，不顾个人安危掩护党的地下工作，为党哺育和培养了一批优秀的革命儿女，家中走出了4名中共中央委员：儿子蔡和森、儿媳向警予、女儿蔡畅、女婿李富春。她虽非中共党员，却立传于《中共党史人物传》第六卷，是其中唯一的非中共党内人士。

毛泽东曾评价道："蔡母葛健豪的家教是卓有成效的，她舍小家为大家，教育自己的子女都走上了革命的道路，是我们每个革命同志学习的榜样。"1943年，毛泽东在延安得知葛健豪逝世后，提笔写了"老妇人，新妇道；儿英烈，女英雄"的挽联。`,
    timeline: [
      {
        year: "1865年",
        event: "出生于湖南省双峰县荷叶镇一个湘军将领家庭。",
      },
      {
        year: "1878年",
        event: "10岁时通过哭诉争取到与哥哥一起上学的机会，展现了对知识的渴望。",
      },
      {
        year: "1881年",
        event: "16岁时与蔡蓉峰结婚，这是父亲生前订下的婚约。",
      },
      {
        year: "1890年代",
        event: "与秋瑾结识，受到其思想影响，开始关注社会变革。",
      },
      {
        year: "1913年",
        event: "变卖首饰支持儿子蔡和森去长沙读书，推动子女接受教育。",
      },
      {
        year: "1919年",
        event: "48岁高龄随子女赴法勤工俭学，成为当时年龄最大的留学生。",
      },
      {
        year: "1922年",
        event: "回国后创办平民女子职业学校，宣传女权思想，推动妇女解放。",
      },
      {
        year: "1925年",
        event: "参与湖南妇女解放运动，为革命工作者传递情报。",
      },
      {
        year: "1927年",
        event: "二儿子蔡麓仙在省港罢工中牺牲。",
      },
      {
        year: "1931年",
        event: "三儿子蔡和森在广州牺牲，家人隐瞒消息。",
      },
      {
        year: "1943年",
        event: "在湖南双峰县病逝，享年78岁，至死不知蔡和森和蔡麓仙已牺牲。",
      },
    ],
    // 添加详细时间线数据
    detailedTimeline: [
      {
        year: 1865,
        title: "出生于湖南省双峰县荷叶镇",
        months: [
          {
            month: 3,
            title: "出生",
            days: [
              {
                day: 15,
                content: "葛健豪出生于湖南省双峰县荷叶镇一个湘军将领家庭。她的父亲是湘军中的一名军官，家境较为富裕。",
              },
            ],
          },
          {
            month: 6,
            title: "取名",
            days: [
              {
                day: 5,
                content:
                  "父亲为其取名葛兰英，后改名葛健豪。在当时的封建社会，女子地位低下，但葛健豪的父亲对女儿却寄予厚望，希望她能健康成长。",
              },
            ],
          },
        ],
      },
      {
        year: 1878,
        title: "争取教育权利",
        months: [
          {
            month: 9,
            title: "争取上学",
            days: [
              {
                day: 10,
                content:
                  "10岁的葛健豪看到哥哥可以去私塾读书，而自己却不能，于是通过哭诉向父亲争取到与哥哥一起上学的机会。这在当时是非常罕见的，因为封建社会普遍认为&quot;女子无才便是德&quot;。",
              },
              {
                day: 15,
                content: "葛健豪开始在家乡的私塾读书，学习诗书礼仪。她学习勤奋，记忆力强，很快就掌握了基本的读写能力。",
              },
            ],
          },
          {
            month: 10,
            title: "学习进步",
            days: [
              {
                day: 5,
                content:
                  "葛健豪在私塾学习期间表现出色，引起了先生的赞赏。她对知识的渴望和学习的热情，为她日后支持子女接受教育奠定了思想基础。",
              },
              { day: 20, content: "葛健豪开始学习古典诗词，并尝试自己创作，展现出文学天赋。" },
            ],
          },
        ],
      },
      {
        year: 1881,
        title: "婚姻",
        months: [
          {
            month: 4,
            title: "订婚",
            days: [
              {
                day: 10,
                content:
                  "按照父亲生前的安排，16岁的葛健豪与蔡蓉峰的婚事正式确定。蔡蓉峰是当地一个书香门第的子弟，有一定的文化修养。",
              },
            ],
          },
          {
            month: 10,
            title: "结婚",
            days: [
              {
                day: 15,
                content:
                  "葛健豪与蔡蓉峰举行婚礼，正式成为蔡家的媳妇。婚后，葛健豪勤劳贤惠，很快就得到了婆家的认可和喜爱。",
              },
              { day: 20, content: "葛健豪开始适应新的家庭生活，并逐渐在家中展现出她的才干和智慧。" },
            ],
          },
        ],
      },
      {
        year: 1890,
        title: "接触进步思想",
        months: [
          {
            month: 5,
            title: "结识秋瑾",
            days: [
              {
                day: 10,
                content: "葛健豪通过家族关系结识了著名女革命家秋瑾。秋瑾的女权思想和革命精神对葛健豪产生了深远影响。",
              },
              {
                day: 15,
                content:
                  "葛健豪与秋瑾进行了长谈，了解了更多关于女性解放和社会变革的思想。这次谈话让葛健豪开始思考女性的社会地位和国家的前途命运。",
              },
            ],
          },
          {
            month: 8,
            title: "思想转变",
            days: [
              { day: 5, content: "在秋瑾的影响下，葛健豪开始阅读一些进步书籍，思想逐渐从传统走向现代。" },
              {
                day: 20,
                content: "葛健豪开始关注社会变革和女性解放运动，这为她日后支持子女参与革命活动奠定了思想基础。",
              },
            ],
          },
        ],
      },
      {
        year: 1913,
        title: "支持子女教育",
        months: [
          {
            month: 2,
            title: "蔡和森求学",
            days: [
              {
                day: 10,
                content:
                  "葛健豪的儿子蔡和森表达了去长沙求学的愿望。当时家庭经济条件有限，但葛健豪坚定地支持儿子的求学梦想。",
              },
              {
                day: 15,
                content: "葛健豪决定变卖自己的首饰，为蔡和森筹集学费。这一决定体现了她对教育的重视和对子女前途的关心。",
              },
            ],
          },
          {
            month: 3,
            title: "送别",
            days: [
              { day: 5, content: "葛健豪亲自送蔡和森前往长沙，嘱咐他要刻苦学习，将来报效国家。" },
              {
                day: 10,
                content:
                  "蔡和森在长沙开始了他的求学生涯，这也是他革命道路的起点。葛健豪的支持为中国革命事业培养了一位重要的领导人。",
              },
            ],
          },
        ],
      },
      {
        year: 1919,
        title: "赴法勤工俭学",
        months: [
          {
            month: 10,
            title: "决定赴法",
            days: [
              {
                day: 5,
                content:
                  "在子女的影响下，48岁高龄的葛健豪决定随子女一起赴法国勤工俭学。这一决定在当时是非常罕见的，因为她的年龄已经相当大了。",
              },
              {
                day: 15,
                content: "葛健豪开始准备赴法所需的各种文件和物品，并学习一些基本的法语。她的决心和勇气令人敬佩。",
              },
            ],
          },
          {
            month: 11,
            title: "启程赴法",
            days: [
              {
                day: 10,
                content:
                  "葛健豪与子女蔡和森、蔡畅及儿媳向警予一同从上海启程，乘船前往法国。这次旅程标志着葛健豪家族革命历程的重要转折点。",
              },
              {
                day: 30,
                content:
                  "经过漫长的海上旅程，葛健豪一行人抵达法国马赛港。作为当时年龄最大的留学生，葛健豪的到来引起了不小的关注。",
              },
            ],
          },
          {
            month: 12,
            title: "在法国生活",
            days: [
              { day: 5, content: "葛健豪一家在巴黎安顿下来，开始了艰苦的勤工俭学生活。" },
              {
                day: 15,
                content: "尽管年事已高，葛健豪仍然积极适应法国的生活，并开始学习法语。她的坚韧精神成为子女们的榜样。",
              },
              {
                day: 25,
                content:
                  "葛健豪参观了巴黎的一些文化场所，对西方文明有了更直观的认识。这些经历拓宽了她的视野，也影响了她后来的教育理念。",
              },
            ],
          },
        ],
      },
      {
        year: 1922,
        title: "回国创办女子学校",
        months: [
          {
            month: 3,
            title: "回国",
            days: [
              {
                day: 10,
                content: "结束在法国的生活，葛健豪回到中国。在法国的经历使她对女性教育和社会变革有了更深刻的认识。",
              },
              {
                day: 25,
                content: "葛健豪回到湖南家乡，开始筹备创办女子学校的计划。她希望通过教育来改变中国女性的命运。",
              },
            ],
          },
          {
            month: 9,
            title: "创办学校",
            days: [
              { day: 1, content: "葛健豪在湖南创办平民女子职业学校，这是她践行女性教育理念的重要实践。" },
              {
                day: 10,
                content: "学校正式开学，招收了第一批女学生。葛健豪亲自参与教学工作，传授知识技能，同时宣传女权思想。",
              },
              { day: 20, content: "葛健豪在学校举办讲座，向学生们介绍西方女性的生活和权利，鼓励她们追求独立和自由。" },
            ],
          },
        ],
      },
      {
        year: 1925,
        title: "参与妇女解放运动",
        months: [
          {
            month: 5,
            title: "参与运动",
            days: [
              { day: 4, content: "葛健豪积极参与湖南妇女解放运动，宣传女性平等和解放思想。" },
              { day: 15, content: "葛健豪组织女校学生参加当地的妇女集会，支持女性争取平等权利的斗争。" },
            ],
          },
          {
            month: 8,
            title: "支持革命",
            days: [
              { day: 10, content: "葛健豪开始为地下革命工作者传递情报，冒着危险支持革命事业。" },
              { day: 20, content: "葛健豪的家成为革命者的秘密联络点，她以母亲的身份掩护革命活动。" },
            ],
          },
        ],
      },
      {
        year: 1927,
        title: "家族牺牲",
        months: [
          {
            month: 6,
            title: "蔡麓仙牺牲",
            days: [
              { day: 15, content: "葛健豪的二儿子蔡麓仙在省港罢工中被捕。" },
              { day: 20, content: "传来蔡麓仙牺牲的消息，葛健豪悲痛欲绝，但仍然坚强地支持其他子女继续革命工作。" },
            ],
          },
          {
            month: 7,
            title: "坚强面对",
            days: [
              { day: 5, content: "葛健豪强忍丧子之痛，继续支持革命事业，表现出非凡的革命母亲的品质。" },
              { day: 15, content: "葛健豪写信给其他子女，鼓励他们坚持革命理想，不要被挫折吓倒。" },
            ],
          },
        ],
      },
      {
        year: 1931,
        title: "蔡和森牺牲",
        months: [
          {
            month: 8,
            title: "噩耗",
            days: [
              { day: 4, content: "葛健豪的三儿子蔡和森在广州被捕后英勇就义。" },
              { day: 10, content: "家人决定隐瞒蔡和森牺牲的消息，怕年迈的葛健豪承受不了接连失去儿子的打击。" },
            ],
          },
          {
            month: 9,
            title: "思念",
            days: [
              { day: 5, content: "葛健豪不知道蔡和森已经牺牲，仍然经常询问他的消息，家人只能编造各种理由搪塞。" },
              {
                day: 15,
                content:
                  "葛健豪写信给&quot;远方的蔡和森&quot;，表达对儿子的思念和期望，这些信件被家人珍藏起来，成为珍贵的历史资料。",
              },
            ],
          },
        ],
      },
      {
        year: 1943,
        title: "逝世",
        months: [
          {
            month: 5,
            title: "病重",
            days: [
              { day: 10, content: "葛健豪在湖南双峰县病重，家人轮流照顾。" },
              { day: 15, content: "病榻上的葛健豪仍然惦记着远方的儿子们，不知道蔡和森和蔡麓仙已经为革命牺牲。" },
            ],
          },
          {
            month: 6,
            title: "离世",
            days: [
              {
                day: 5,
                content: "葛健豪在湖南双峰县病逝，享年78岁。至死她都不知道自己的两个儿子已经为革命事业献出了生命。",
              },
              { day: 10, content: "葛健豪的葬礼在家乡举行，当地民众自发前来悼念这位伟大的革命母亲。" },
              {
                day: 15,
                content:
                  "葛健豪的一生培养了多位杰出的革命家，包括蔡和森、蔡畅、向警予和李富春等，她对中国革命的特殊贡献被后人铭记。她也是唯一以非党员身份被收录于《中共党史人物传》的杰出女性。",
              },
            ],
          },
        ],
      },
    ],
    materials: [
      {
        id: 1,
        title: "革命母亲的家书",
        description: "收录了葛健豪与子女们的往来书信，展现了她对子女革命事业的支持和关心。",
        image: "/placeholder.svg?height=200&width=300",
      },
      {
        id: 2,
        title: "葛健豪与子女的合影",
        description: "珍贵的历史照片，记录了葛健豪与蔡和森、蔡畅等子女在法国勤工俭学时期的生活。",
        image: "/placeholder.svg?height=200&width=300",
      },
    ],
    comments: [
      {
        id: 1,
        user: "张明",
        content: "葛健豪老人的故事令人感动，她是中国革命史上不可忽视的重要人物！",
        time: "5分钟前",
      },
      {
        id: 2,
        user: "李华",
        content: "作为一位母亲，她对子女革命事业的支持和牺牲精神值得我们学习。",
        time: "12分钟前",
      },
      {
        id: 3,
        user: "王强",
        content: "希望能有更多关于葛健豪老人的历史资料被发掘和整理。",
        time: "18分钟前",
      },
      {
        id: 4,
        user: "陈静",
        content: "革命母亲的称号实至名归，她培养的革命家们为中国革命做出了巨大贡献。",
        time: "25分钟前",
      },
    ],
  }

  return (
    <div className="min-h-screen bg-[#fdf9f1]">
      <Navbar />
      <div className="flex flex-col md:flex-row pt-16">
        {/* 左侧AI助手区域 - 固定宽度40% */}
        <div className="w-full md:w-[40%] h-[70vh] md:h-[calc(100vh-64px)] md:fixed md:top-16 md:left-0 border-r border-[#1e7a43]/20">
          {aiConfigLoading ? (
            <div className="text-center p-4">
              <p>正在加载AI助手配置...</p>
            </div>
          ) : aiConfigError && isLoggedIn ? (
            <div className="text-center p-4 text-red-500">
              <p>{aiConfigError}</p>
              <p className="text-sm mt-2">请联系管理员在系统设置-AI管理中配置个人专题助手</p>
            </div>
          ) : (
            <PersonalAssistant
              id="ge-jianhao-assistant"
              name={personData.name}
              personalId={personData.id}
              description="葛健豪专题研究助手，可以回答关于葛健豪生平、思想和贡献的问题"
              tags={["个人专题", "葛健豪", "革命母亲"]}
              isNew={false}
              isPopular={true}
              enabled={true}
              apiKey={isLoggedIn ? aiConfig?.apiKey : undefined}
              apiEndpoint={isLoggedIn ? aiConfig?.apiEndpoint : undefined}
              appId={isLoggedIn ? aiConfig?.appId : undefined}
              appCode={isLoggedIn ? aiConfig?.appCode : undefined}
            />
          )}
        </div>

        {/* 右侧内容区域 - 宽度60%，可滚动 */}
        <div className="w-full md:w-[60%] md:ml-[40%]">
          <PersonalProfile person={personData} isLoggedIn={isLoggedIn} />
        </div>
      </div>
    </div>
  )
}
