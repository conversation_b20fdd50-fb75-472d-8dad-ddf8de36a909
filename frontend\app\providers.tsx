'use client'

import { ThemeProvider } from '@/components/theme-provider'
import { AuthProvider } from '@/contexts/auth-context'
import { PermissionProvider } from '@/contexts/permission-context'
import { Toaster } from '@/components/ui/toaster'
import { ModalProvider } from '@/components/modal-provider'
import { PageTransition } from '@/components/page-transition'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import { initConsoleInterceptor } from '@/utils/console-interceptor'

export function Providers({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // 初始化控制台拦截器，在生产环境中禁用所有控制台输出
  useEffect(() => {
    // 初始化控制台拦截器
    initConsoleInterceptor()
  }, [])

  // 监听路由变化，触发自定义事件
  useEffect(() => {
    // 触发路由变化完成事件
    window.dispatchEvent(new Event('route-change-complete'))

    // 滚动到页面顶部
    window.scrollTo(0, 0)
  }, [pathname, searchParams])

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      disableTransitionOnChange
      forcedTheme="light"
    >
      <AuthProvider>
        <PermissionProvider>
          <ModalProvider>
            <PageTransition>
              {children}
            </PageTransition>
            <Toaster />
          </ModalProvider>
        </PermissionProvider>
      </AuthProvider>
    </ThemeProvider>
  )
}
