"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { X } from "lucide-react"
import { PermissionCheckbox } from "@/components/permission-checkbox"
import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import axios from "axios"
import { setRolePermissions } from "@/utils/role-permission-utils"
import { mapPermissionsToBackend, mapPermissionsToFrontend } from "@/utils/permission-mapping"
import { getPermissionName } from "@/utils/permission-names"

interface RolePermissionModalProps {
  roleToSetPermission: any
  setShowRolePermissionModal: (show: boolean) => void
  setSuccessMessage: (message: string) => void
  setShowSuccessToast: (show: boolean) => void
  API_BASE_URL: string
}

/**
 * 角色权限设置模态框组件
 *
 * 用于设置角色的权限
 *
 * @param roleToSetPermission 要设置权限的角色
 * @param setShowRolePermissionModal 设置是否显示模态框的函数
 * @param setSuccessMessage 设置成功消息的函数
 * @param setShowSuccessToast 设置是否显示成功提示的函数
 * @param API_BASE_URL API基础URL
 */
export function RolePermissionModal({
  roleToSetPermission,
  setShowRolePermissionModal,
  setSuccessMessage,
  setShowSuccessToast,
  API_BASE_URL
}: RolePermissionModalProps) {
  const [loading, setLoading] = useState(false)
  const [availablePermissions, setAvailablePermissions] = useState<string[]>([])
  const { toast } = useToast()

  /**
   * 获取所有权限
   *
   * @returns 所有权限列表
   */
  const getAllPermissions = async () => {
    try {
      const token = localStorage.getItem('hefamily_token');
      const response = await axios.get(`${API_BASE_URL}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('获取所有权限响应:', response.data);

      // 输出所有权限代码，帮助调试
      if (response.data && response.data.data && response.data.data.length > 0) {
        // 过滤掉测试权限和未授权权限
        const filteredPermissions = response.data.data.filter(p =>
          !p.code.startsWith('test:permission:') &&
          !p.code.startsWith('unauthorized:permission:') &&
          !p.code.startsWith('permission:to:delete:') &&
          !p.code.startsWith('user:read:') &&
          !p.code.startsWith('user:write:') &&
          !p.code.startsWith('knowledge:read:')
        );

        // 按照模块分组权限
        const permissionsByModule = {};
        filteredPermissions.forEach(p => {
          if (!permissionsByModule[p.module]) {
            permissionsByModule[p.module] = [];
          }
          permissionsByModule[p.module].push(p);
        });

        console.log('按模块分组的权限:', permissionsByModule);

        // 获取所有权限代码并排序
        const permissionCodes = filteredPermissions.map(p => p.code).sort();
        console.log('过滤后的系统权限代码:', permissionCodes);

        // 移除重复的权限代码
        const uniquePermissionCodes = [...new Set(permissionCodes)];
        setAvailablePermissions(uniquePermissionCodes);

        // 返回过滤后的权限列表
        return filteredPermissions;
      }

      return response.data.data || [];
    } catch (error) {
      console.error('获取所有权限失败:', error);
      return [];
    }
  };

  // 在组件加载时获取所有可用权限和角色当前权限
  useEffect(() => {
    const fetchData = async () => {
      // 获取所有可用权限
      await getAllPermissions();

      // 等待权限加载完成后，再触发权限更新事件
      setTimeout(() => {
        try {
          // 获取角色当前权限代码
          const backendPermissionCodes = roleToSetPermission.permissions?.map(p => p.code) || [];
          console.log('初始化角色后端权限代码:', backendPermissionCodes);

          // 将后端权限代码映射到前端权限代码
          import('@/utils/permission-mapping').then(({ mapPermissionsToFrontend }) => {
            const frontendPermissionCodes = mapPermissionsToFrontend(backendPermissionCodes);
            console.log('映射后的前端权限代码:', frontendPermissionCodes);

            // 使用自定义事件通知所有权限复选框更新状态
            const event = new CustomEvent('permission-update', {
              detail: { permissionCodes: frontendPermissionCodes }
            });
            window.dispatchEvent(event);
          });
        } catch (error) {
          console.error('初始化权限状态失败:', error);
        }
      }, 500); // 给一点延迟，确保权限复选框已经渲染
    };

    fetchData();
  }, [roleToSetPermission]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg w-full max-w-4xl p-6 relative max-h-[90vh] overflow-y-auto mx-4">
        <button
          onClick={() => setShowRolePermissionModal(false)}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
        >
          <X className="h-5 w-5" />
        </button>

        <h2 className="text-xl font-bold mb-4">设置角色权限</h2>
        <p className="mb-4">
          为角色 <span className="font-bold">{roleToSetPermission.name}</span> 设置权限
        </p>

        <div className="space-y-6">
          {/* 权限模块 */}
          <div>
            <h3 className="text-md font-medium mb-2 bg-gray-50 p-2 rounded">系统权限</h3>

            {availablePermissions.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                正在加载权限数据...
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-2 pl-2">
                {/* 基础页面权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">基础页面</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("home:access") && (
                      <PermissionCheckbox code="home:access" label="访问首页" />
                    )}
                    {availablePermissions.includes("family:access") && (
                      <PermissionCheckbox code="family:access" label="访问家族专题" />
                    )}
                    {availablePermissions.includes("activity:view") && (
                      <PermissionCheckbox code="activity:view" label="查看活动" />
                    )}
                    {availablePermissions.includes("activity:manage") && (
                      <PermissionCheckbox code="activity:manage" label="管理活动" />
                    )}
                  </div>
                </div>

                {/* 系统管理权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">系统管理</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("system:access") && (
                      <PermissionCheckbox code="system:access" label="访问系统管理" />
                    )}
                    {availablePermissions.includes("user:manage") && (
                      <PermissionCheckbox code="user:manage" label="用户管理" />
                    )}
                    {availablePermissions.includes("role:manage") && (
                      <PermissionCheckbox code="role:manage" label="角色管理" />
                    )}
                    {availablePermissions.includes("ai:manage") && (
                      <PermissionCheckbox code="ai:manage" label="AI管理" />
                    )}
                    {availablePermissions.includes("security:manage") && (
                      <PermissionCheckbox code="security:manage" label="安全管理" />
                    )}
                    {availablePermissions.includes("permission:manage") && (
                      <PermissionCheckbox code="permission:manage" label="权限管理" />
                    )}
                  </div>
                </div>

                {/* 知识库权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">知识库</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("knowledge:access") && (
                      <PermissionCheckbox code="knowledge:access" label="访问知识库" />
                    )}
                    {availablePermissions.includes("knowledge:manage") && (
                      <PermissionCheckbox code="knowledge:manage" label="管理知识库" />
                    )}
                    {availablePermissions.includes("knowledge:create_user") && (
                      <PermissionCheckbox code="knowledge:create_user" label="创建用户知识库" />
                    )}
                    {availablePermissions.includes("knowledge:create_system") && (
                      <PermissionCheckbox code="knowledge:create_system" label="创建系统知识库" />
                    )}
                    {availablePermissions.includes("file:upload") && (
                      <PermissionCheckbox code="file:upload" label="上传文件" />
                    )}
                    {availablePermissions.includes("file:download") && (
                      <PermissionCheckbox code="file:download" label="下载文件" />
                    )}
                    {availablePermissions.includes("file:manage") && (
                      <PermissionCheckbox code="file:manage" label="管理文件" />
                    )}
                    {availablePermissions.includes("file:review") && (
                      <PermissionCheckbox code="file:review" label="审核文件" />
                    )}
                  </div>
                </div>

                {/* 个人专题权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">个人专题</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("personal:access") && (
                      <PermissionCheckbox code="personal:access" label="访问个人专题" />
                    )}
                    {availablePermissions.includes("personal:ai_use") && (
                      <PermissionCheckbox code="personal:ai_use" label="使用个人专题AI" />
                    )}
                    {availablePermissions.includes("personal:manage_materials") && (
                      <PermissionCheckbox code="personal:manage_materials" label="管理个人资料" />
                    )}
                  </div>
                </div>

                {/* 数据查询权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">数据查询</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("data:access") && (
                      <PermissionCheckbox code="data:access" label="访问数据查询" />
                    )}
                    {availablePermissions.includes("data:ai_query") && (
                      <PermissionCheckbox code="data:ai_query" label="使用数据查询AI" />
                    )}
                  </div>
                </div>

                {/* AI研究助手权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">AI研究助手</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("assistant:use") && (
                      <PermissionCheckbox code="assistant:use" label="使用AI研究助手" />
                    )}
                  </div>
                </div>

                {/* 内容管理权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">内容管理</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("content:view") && (
                      <PermissionCheckbox code="content:view" label="查看消息" />
                    )}
                    {availablePermissions.includes("content:publish") && (
                      <PermissionCheckbox code="content:publish" label="发布消息" />
                    )}
                    {availablePermissions.includes("content:manage") && (
                      <PermissionCheckbox code="content:manage" label="管理消息" />
                    )}
                  </div>
                </div>

                {/* 其他权限 */}
                <div className="col-span-2">
                  <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">其他权限</h4>
                  <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                    {availablePermissions.includes("comment:manage") && (
                      <PermissionCheckbox code="comment:manage" label="管理评论" />
                    )}
                    {availablePermissions.includes("notification:manage") && (
                      <PermissionCheckbox code="notification:manage" label="管理通知" />
                    )}
                    {availablePermissions.includes("timeline:manage") && (
                      <PermissionCheckbox code="timeline:manage" label="管理时间线" />
                    )}
                  </div>
                </div>

                {/* 显示所有未分类的权限 */}
                {availablePermissions.filter(code =>
                  ![
                    // 基础页面权限
                    "home:access", "family:access", "activity:view", "activity:manage",
                    // 系统管理权限
                    "system:access", "system:config", "user:manage", "role:manage", "ai:manage", "security:manage", "permission:manage",
                    // 知识库权限
                    "knowledge:access", "knowledge:manage", "knowledge:create_user", "knowledge:create_system",
                    "file:upload", "file:download", "file:manage", "file:review",
                    // 个人专题权限
                    "personal:access", "personal:ai_use", "personal:manage_materials",
                    // 数据查询权限
                    "data:access", "data:ai_query",
                    // AI研究助手权限
                    "assistant:use",
                    // 内容管理权限
                    "content:view", "content:publish", "content:manage",
                    // 其他权限
                    "comment:manage", "notification:manage", "timeline:manage",
                    // 排除测试权限
                    "new:permission:9487"
                  ].includes(code)
                ).length > 0 && (
                  <div className="col-span-2">
                    <h4 className="text-sm font-medium mb-1 text-gray-700 bg-gray-100 p-1 rounded">未分类权限</h4>
                    <div className="grid grid-cols-2 gap-2 pl-2 mb-4">
                      {availablePermissions.filter(code =>
                        ![
                          // 基础页面权限
                          "home:access", "family:access", "activity:view", "activity:manage",
                          // 系统管理权限
                          "system:access", "system:config", "user:manage", "role:manage", "ai:manage", "security:manage", "permission:manage",
                          // 知识库权限
                          "knowledge:access", "knowledge:manage", "knowledge:create_user", "knowledge:create_system",
                          "file:upload", "file:download", "file:manage", "file:review",
                          // 个人专题权限
                          "personal:access", "personal:ai_use", "personal:manage_materials",
                          // 数据查询权限
                          "data:access", "data:ai_query",
                          // AI研究助手权限
                          "assistant:use",
                          // 内容管理权限
                          "content:view", "content:publish", "content:manage",
                          // 其他权限
                          "comment:manage", "notification:manage", "timeline:manage",
                          // 排除测试权限
                          "new:permission:9487"
                        ].includes(code)
                      ).map(code => {
                        // 使用权限名称映射工具获取友好的中文名称
                        const label = getPermissionName(code);

                        return (
                          <PermissionCheckbox key={code} code={code} label={label} />
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={() => setShowRolePermissionModal(false)}>
              取消
            </Button>
            <Button
              className="bg-[#f5a623] hover:bg-[#f5a623]/90"
              onClick={async () => {
                setLoading(true);

                try {
                  // 先检查角色是否存在
                  try {
                    // 尝试获取角色信息
                    const roleResponse = await axios.get(`${API_BASE_URL}/roles/${roleToSetPermission.id}`, {
                      headers: {
                        'Authorization': `Bearer ${localStorage.getItem('hefamily_token')}`
                      }
                    });

                    console.log('角色信息:', roleResponse.data);

                    // 如果角色不存在，API会返回404错误，不会执行到这里
                  } catch (roleError) {
                    if (roleError.response && roleError.response.status === 404) {
                      // 角色不存在
                      toast({
                        title: "角色不存在",
                        description: `角色ID=${roleToSetPermission.id}在系统中不存在，无法设置权限`,
                        variant: "destructive"
                      });
                      setLoading(false);
                      setShowRolePermissionModal(false);
                      return;
                    }
                    // 其他错误继续执行
                  }

                  // 创建一个函数来获取所有选中的权限复选框
                  const getSelectedPermissions = () => {
                    // 使用一个临时数组来存储选中的权限代码
                    const selectedCodes: string[] = [];

                    // 从全局权限复选框列表中获取选中的权限
                    if (typeof window !== 'undefined' && window.permissionCheckboxes) {
                      Object.entries(window.permissionCheckboxes).forEach(([code, checkbox]) => {
                        if (checkbox.isChecked()) {
                          selectedCodes.push(code);
                        }
                      });
                    }

                    // 如果全局列表为空，尝试从DOM中获取
                    if (selectedCodes.length === 0) {
                      // 查询所有权限复选框
                      const checkboxes = document.querySelectorAll('input[name="permission-checkbox"]');

                      // 遍历所有复选框，找出选中的
                      checkboxes.forEach((checkbox: HTMLInputElement) => {
                        if (checkbox.checked) {
                          const permissionCode = checkbox.getAttribute('data-permission-code');
                          if (permissionCode) {
                            selectedCodes.push(permissionCode);
                          }
                        }
                      });
                    }

                    return selectedCodes;
                  };

                  // 获取选中的权限代码
                  const frontendPermissionCodes = getSelectedPermissions();
                  console.log('选中的前端权限代码:', frontendPermissionCodes);

                  // 将前端权限代码映射到后端权限代码
                  const backendPermissionCodes = mapPermissionsToBackend(frontendPermissionCodes);
                  console.log('映射后的后端权限代码:', backendPermissionCodes);

                  // 获取所有权限列表
                  const allPermissions = await getAllPermissions();
                  console.log('所有权限:', allPermissions);

                  // 过滤出系统中实际存在的权限代码
                  const validPermissionCodes = backendPermissionCodes.filter(code =>
                    allPermissions.some(permission => permission.code === code)
                  );

                  if (validPermissionCodes.length < backendPermissionCodes.length) {
                    console.warn(`警告：有${backendPermissionCodes.length - validPermissionCodes.length}个映射后的权限代码在系统中不存在`);
                    console.log('有效的权限代码:', validPermissionCodes);
                    console.log('无效的权限代码:', backendPermissionCodes.filter(code => !validPermissionCodes.includes(code)));

                    // 显示警告但不阻止保存
                    toast({
                      title: "部分权限无效",
                      description: "部分选中的权限在系统中不存在，已自动忽略",
                      variant: "warning"
                    });
                  }

                  // 找出选中权限代码对应的权限ID
                  const selectedPermissionIds = allPermissions
                    .filter(permission => validPermissionCodes.includes(permission.code))
                    .map(permission => permission.id);

                  console.log('选中的权限ID:', selectedPermissionIds);

                  // 如果没有选中任何有效权限，显示警告
                  if (frontendPermissionCodes.length > 0 && selectedPermissionIds.length === 0) {
                    console.warn('警告：所有选中的权限代码都无法匹配到权限ID，请检查权限代码是否正确');

                    // 输出所有权限代码和选中的权限代码，帮助调试
                    console.log('所有权限代码:', allPermissions.map(p => p.code));
                    console.log('选中的前端权限代码:', frontendPermissionCodes);
                    console.log('映射后的后端权限代码:', backendPermissionCodes);

                    toast({
                      title: "警告",
                      description: "选中的权限都无法匹配到系统权限，请联系管理员",
                      variant: "destructive"
                    });
                  }

                  // 获取角色当前的权限ID
                  const currentPermissionIds = roleToSetPermission.permissions?.map(p => p.id) || [];
                  console.log('角色当前的权限ID:', currentPermissionIds);

                  // 获取当前角色的所有后端权限代码
                  const currentBackendPermissionCodes = roleToSetPermission.permissions?.map(p => p.code) || [];
                  console.log('角色当前的后端权限代码:', currentBackendPermissionCodes);

                  // 将当前后端权限代码映射到前端权限代码
                  const currentFrontendPermissionCodes = mapPermissionsToFrontend(currentBackendPermissionCodes);
                  console.log('映射后的当前前端权限代码:', currentFrontendPermissionCodes);

                  // 获取用户选中的前端权限代码
                  const selectedFrontendPermissionCodes = getSelectedPermissions();
                  console.log('用户选中的前端权限代码:', selectedFrontendPermissionCodes);

                  // 检查哪些权限被取消了
                  const canceledFrontendPermissionCodes = currentFrontendPermissionCodes.filter(
                    code => !selectedFrontendPermissionCodes.includes(code)
                  );
                  console.log('被取消的前端权限代码:', canceledFrontendPermissionCodes);

                  // 将被取消的前端权限代码映射到后端权限代码
                  const canceledBackendPermissionCodes = mapPermissionsToBackend(canceledFrontendPermissionCodes);
                  console.log('被取消的后端权限代码:', canceledBackendPermissionCodes);

                  // 获取被取消的权限ID
                  const canceledPermissionIds = allPermissions
                    .filter(permission => canceledBackendPermissionCodes.includes(permission.code))
                    .map(permission => permission.id);
                  console.log('被取消的权限ID:', canceledPermissionIds);

                  // 从当前权限ID中移除被取消的权限ID
                  const updatedPermissionIds = currentPermissionIds.filter(
                    id => !canceledPermissionIds.includes(id)
                  );
                  console.log('移除被取消权限后的权限ID:', updatedPermissionIds);

                  // 合并更新后的权限ID和新选中的权限ID
                  const mergedPermissionIds = [...new Set([...updatedPermissionIds, ...selectedPermissionIds])];
                  console.log('最终合并后的权限ID:', mergedPermissionIds);

                  // 调用API更新角色权限
                  const result = await setRolePermissions(
                    parseInt(roleToSetPermission.id),
                    mergedPermissionIds
                  );

                  if (result.success) {
                    setSuccessMessage(`已成功更新角色 ${roleToSetPermission.name} 的权限设置！如果您正在使用此角色，请刷新页面或重新登录以获取最新权限。`);
                    setShowSuccessToast(true);
                    setShowRolePermissionModal(false);
                    setTimeout(() => {
                      setShowSuccessToast(false);
                    }, 5000);
                  } else {
                    // 显示错误消息
                    toast({
                      title: "更新失败",
                      description: result.message || "更新角色权限失败，请稍后重试",
                      variant: "destructive"
                    });
                  }
                } catch (error) {
                  console.error('更新角色权限失败:', error);
                  toast({
                    title: "更新失败",
                    description: "更新角色权限失败，请稍后重试",
                    variant: "destructive"
                  });
                } finally {
                  setLoading(false);
                }
              }}
              disabled={loading}
            >
              {loading ? "保存中..." : "保存"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
