"use client"

import React, { useState } from "react"
import { X, Calendar, User, Eye, ExternalLink, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"
import { buildImageUrl } from "@/utils/image-utils"

interface Message {
  id: number
  title: string
  content?: string
  cover_image?: string
  author: string
  type: 'content' | 'link'
  external_url?: string
  view_count: number
  created_at: string
  updated_at: string
  creator?: {
    id: number
    username: string
  }
}

interface ArticleDetailModalProps {
  message: Message
  onClose: () => void
}

/**
 * 文章详情弹窗组件
 *
 * 统一显示自定义内容和外部链接，外部链接使用iframe嵌入
 */
export function ArticleDetailModal({ message, onClose }: ArticleDetailModalProps) {
  const [iframeError, setIframeError] = useState(false)
  const [iframeLoaded, setIframeLoaded] = useState(false)
  const [webPageContent, setWebPageContent] = useState<string | null>(null)
  const [loadingContent, setLoadingContent] = useState(false)
  const iframeRef = React.useRef<HTMLIFrameElement>(null)

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: zhCN
      })
    } catch (error) {
      return dateString
    }
  }

  // 处理iframe加载错误
  const handleIframeError = () => {
    console.log('iframe加载失败')
    setIframeError(true)
  }

  // 处理iframe加载成功
  const handleIframeLoad = () => {
    console.log('iframe加载成功')
    setIframeLoaded(true)
    setIframeError(false)
  }

  // 检测是否是微信公众号链接
  const isWechatArticle = message.external_url?.includes('mp.weixin.qq.com')

  // 获取微信公众号内容
  const fetchWechatContent = async () => {
    if (!message.external_url) return

    setLoadingContent(true)
    try {
      const token = localStorage.getItem('hefamily_token')

      if (!token) {
        console.error('未找到认证token')
        setIframeError(true)
        setLoadingContent(false)
        return
      }

      const response = await fetch('/api/messages/get-webpage-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ url: message.external_url })
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`HTTP ${response.status}:`, errorText)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      console.log('微信内容获取响应:', data)

      if (data.success) {
        console.log('微信内容获取成功，内容长度:', data.data.content.length)
        console.log('设置webPageContent状态...')
        setWebPageContent(data.data.content)
        setIframeError(false)
        console.log('状态设置完成')
      } else {
        console.error('获取微信内容失败:', data.message, data.error)
        setIframeError(true)
      }
    } catch (error) {
      console.error('获取微信内容失败:', error)
      setIframeError(true)
    } finally {
      setLoadingContent(false)
    }
  }

  // 温和的HTML清理函数
  const cleanHtmlContent = (html: string) => {
    try {
      // 创建一个临时的DOM元素来解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = html

      // 移除所有style标签
      const styleTags = tempDiv.querySelectorAll('style')
      styleTags.forEach(tag => tag.remove())

      // 移除script标签
      const scriptTags = tempDiv.querySelectorAll('script')
      scriptTags.forEach(tag => tag.remove())

      // 只移除明确的微信底部元素
      const wechatBottomSelectors = [
        '#js_stream_bottom_bar',
        '.wx_expand_article_button_wrap',
        '.wx_follow_context',
        '.stream_bottom_bar_wrp',
        '.function_mod',
        '.bottom_bar_placeholder'
      ]

      wechatBottomSelectors.forEach(selector => {
        try {
          const elements = tempDiv.querySelectorAll(selector)
          elements.forEach(el => el.remove())
        } catch (e) {
          // 忽略选择器错误
        }
      })

      // 只移除style属性，保留其他属性
      const allElements = tempDiv.querySelectorAll('*')
      allElements.forEach(el => {
        el.removeAttribute('style')
      })

      return tempDiv.innerHTML
    } catch (error) {
      console.error('清理HTML内容时出错:', error)
      // 如果清理失败，返回原始内容
      return html
    }
  }

  // 格式化内容（支持HTML）
  const formatContent = (content: string) => {
    // 如果内容包含HTML标签，直接渲染
    if (content.includes('<')) {
      return <div dangerouslySetInnerHTML={{ __html: content }} />
    }

    // 否则按换行处理
    return content.split('\n').map((line, index) => (
      <p key={index} className="mb-4 leading-relaxed">
        {line}
      </p>
    ))
  }

  // 移除微信内容获取逻辑，改为直接使用iframe
  // React.useEffect(() => {
  //   if (isWechatArticle && message.external_url) {
  //     fetchWechatContent()
  //   }
  // }, [isWechatArticle, message.external_url])

  // 阻止背景滚动
  React.useEffect(() => {
    // 保存原始样式
    const originalOverflow = document.body.style.overflow

    // 禁用背景滚动
    document.body.style.overflow = 'hidden'

    // 清理函数：恢复原始样式
    return () => {
      document.body.style.overflow = originalOverflow
    }
  }, [])

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4"
      onClick={(e) => {
        // 点击背景关闭弹窗
        if (e.target === e.currentTarget) {
          onClose()
        }
      }}
    >
      <div
        className="bg-white rounded-lg w-full max-w-6xl h-[95vh] sm:h-[90vh] flex flex-col"
        onClick={(e) => {
          // 阻止点击弹窗内容时关闭
          e.stopPropagation()
        }}
      >
        {/* 头部 */}
        <div className="flex items-start justify-between p-4 sm:p-6 border-b flex-shrink-0">
          <div className="flex-1 pr-4">
            <h1 className="text-lg sm:text-2xl font-bold mb-2 line-clamp-2">{message.title}</h1>
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-1 sm:space-y-0 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <User size={14} />
                <span>{message.author}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar size={14} />
                <span>{formatTime(message.created_at)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye size={14} />
                <span>{message.view_count} 次查看</span>
              </div>
              {message.type === 'link' && (
                <div className="flex items-center space-x-1">
                  <ExternalLink size={14} />
                  <span>外部链接</span>
                </div>
              )}
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="flex-shrink-0">
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 flex flex-col min-h-0">
          {message.type === 'link' && message.external_url ? (
            /* 外部链接处理 */
            <div className="flex-1 flex flex-col min-h-0">
              {/* 加载状态 */}
              {(!iframeLoaded && !iframeError) && (
                <div className="flex-1 flex items-center justify-center bg-gray-50">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1e7a43] mx-auto mb-2"></div>
                    <p className="text-gray-500">
                      加载中...
                    </p>
                  </div>
                </div>
              )}

              {/* 微信公众号和其他外部链接都使用iframe */}
              {isWechatArticle ? (
                /* 微信文章直接使用iframe */
                !iframeError ? (
                  <div className="flex-1 bg-white min-h-0 relative">
                    {/* 微信文章提示 */}
                    <div className="absolute top-0 left-0 right-0 bg-green-50 border-b border-green-200 p-2 z-10">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 text-sm">
                        <div className="flex items-center text-green-700">
                          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          <span className="hidden sm:inline">微信公众号文章 - 如果显示异常，请点击右侧按钮在新窗口打开</span>
                          <span className="sm:hidden">微信文章 - 点击按钮在新窗口打开</span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(message.external_url, '_blank')}
                          className="text-xs flex-shrink-0"
                        >
                          <ExternalLink className="w-3 h-3 mr-1" />
                          新窗口打开
                        </Button>
                      </div>
                    </div>
                    <iframe
                      ref={iframeRef}
                      src={message.external_url}
                      className="w-full h-full border-0"
                      style={{ marginTop: '60px', height: 'calc(100% - 60px)' }}
                      title={message.title}
                      sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox allow-top-navigation"
                      onLoad={handleIframeLoad}
                      onError={handleIframeError}
                      referrerPolicy="no-referrer-when-downgrade"
                    />
                  </div>
                ) : (
                  /* 微信文章iframe加载失败 */
                  <div className="h-full flex items-center justify-center bg-gray-50">
                    <div className="text-center max-w-md">
                      <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">无法加载微信文章</h3>
                      <p className="text-gray-600 mb-4">
                        微信公众号可能限制了在框架中显示，请在新窗口中打开查看。
                      </p>
                      {message.cover_image && (
                        <div className="mb-4">
                          <img
                            src={buildImageUrl(message.cover_image)}
                            alt={message.title}
                            className="w-full max-w-sm mx-auto rounded-lg shadow-md"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none'
                            }}
                          />
                        </div>
                      )}
                      <div className="space-y-2">
                        <Button
                          onClick={() => {
                            setIframeError(false)
                            // 重新加载iframe
                            if (iframeRef.current) {
                              iframeRef.current.src = message.external_url
                            }
                          }}
                          variant="outline"
                          className="mr-2"
                        >
                          重试加载
                        </Button>
                        <Button
                          onClick={() => window.open(message.external_url, '_blank')}
                          className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          在新窗口打开
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              ) : (
                /* 非微信链接使用iframe */
                !iframeError ? (
                  <iframe
                    ref={iframeRef}
                    src={message.external_url}
                    className="w-full h-full border-0"
                    title={message.title}
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
                    onLoad={handleIframeLoad}
                    onError={handleIframeError}
                    referrerPolicy="no-referrer-when-downgrade"
                  />
                ) : (
                  /* 非微信链接错误状态 */
                  <div className="h-full flex items-center justify-center bg-gray-50">
                    <div className="text-center max-w-md">
                      <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">无法加载外部内容</h3>
                      <p className="text-gray-600 mb-4">
                        该网站可能不支持在框架中显示，或者存在网络问题。
                      </p>
                      {message.cover_image && (
                        <div className="mb-4">
                          <img
                            src={buildImageUrl(message.cover_image)}
                            alt={message.title}
                            className="w-full max-w-sm mx-auto rounded-lg shadow-md"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none'
                            }}
                          />
                        </div>
                      )}
                      <div className="space-y-2">
                        <Button
                          onClick={() => window.open(message.external_url, '_blank')}
                          className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          在新窗口打开
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              )}
            </div>
          ) : (
            /* 自定义内容 */
            <div className="h-full overflow-y-auto p-6">
              {/* 封面图片 */}
              {message.cover_image && (
                <div className="mb-6">
                  <img
                    src={buildImageUrl(message.cover_image)}
                    alt={message.title}
                    className="w-full max-h-96 object-cover rounded-lg"
                  />
                </div>
              )}

              {/* 正文内容 */}
              {message.content ? (
                <div className="prose max-w-none">
                  <div className="text-gray-800 text-base leading-relaxed">
                    {formatContent(message.content)}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <p>暂无详细内容</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 底部 */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 sm:p-6 border-t bg-gray-50 flex-shrink-0 space-y-2 sm:space-y-0">
          <div className="flex items-center space-x-4">
            {message.type === 'link' && message.external_url && (
              <Button
                variant="outline"
                onClick={() => window.open(message.external_url, '_blank')}
                className="flex-1 sm:flex-none"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                在新窗口打开
              </Button>
            )}
          </div>
          <Button onClick={onClose} className="flex-1 sm:flex-none">
            关闭
          </Button>
        </div>
      </div>
    </div>
  )
}
