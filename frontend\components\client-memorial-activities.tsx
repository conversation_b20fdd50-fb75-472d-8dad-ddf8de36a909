"use client"

import { useState, useEffect, useMemo, useCallback, startTransition } from "react"
import { MemorialActivities } from "./memorial-activities"
import { useAuth } from "@/contexts/auth-context"
import { ActivityManagementButton } from "./activity-management-button"
import { logger } from "@/utils/logger"

/**
 * 客户端纪念活动组件
 *
 * 用于在客户端渲染纪念活动组件，解决服务器端渲染与客户端渲染不匹配的问题
 */
export function ClientMemorialActivities() {
  const [isManagementMode, setIsManagementMode] = useState(false)
  const { isLoggedIn, hasPermission } = useAuth()
  const [mounted, setMounted] = useState(false)

  // 检查用户是否有管理活动的权限 - 使用useMemo避免渲染期间调用
  const canManageActivities = useMemo(() => {
    if (!isLoggedIn) return false
    return hasPermission("manage_activities") || hasPermission("activity:manage")
  }, [isLoggedIn, hasPermission])

  // 移除调试日志以提升性能

  // 切换管理模式 - 使用useCallback稳定函数引用，startTransition减少闪烁
  const toggleManagementMode = useCallback(() => {
    startTransition(() => {
      setIsManagementMode(prev => !prev)
    })
  }, [])

  // 确保组件已挂载，避免服务器端渲染与客户端渲染不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <MemorialActivities
      isManagementMode={isManagementMode}
      toggleManagementMode={toggleManagementMode}
      showManagementButton={mounted && canManageActivities}
    />
  )
}
