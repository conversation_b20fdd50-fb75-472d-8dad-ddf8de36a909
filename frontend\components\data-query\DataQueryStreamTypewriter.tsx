"use client"

import React, { useState, useRef, useEffect, useCallback } from "react"

/**
 * 数据查询助手流式打字机组件
 *
 * 用于处理数据查询助手的流式响应，实现打字机效果
 */
export function DataQueryStreamTypewriter({
  className = "",
  onComplete,
  onFirstToken
}: {
  className?: string,
  onComplete?: (text: string, conversationId: string | null) => void,
  onFirstToken?: () => void
}) {
  const [text, setText] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [conversationId, setConversationId] = useState<string | null>(null)
  const eventSourceRef = useRef<EventSource | null>(null)

  // 使用ref来跟踪文本内容，避免闭包问题
  const textRef = useRef("")

  // 使用ref来跟踪完整的文本内容和当前显示的位置
  const fullTextRef = useRef("")
  const displayIndexRef = useRef(0)

  // 打字机效果的定时器
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  /**
   * 开始流式打字
   *
   * @param query 查询内容
   * @param initialConversationId 初始会话ID
   * @param apiKey API密钥
   * @param apiEndpoint API端点
   * @param appId 应用ID
   * @param appCode 应用代码
   */
  const startStreaming = async (
    query: string,
    initialConversationId?: string | null,
    apiKey?: string,
    apiEndpoint?: string,
    appId?: string,
    appCode?: string
  ) => {
    // 重置状态
    setText("")
    textRef.current = ""
    fullTextRef.current = ""
    displayIndexRef.current = 0

    // 清除之前的定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current)
      timerRef.current = null
    }

    setIsTyping(true)

    // 保留初始会话ID
    if (initialConversationId) {
      setConversationId(initialConversationId)
      console.log('使用现有会话ID:', initialConversationId)
    } else {
      setConversationId(null)
      console.log('创建新会话')
    }

    console.log('开始数据查询助手流式打字，初始会话ID:', initialConversationId || '新会话')

    try {
      // 获取认证token
      let token = localStorage.getItem('hefamily_token')

      // 如果没有找到token，尝试其他可能的存储位置
      if (!token) {
        token = localStorage.getItem('token') || sessionStorage.getItem('token')
        console.log('从其他存储位置获取token:', token ? '已获取' : '未获取')
      }

      // 如果仍然没有找到token，尝试从auth-service获取
      if (!token && typeof window !== 'undefined') {
        try {
          console.log('尝试从auth-service检查登录状态')
          // 导入isLoggedIn函数
          const { isLoggedIn } = await import('@/services/auth-service')
          const loggedIn = isLoggedIn()
          console.log('auth-service登录状态:', loggedIn)

          if (loggedIn) {
            token = localStorage.getItem('hefamily_token')
            console.log('从auth-service确认登录后获取token:', token ? '已获取' : '未获取')
          }
        } catch (e) {
          console.error('导入auth-service失败:', e)
        }
      }

      if (!token) {
        console.error('无法获取认证令牌，用户未登录')
        throw new Error('未登录，请先登录')
      }

      console.log('成功获取认证令牌')

      // 关闭之前的连接
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }

      // 创建请求体
      const requestBody = {
        query,
        conversation_id: initialConversationId || null,
        api_key: apiKey,
        api_endpoint: apiEndpoint,
        app_id: appId,
        app_code: appCode
      }

      // 创建请求头
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }

      // 创建POST请求获取流式响应
      console.log('发送流式请求到后端:', '/api/ai/data-query/stream')
      console.log('请求体:', JSON.stringify(requestBody))

      const response = await fetch('/api/ai/data-query/stream', {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      })

      console.log('收到流式响应，状态码:', response.status)

      if (!response.ok) {
        console.error('流式响应错误，状态码:', response.status)
        const errorData = await response.json()
        console.error('错误详情:', errorData)
        throw new Error(errorData.message || '请求失败')
      }

      console.log('流式响应成功，开始处理')

      // 创建EventSource对象
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法创建流式读取器')
      }

      // 处理流式响应
      const decoder = new TextDecoder()
      let buffer = ''

      // 将变量声明移到函数最外层，确保在finally块中可以访问
      let hasReceivedAnyToken = false
      let totalTokensReceived = 0

      const processStream = async () => {
        try {

          while (true) {
            const { done, value } = await reader.read()
            if (done) {
              console.log('流式读取完成，总共接收token数:', totalTokensReceived)
              break
            }

            // 解码并添加到缓冲区
            const chunk = decoder.decode(value, { stream: true })
            buffer += chunk
            console.log('接收到新的数据块，长度:', chunk.length)

            // 打印数据块内容，帮助调试
            if (chunk.length > 0) {
              console.log('数据块内容:', chunk.length > 100 ?
                chunk.substring(0, 50) + '...' + chunk.substring(chunk.length - 50) :
                chunk)
            }

            // 处理完整的SSE消息
            const lines = buffer.split('\n\n')
            buffer = lines.pop() || ''

            for (const line of lines) {
              if (line.startsWith('data:')) {
                try {
                  const jsonStr = line.substring(5).trim()
                  const data = JSON.parse(jsonStr)

                  // 根据消息类型处理
                  if (data.type === 'token') {
                    // 检查是否是第一个token
                    const isFirstToken = !hasReceivedAnyToken

                    hasReceivedAnyToken = true
                    totalTokensReceived++

                    // 添加新的文本片段
                    // 检查是否包含思考过程（details标签）
                    let newContent = data.content;

                    // 更新完整文本内容
                    textRef.current += newContent;

                    // 处理完整文本，移除思考过程
                    let displayText = textRef.current;

                    // 如果包含思考过程（details标签），则只显示结果部分
                    if (displayText.includes('<details') && displayText.includes('</details>')) {
                      const detailsEndIndex = displayText.indexOf('</details>') + 10;
                      if (detailsEndIndex < displayText.length) {
                        displayText = displayText.substring(detailsEndIndex).trim();
                      } else {
                        // 如果还没有收到结果部分，暂时不显示任何内容
                        displayText = "";
                      }
                    }

                    // 使用打字机效果更新显示的文本
                    setText(displayText)

                    // 确保更新fullTextRef，以便打字机效果能够正确显示
                    // 如果包含思考过程（details标签），则只显示结果部分
                    let cleanedText = displayText;
                    if (cleanedText.includes('<details') && cleanedText.includes('</details>')) {
                      const detailsEndIndex = cleanedText.indexOf('</details>') + 10;
                      if (detailsEndIndex < cleanedText.length) {
                        cleanedText = cleanedText.substring(detailsEndIndex).trim();
                        console.log('提取出正式回复部分，长度:', cleanedText.length);
                      }
                    }

                    fullTextRef.current = cleanedText

                    // 每次更新都打印日志，帮助调试
                    console.log(`数据查询助手流式响应更新(token ${totalTokensReceived}):`,
                      data.content.length > 0 ?
                        `"${data.content}"` :
                        "(空token)",
                      "当前总长度:", textRef.current.length)

                    // 如果是第一个token，调用onFirstToken回调
                    if (isFirstToken && onFirstToken) {
                      console.log('收到第一个token，调用onFirstToken回调')
                      onFirstToken()
                    }
                  } else if (data.type === 'end') {
                    // 消息结束，保存会话ID
                    if (data.conversation_id) {
                      console.log('数据查询助手流式响应结束，会话ID:', data.conversation_id)
                      setConversationId(data.conversation_id)
                    }

                    // 检查当前文本内容是否为空
                    if (textRef.current.trim() === '') {
                      console.error('数据查询助手流式响应结束，但累积内容为空，尝试使用end事件中的content')
                      // 尝试使用end事件中的content
                      if (data.content && typeof data.content === 'string' && data.content.trim() !== '') {
                        textRef.current = data.content
                        setText(data.content)
                        console.log('使用end事件中的content，长度:', data.content.length, '内容:', data.content)
                      } else {
                        // 如果end事件中也没有content，设置一个默认消息
                        const defaultMsg = '抱歉，生成回复时出现问题，请重试。'
                        textRef.current = defaultMsg
                        setText(defaultMsg)
                        console.error('数据查询助手流式响应结束，累积内容和end事件content都为空')
                      }
                    } else {
                      console.log('数据查询助手流式响应结束，累积内容长度:', textRef.current.length, '内容:',
                        textRef.current.length > 50 ?
                          textRef.current.substring(0, 20) + '...' + textRef.current.substring(textRef.current.length - 20) :
                          textRef.current)

                      // 即使有累积内容，也检查end事件中的content是否更完整
                      if (data.content && typeof data.content === 'string' &&
                          data.content.trim() !== '' &&
                          data.content.length > textRef.current.length) {
                        console.log('end事件中的content更长，使用end事件中的content')
                        textRef.current = data.content
                        setText(data.content)
                      }
                    }
                  } else if (data.type === 'error') {
                    // 错误消息
                    console.error('数据查询助手流式响应错误:', data.error)
                    const errorMsg = '\n\n[错误: ' + data.error + ']'
                    textRef.current += errorMsg
                    setText(textRef.current)
                  }
                } catch (e) {
                  console.error('解析SSE消息失败:', e)
                }
              }
            }
          }
        } catch (error) {
          console.error('处理数据查询助手流式响应错误:', error)
          const errorMsg = '\n\n[连接错误，请重试]'
          textRef.current += errorMsg
          setText(textRef.current)
        } finally {
          // 检查是否收到过任何token
          if (!hasReceivedAnyToken) {
            console.error('数据查询助手流式响应结束，但未收到任何token')
            const defaultMsg = '抱歉，生成回复时出现问题，请重试。'
            textRef.current = defaultMsg
            setText(defaultMsg)
          } else if (textRef.current.trim() === '') {
            // 如果收到了token但最终文本为空
            console.error('数据查询助手流式响应结束，收到了token但最终文本为空')
            const defaultMsg = '抱歉，生成回复时出现问题，请重试。'
            textRef.current = defaultMsg
            setText(defaultMsg)
          } else if (textRef.current.length < 5) {
            // 如果文本太短，可能是不完整的响应
            console.warn('数据查询助手流式响应结束，但文本内容过短:', textRef.current)
            // 不修改文本，保留原始内容
          }

          // 最后一次检查文本内容
          console.log('数据查询助手流式响应最终内容:', textRef.current)

          // 确保显示完整的文本内容，但移除思考过程
          let finalText = textRef.current;

          // 如果包含思考过程（details标签），则只显示结果部分
          if (finalText.includes('<details') && finalText.includes('</details>')) {
            const detailsEndIndex = finalText.indexOf('</details>') + 10;
            if (detailsEndIndex < finalText.length) {
              finalText = finalText.substring(detailsEndIndex).trim();
            } else {
              // 如果没有结果部分，使用默认消息
              finalText = "抱歉，生成回复时出现问题，请重试。";
            }
          }

          // 更新最终文本
          textRef.current = finalText

          // 如果定时器已存在，清除它
          if (timerRef.current) {
            clearTimeout(timerRef.current)
            timerRef.current = null
          }

          // 更新fullTextRef，以便打字机效果能够正确显示
          fullTextRef.current = textRef.current

          // 重置显示位置
          displayIndexRef.current = 0

          // 启动打字机效果
          timerRef.current = setTimeout(typeNextChar, 10)

          // 设置正在打字状态
          setIsTyping(true)

          // 如果提供了onComplete回调，调用它
          if (onComplete) {
            onComplete(finalText, conversationId)
          }
        }
      }

      processStream()

      return {
        cancel: () => {
          reader.cancel()

          // 清除定时器
          if (timerRef.current) {
            clearTimeout(timerRef.current)
            timerRef.current = null
          }

          setIsTyping(false)
        }
      }
    } catch (error) {
      console.error('数据查询助手流式打字错误:', error)
      const errorMsg = '\n\n[错误: ' + (error instanceof Error ? error.message : '未知错误') + ']'
      textRef.current += errorMsg

      // 清除定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }

      // 直接设置文本内容，不使用打字机效果
      setText(textRef.current)
      setIsTyping(false)
      return { cancel: () => {} }
    }
  }

  // 打字机效果函数
  const typeNextChar = useCallback(() => {
    if (displayIndexRef.current < fullTextRef.current.length) {
      // 更新显示的文本
      const nextChar = fullTextRef.current[displayIndexRef.current]
      textRef.current = fullTextRef.current.substring(0, displayIndexRef.current + 1)
      setText(textRef.current)

      // 打印日志，帮助调试
      if (displayIndexRef.current % 50 === 0) {
        console.log(`打字机效果进度: ${displayIndexRef.current}/${fullTextRef.current.length}`)
      }

      // 增加显示位置
      displayIndexRef.current++

      // 设置下一个字符的延迟 - 生产环境使用更快的速度
      const delay = process.env.NODE_ENV === 'production' ?
        (nextChar === '\n' ? 15 : 5) : // 生产环境更快
        (nextChar === '\n' ? 30 : 10)  // 开发环境正常速度
      timerRef.current = setTimeout(typeNextChar, delay)
    } else {
      // 打字机效果结束
      console.log('打字机效果结束')
      setIsTyping(false)
    }
  }, [])

  // 当收到新的文本内容时，更新fullTextRef并启动打字机效果
  const updateFullText = useCallback((newContent: string) => {
    console.log('更新打字机文本内容，长度:', newContent.length)

    // 如果包含思考过程（details标签），则只显示结果部分
    let displayText = newContent;
    if (displayText.includes('<details') && displayText.includes('</details>')) {
      const detailsEndIndex = displayText.indexOf('</details>') + 10;
      if (detailsEndIndex < displayText.length) {
        displayText = displayText.substring(detailsEndIndex).trim();
        console.log('提取出正式回复部分，长度:', displayText.length);
      }
    }

    // 更新完整文本
    fullTextRef.current = displayText

    // 重置显示位置
    displayIndexRef.current = 0

    // 如果定时器已存在，清除它
    if (timerRef.current) {
      clearTimeout(timerRef.current)
      timerRef.current = null
    }

    // 启动打字机效果
    console.log('启动打字机效果')
    timerRef.current = setTimeout(typeNextChar, 10)

    // 设置正在打字状态
    setIsTyping(true)

    // 更新文本状态，这样React会重新渲染
    setText('')
  }, [typeNextChar])

  // 将文本中的换行符转换为<br>标签
  const formattedText = React.useMemo(() => {
    if (!text) return null;

    return text.split('\n').map((line, i, arr) => (
      <React.Fragment key={i}>
        {line}
        {i < arr.length - 1 && <br />}
      </React.Fragment>
    ))
  }, [text])

  // 创建一个可以直接渲染的组件
  const TextComponent = React.useMemo(() => {
    if (!text && isTyping) {
      // 如果没有文本但正在打字，显示加载动画
      return (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
          <span>正在生成回复...</span>
        </div>
      )
    }

    // 如果有文本，显示文本内容
    if (text) {
      // 如果包含思考过程（details标签），则只显示结果部分
      let displayText = text;
      if (displayText.includes('<details') && displayText.includes('</details>')) {
        const detailsEndIndex = displayText.indexOf('</details>') + 10;
        if (detailsEndIndex < displayText.length) {
          displayText = displayText.substring(detailsEndIndex).trim();
        }
      }

      return (
        <div className={className}>
          {displayText.split('\n').map((line, i, arr) => (
            <React.Fragment key={i}>
              {line}
              {i < arr.length - 1 && <br />}
            </React.Fragment>
          ))}
          {isTyping && (
            <span className="inline-block w-2 h-4 bg-gray-500 ml-1 animate-pulse"></span>
          )}
        </div>
      );
    }

    // 默认返回空div
    return <div className={className}></div>;
  }, [className, text, isTyping])

  return {
    text,
    isTyping,
    conversationId,
    startStreaming,
    textComponent: TextComponent
  }
}
