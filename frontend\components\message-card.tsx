"use client"

import Image from "next/image"
import Link from "next/link"
import { ExternalLink, Calendar, User, Eye, FileText, Archive, Edit, Send, MoreVertical } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"
import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import { useState, useMemo } from "react"
import { buildImageUrl } from "@/utils/image-utils"

interface MessageCardProps {
  message: {
    id: number
    title: string
    excerpt?: string
    cover_image?: string
    author: string
    type: 'content' | 'link'
    external_url?: string
    status: 'draft' | 'published' | 'archived'
    view_count: number
    created_at: string
    updated_at: string
  }
  onClick?: () => void
  onEdit?: (message: any) => void
  onPublish?: (id: number) => void
  onUnpublish?: (id: number) => void
  onArchive?: (id: number) => void
  onDelete?: (id: number) => void
  isManagementMode?: boolean
}

/**
 * 消息卡片组件
 *
 * 用于在首页展示消息的卡片形式，支持管理模式
 */
export function MessageCard({
  message,
  onClick,
  onEdit,
  onPublish,
  onUnpublish,
  onArchive,
  onDelete,
  isManagementMode = false
}: MessageCardProps) {
  // console.log('渲染消息卡片:', { id: message.id, title: message.title, cover_image: message.cover_image })

  const { hasPermission } = useAuth()
  const [showActions, setShowActions] = useState(false)

  // 检查是否有管理权限 - 使用useMemo避免渲染期间调用
  const canManage = useMemo(() => {
    return hasPermission('content:publish') || hasPermission('content:manage')
  }, [hasPermission])

  const handleClick = () => {
    if (onClick) {
      // 所有类型都调用onClick回调，在弹窗中显示
      onClick()
    }
  }

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation() // 阻止事件冒泡
    action()
  }

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: zhCN
      })
    } catch (error) {
      return dateString
    }
  }

  // 获取状态显示信息
  const getStatusInfo = () => {
    switch (message.status) {
      case 'draft':
        return {
          text: '草稿',
          icon: FileText,
          className: 'bg-yellow-500 text-white'
        }
      case 'archived':
        return {
          text: '已归档',
          icon: Archive,
          className: 'bg-gray-500 text-white'
        }
      default:
        return null
    }
  }

  const statusInfo = getStatusInfo()

  // 检查是否是微信公众号文章
  const isWechatArticle = message.external_url?.includes('mp.weixin.qq.com')

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border overflow-hidden cursor-pointer hover:shadow-md transition-shadow ${
        isWechatArticle ? 'border-green-200 hover:border-green-300' : ''
      }`}
      onClick={handleClick}
    >
      <div className="flex flex-col sm:flex-row">
        {/* 封面图片 */}
        <div className="w-full h-48 sm:w-48 sm:h-32 relative flex-shrink-0">
          <Image
            src={buildImageUrl(message.cover_image)}
            alt={message.title}
            fill
            className="object-cover"
          />
          {/* 类型和状态标识 */}
          <div className="absolute top-2 right-2 flex space-x-1">
            {message.type === 'link' && (
              <div className={`${isWechatArticle ? 'bg-green-500' : 'bg-blue-500'} text-white rounded-full p-1`}>
                <ExternalLink size={16} />
              </div>
            )}
            {isWechatArticle && (
              <div className="bg-green-500 text-white rounded-full px-2 py-1 flex items-center space-x-1">
                <span className="text-xs font-medium">微信</span>
              </div>
            )}
            {statusInfo && (
              <div className={`${statusInfo.className} rounded-full px-2 py-1 flex items-center space-x-1`}>
                <statusInfo.icon size={12} />
                <span className="text-xs font-medium">{statusInfo.text}</span>
              </div>
            )}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-4 flex flex-col justify-between">
          <div>
            {/* 标题 */}
            <h3 className={`text-lg font-bold mb-2 line-clamp-2 transition-colors ${
              isWechatArticle ? 'hover:text-green-600' : 'hover:text-blue-600'
            }`}>
              {message.title}
            </h3>

            {/* 微信文章提示 */}
            {isWechatArticle && (
              <div className="mb-2 text-xs text-green-600 bg-green-50 px-2 py-1 rounded inline-block">
                📱 点击在新窗口打开微信文章
              </div>
            )}

            {/* 作者和发布时间 */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0 text-sm text-gray-500 mb-2">
              <div className="flex items-center space-x-1">
                <User size={14} />
                <span>{message.author}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar size={14} />
                <span>{formatTime(message.created_at)}</span>
              </div>
            </div>

            {/* 摘要 */}
            {message.excerpt && (
              <p className="text-gray-600 text-sm line-clamp-3">
                {message.excerpt}
              </p>
            )}
          </div>

          {/* 底部操作区域 */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mt-2 space-y-2 sm:space-y-0">
            {/* 查看次数 */}
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <Eye size={12} />
              <span>{message.view_count}</span>
            </div>

            {/* 管理按钮 - 只在管理模式下显示 */}
            {canManage && isManagementMode && (
              <div className="flex items-center space-x-1 flex-wrap gap-1">
                {/* 草稿状态：发布、编辑、删除 */}
                {message.status === 'draft' && (
                  <>
                    {onPublish && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs text-green-600 border-green-600 hover:bg-green-50"
                        onClick={(e) => handleActionClick(e, () => onPublish(message.id))}
                      >
                        <Send size={12} className="mr-1" />
                        发布
                      </Button>
                    )}
                    {onEdit && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs"
                        onClick={(e) => handleActionClick(e, () => onEdit(message))}
                      >
                        <Edit size={12} className="mr-1" />
                        编辑
                      </Button>
                    )}
                    {onDelete && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs text-red-600 border-red-600 hover:bg-red-50"
                        onClick={(e) => handleActionClick(e, () => onDelete(message.id))}
                      >
                        删除
                      </Button>
                    )}
                  </>
                )}

                {/* 已发布状态：下架、编辑、归档 */}
                {message.status === 'published' && (
                  <>
                    {onUnpublish && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs text-orange-600 border-orange-600 hover:bg-orange-50"
                        onClick={(e) => handleActionClick(e, () => onUnpublish(message.id))}
                      >
                        下架
                      </Button>
                    )}
                    {onEdit && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs"
                        onClick={(e) => handleActionClick(e, () => onEdit(message))}
                      >
                        <Edit size={12} className="mr-1" />
                        编辑
                      </Button>
                    )}
                    {onArchive && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs text-gray-600 border-gray-600 hover:bg-gray-50"
                        onClick={(e) => handleActionClick(e, () => onArchive(message.id))}
                      >
                        <Archive size={12} className="mr-1" />
                        归档
                      </Button>
                    )}
                  </>
                )}

                {/* 归档状态：恢复、删除 */}
                {message.status === 'archived' && (
                  <>
                    {onPublish && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs text-blue-600 border-blue-600 hover:bg-blue-50"
                        onClick={(e) => handleActionClick(e, () => onPublish(message.id))}
                      >
                        恢复
                      </Button>
                    )}
                    {onDelete && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-6 px-2 text-xs text-red-600 border-red-600 hover:bg-red-50"
                        onClick={(e) => handleActionClick(e, () => onDelete(message.id))}
                      >
                        删除
                      </Button>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
