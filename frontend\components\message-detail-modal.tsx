"use client"

import { X, Calendar, User, Eye } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { formatDistanceToNow } from "date-fns"
import { zhCN } from "date-fns/locale"
import { buildImageUrl } from "@/utils/image-utils"

interface Message {
  id: number
  title: string
  content?: string
  cover_image?: string
  author: string
  type: 'content' | 'link'
  external_url?: string
  view_count: number
  created_at: string
  updated_at: string
  creator?: {
    id: number
    username: string
  }
}

interface MessageDetailModalProps {
  message: Message
  onClose: () => void
}

/**
 * 消息详情弹窗组件
 *
 * 显示自定义内容类型消息的详细信息
 */
export function MessageDetailModal({ message, onClose }: MessageDetailModalProps) {
  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: zhCN
      })
    } catch (error) {
      return dateString
    }
  }

  // 格式化内容（简单的换行处理）
  const formatContent = (content: string) => {
    return content.split('\n').map((line, index) => (
      <p key={index} className="mb-4 leading-relaxed">
        {line}
      </p>
    ))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex-1">
            <h1 className="text-2xl font-bold mb-2">{message.title}</h1>
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <User size={14} />
                <span>{message.author}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar size={14} />
                <span>{formatTime(message.created_at)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye size={14} />
                <span>{message.view_count} 次查看</span>
              </div>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* 内容 */}
        <div className="p-6">
          {/* 封面图片 */}
          {message.cover_image && (
            <div className="mb-6">
              <img
                src={buildImageUrl(message.cover_image)}
                alt={message.title}
                className="w-full max-h-96 object-cover rounded-lg"
              />
            </div>
          )}

          {/* 正文内容 */}
          {message.content && (
            <div className="prose max-w-none">
              <div className="text-gray-800 text-base leading-relaxed">
                {formatContent(message.content)}
              </div>
            </div>
          )}

          {/* 如果没有内容，显示提示 */}
          {!message.content && (
            <div className="text-center py-12 text-gray-500">
              <p>暂无详细内容</p>
            </div>
          )}
        </div>

        {/* 底部 */}
        <div className="flex items-center justify-end p-6 border-t bg-gray-50">
          <Button onClick={onClose}>
            关闭
          </Button>
        </div>
      </div>
    </div>
  )
}
