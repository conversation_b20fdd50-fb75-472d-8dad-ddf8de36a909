"use client"

import { useState, useEffect } from "react"
import { Plus, Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { MessageCard } from "./message-card"
import { MessagePublishModal } from "./message-publish-modal"
import { ArticleDetailModal } from "./article-detail-modal"
import { useAuth } from "@/contexts/auth-context"
import { usePermission } from "@/contexts/permission-context"
import { messageApi } from "@/src/api"
import { toast } from "@/hooks/use-toast"

interface Message {
  id: number
  title: string
  content?: string
  excerpt?: string
  cover_image?: string
  author: string
  type: 'content' | 'link'
  external_url?: string
  status: 'draft' | 'published' | 'archived'
  view_count: number
  created_at: string
  updated_at: string
  creator?: {
    id: number
    username: string
  }
}

/**
 * 消息列表组件
 *
 * 在首页展示消息列表，支持发布新消息和管理模式
 */
export function MessageList() {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [showPublishModal, setShowPublishModal] = useState(false)
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null)
  const [editingMessage, setEditingMessage] = useState<Message | null>(null)
  const [isManagementMode, setIsManagementMode] = useState(false)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    total: 0,
    limit: 4 // 首页最多显示4个
  })
  const { isLoggedIn } = useAuth()
  const { hasPermission, isLoading: permissionLoading } = usePermission()

  // 检查是否有发布权限
  const canPublish = isLoggedIn && hasPermission('content:publish')

  // 检查是否可以进入管理模式（有发布或管理权限）
  const canManage = isLoggedIn && (hasPermission('content:publish') || hasPermission('content:manage'))

  // 切换管理模式
  const toggleManagementMode = () => {
    setIsManagementMode(!isManagementMode)
  }

  // 加载消息列表
  const loadMessages = async (page = 1) => {
    try {
      setLoading(true)

      // 根据管理模式和权限决定获取哪些状态的消息
      const params = {
        page: page,
        limit: pagination.limit,
        // 管理模式下：有权限的用户获取所有状态，无权限的用户只能看到已发布的
        // 非管理模式下：所有用户都只能看到已发布的
        status: (isManagementMode && isLoggedIn && (hasPermission('content:publish') || hasPermission('content:manage'))) ? '' : 'published',
        // 添加时间戳确保每次请求都是唯一的
        _t: Date.now()
      }

      // console.log('消息列表请求参数:', params)

      const response = await messageApi.getMessageList(params)

      if (response.success) {
        setMessages(response.data.messages || [])
        // 更新分页信息
        const newPagination = {
          currentPage: page,
          totalPages: response.data.pagination?.pages || 1,
          total: response.data.pagination?.total || 0,
          limit: pagination.limit
        }
        setPagination(newPagination)
        // console.log('获取到的消息:', response.data.messages?.map(m => ({ id: m.id, title: m.title, status: m.status, cover_image: m.cover_image })))
      } else {
        console.error('获取消息列表失败:', response.message)
        console.error('完整的错误响应:', response)
      }
    } catch (error) {
      console.error('获取消息列表失败:', error)
      toast({
        title: "加载失败",
        description: "无法加载消息列表，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 组件挂载时加载消息，当权限加载完成或管理模式变化时重新加载
  useEffect(() => {
    if (!permissionLoading) {
      loadMessages()
    }
  }, [isManagementMode, permissionLoading])

  // 处理消息卡片点击
  const handleMessageClick = (message: Message) => {
    // 检查是否是微信公众号文章
    const isWechatArticle = message.external_url?.includes('mp.weixin.qq.com')

    if (isWechatArticle) {
      // 微信文章直接在新窗口打开
      window.open(message.external_url, '_blank')
    } else {
      // 其他类型的消息在弹窗中显示
      setSelectedMessage(message)
    }
  }

  // 处理发布成功
  const handlePublishSuccess = async (updatedMessage?: any) => {
    setShowPublishModal(false)
    setEditingMessage(null) // 清除编辑状态

    // 如果有更新后的消息数据，立即更新本地状态
    if (updatedMessage) {
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === updatedMessage.id ? { ...msg, ...updatedMessage } : msg
        )
      )
    }

    // 立即刷新一次
    loadMessages()

    // 再次延迟刷新确保获取最新数据
    setTimeout(() => {
      loadMessages()
    }, 500)
    // 不在这里显示toast，因为在弹窗中已经显示了
  }

  // 处理编辑消息
  const handleEditMessage = (message: Message) => {
    // 设置编辑模式的消息数据
    setEditingMessage(message)
    setShowPublishModal(true)
  }

  // 处理发布草稿
  const handlePublishDraft = async (id: number) => {
    try {
      const response = await messageApi.publishMessage(id)
      if (response.success) {
        toast({
          title: "发布成功",
          description: "草稿已成功发布",
        })
        loadMessages() // 重新加载消息列表
      } else {
        toast({
          title: "发布失败",
          description: response.message || "发布失败，请稍后重试",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('发布草稿失败:', error)
      toast({
        title: "发布失败",
        description: "网络错误，请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 处理下架消息
  const handleUnpublishMessage = async (id: number) => {
    try {
      const response = await messageApi.unpublishMessage(id)
      if (response.success) {
        toast({
          title: "下架成功",
          description: "消息已下架，改为草稿状态",
        })
        loadMessages() // 重新加载消息列表
      } else {
        toast({
          title: "下架失败",
          description: response.message || "下架失败，请稍后重试",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('下架消息失败:', error)
      toast({
        title: "下架失败",
        description: "网络错误，请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 处理归档消息
  const handleArchiveMessage = async (id: number) => {
    try {
      const response = await messageApi.archiveMessage(id)
      if (response.success) {
        toast({
          title: "归档成功",
          description: "消息已成功归档（归档的消息不会在首页显示）",
        })
        loadMessages() // 重新加载消息列表
      } else {
        toast({
          title: "归档失败",
          description: response.message || "归档失败，请稍后重试",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('归档消息失败:', error)
      toast({
        title: "归档失败",
        description: "网络错误，请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 处理删除消息
  const handleDeleteMessage = async (id: number) => {
    if (!confirm('确定要删除这条消息吗？删除后无法恢复。')) {
      return
    }

    try {
      const response = await messageApi.deleteMessage(id)
      if (response.success) {
        toast({
          title: "删除成功",
          description: "消息已成功删除",
        })
        loadMessages() // 重新加载消息列表
      } else {
        toast({
          title: "删除失败",
          description: response.message || "删除失败，请稍后重试",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('删除消息失败:', error)
      toast({
        title: "删除失败",
        description: "网络错误，请稍后重试",
        variant: "destructive"
      })
    }
  }

  // 显示的消息：API已经根据管理模式返回了正确的数据，不需要再次过滤
  const displayMessages = messages

  // 如果没有消息且不在加载中且没有发布权限，不显示此组件
  // 这样未登录用户在有已发布文章时可以看到，没有文章时不显示空组件
  if (!loading && displayMessages.length === 0 && !canPublish) {
    return null
  }

  return (
    <section className="py-16">
      <div className="flex items-center justify-between mb-12">
        <h2 className="text-3xl font-bold">相关文章</h2>
        <div className="flex items-center space-x-2">
          {/* 管理模式切换按钮 */}
          {canManage && (
            <Button
              variant={isManagementMode ? "default" : "outline"}
              onClick={toggleManagementMode}
              className={isManagementMode ? "bg-[#1e7a43] hover:bg-[#1e7a43]/90" : ""}
            >
              <Settings className="w-4 h-4 mr-2" />
              {isManagementMode ? "退出管理" : "管理模式"}
            </Button>
          )}
          {/* 新增文章按钮 - 只在管理模式下显示 */}
          {canPublish && isManagementMode && (
            <Button
              onClick={() => setShowPublishModal(true)}
              className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
            >
              <Plus className="w-4 h-4 mr-2" />
              新增文章
            </Button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="py-12 text-center">
          <p className="text-gray-500">加载中...</p>
        </div>
      ) : displayMessages.length > 0 ? (
        <div className="space-y-6">
          {displayMessages.map((message) => (
            <MessageCard
              key={message.id}
              message={message}
              onClick={() => handleMessageClick(message)}
              onEdit={handleEditMessage}
              onPublish={handlePublishDraft}
              onUnpublish={handleUnpublishMessage}
              onArchive={handleArchiveMessage}
              onDelete={handleDeleteMessage}
              isManagementMode={isManagementMode}
            />
          ))}

          {/* 分页控件 */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadMessages(pagination.currentPage - 1)}
                disabled={pagination.currentPage <= 1 || loading}
              >
                上一页
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={page === pagination.currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => loadMessages(page)}
                    disabled={loading}
                    className={page === pagination.currentPage ? "bg-[#1e7a43] hover:bg-[#1e7a43]/90" : ""}
                  >
                    {page}
                  </Button>
                ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => loadMessages(pagination.currentPage + 1)}
                disabled={pagination.currentPage >= pagination.totalPages || loading}
              >
                下一页
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="py-12 text-center">
          <p className="text-gray-500">暂无文章</p>
          {canPublish && isManagementMode && (
            <Button
              onClick={() => setShowPublishModal(true)}
              className="mt-4 bg-[#1e7a43] hover:bg-[#1e7a43]/90"
            >
              <Plus className="w-4 h-4 mr-2" />
              发布第一篇文章
            </Button>
          )}
        </div>
      )}

      {/* 发布消息弹窗 */}
      {showPublishModal && (
        <MessagePublishModal
          onClose={() => {
            setShowPublishModal(false)
            setEditingMessage(null) // 关闭时清除编辑状态
          }}
          onSuccess={handlePublishSuccess}
          editingMessage={editingMessage}
        />
      )}

      {/* 文章详情弹窗 */}
      {selectedMessage && (
        <ArticleDetailModal
          message={selectedMessage}
          onClose={() => setSelectedMessage(null)}
        />
      )}
    </section>
  )
}
