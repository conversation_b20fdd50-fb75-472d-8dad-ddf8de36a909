"use client"

import { useState, useRef } from "react"
import { X, Upload, Link as LinkIcon, Type, Save, Send } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { SimpleRichEditor } from "./simple-rich-editor"
import { messageApi } from "@/src/api"
import { toast } from "@/hooks/use-toast"
import { uploadImage } from "@/services/upload-service"

interface Message {
  id: number
  title: string
  content?: string
  author: string
  type: 'content' | 'link'
  external_url?: string
  cover_image?: string
  status: 'draft' | 'published' | 'archived'
}

interface MessagePublishModalProps {
  onClose: () => void
  onSuccess: (updatedMessage?: any) => void
  editingMessage?: Message | null
}

/**
 * 消息发布弹窗组件
 *
 * 支持发布自定义内容和外部链接两种类型的消息
 */
export function MessagePublishModal({ onClose, onSuccess, editingMessage }: MessagePublishModalProps) {
  const [formData, setFormData] = useState({
    title: editingMessage?.title || '',
    content: editingMessage?.content || '',
    author: editingMessage?.author || '',
    type: (editingMessage?.type || 'content') as 'content' | 'link',
    external_url: editingMessage?.external_url || '',
    cover_image: editingMessage?.cover_image || ''
  })
  const [loading, setLoading] = useState(false)
  const [extractingImage, setExtractingImage] = useState(false)
  const [uploadingImage, setUploadingImage] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理表单字段变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 处理类型切换
  const handleTypeChange = (type: 'content' | 'link') => {
    setFormData(prev => ({
      ...prev,
      type,
      // 切换类型时清空相关字段
      content: type === 'link' ? '' : prev.content,
      external_url: type === 'content' ? '' : prev.external_url
    }))
  }

  // 处理图片上传
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      try {
        setUploadingImage(true)
        const result = await uploadImage(file, 'cover')
        handleInputChange('cover_image', result.url)
        toast({
          title: "图片上传成功",
          description: "封面图片已设置"
        })
      } catch (error) {
        console.error('图片上传失败:', error)
        toast({
          title: "图片上传失败",
          description: "请稍后重试",
          variant: "destructive"
        })
      } finally {
        setUploadingImage(false)
      }
    }
  }

  // 从URL提取图片
  const handleExtractImage = async () => {
    if (!formData.external_url) {
      toast({
        title: "请先输入URL",
        description: "需要先输入外部链接才能提取图片",
        variant: "destructive"
      })
      return
    }

    try {
      setExtractingImage(true)
      const response = await messageApi.extractImageFromUrl(formData.external_url)

      if (response.success && response.data.imageUrl) {
        handleInputChange('cover_image', response.data.imageUrl)
        toast({
          title: "图片提取成功",
          description: "已自动设置封面图片"
        })
      } else {
        // 处理没有找到图片的情况
        const message = response.message || "无法从该网页提取图片"
        toast({
          title: "未找到图片",
          description: `${message}，请手动上传封面图片`,
          variant: "default" // 使用默认样式而不是错误样式
        })
      }
    } catch (error) {
      console.error('提取图片失败:', error)

      // 根据错误类型显示不同的提示
      if (error.response) {
        const status = error.response.status
        const message = error.response.data?.message || error.message

        if (status === 404) {
          // 404错误（兼容旧版本API）
          toast({
            title: "未找到图片",
            description: "该网页没有合适的图片，请手动上传封面图片",
            variant: "default"
          })
        } else if (status === 400) {
          // 400错误（URL格式错误等）
          toast({
            title: "URL格式错误",
            description: message || "请检查输入的URL是否正确",
            variant: "destructive"
          })
        } else if (status === 500) {
          // 500错误（服务器内部错误）
          toast({
            title: "服务器错误",
            description: message || "服务器处理请求时出现错误，请稍后重试",
            variant: "destructive"
          })
        } else {
          // 其他HTTP错误
          toast({
            title: "请求失败",
            description: `HTTP ${status}: ${message}`,
            variant: "destructive"
          })
        }
      } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        // 网络错误
        toast({
          title: "网络连接失败",
          description: "请检查网络连接是否正常，或稍后重试",
          variant: "destructive"
        })
      } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        // 超时错误
        toast({
          title: "请求超时",
          description: "网页响应时间过长，请稍后重试",
          variant: "destructive"
        })
      } else {
        // 其他未知错误
        toast({
          title: "图片提取失败",
          description: error.message || "发生未知错误，请稍后重试",
          variant: "destructive"
        })
      }
    } finally {
      setExtractingImage(false)
    }
  }

  // 验证表单
  const validateForm = (status: 'draft' | 'published') => {
    // 草稿状态下只需要标题
    if (!formData.title.trim()) {
      toast({
        title: "请输入标题",
        variant: "destructive"
      })
      return false
    }

    // 发布状态下需要完整验证
    if (status === 'published') {
      if (!formData.author.trim()) {
        toast({
          title: "请输入作者",
          variant: "destructive"
        })
        return false
      }

      if (formData.type === 'content' && !formData.content.trim()) {
        toast({
          title: "请输入内容",
          variant: "destructive"
        })
        return false
      }

      if (formData.type === 'link' && !formData.external_url.trim()) {
        toast({
          title: "请输入外部链接",
          variant: "destructive"
        })
        return false
      }
    }

    return true
  }

  // 提交表单
  const handleSubmit = async (status: 'draft' | 'published') => {
    if (!validateForm(status)) return

    try {
      setLoading(true)
      let response

      if (editingMessage) {
        // 编辑模式：更新现有消息
        response = await messageApi.updateMessage(editingMessage.id, {
          ...formData,
          status
        })
      } else {
        // 新建模式：创建新消息
        response = await messageApi.createMessage({
          ...formData,
          status
        })
      }

      console.log('消息创建/更新响应:', JSON.stringify(response, null, 2))

      if (response.success) {
        // 根据状态显示不同的成功消息
        const action = editingMessage ? "更新" : (status === 'draft' ? "保存" : "发布")
        console.log(`${action}成功，创建的消息:`, JSON.stringify(response.data, null, 2))
        toast({
          title: `${action}成功`,
          description: editingMessage ? "消息已成功更新" : (status === 'draft' ? "草稿已保存，您可以稍后继续编辑" : "消息已成功发布"),
        })
        onSuccess(response.data)
      } else {
        const action = editingMessage ? "更新" : (status === 'draft' ? "保存" : "发布")
        console.error(`${action}失败:`, response)
        toast({
          title: `${action}失败`,
          description: response.message || `${action}失败，请稍后重试`,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('提交消息失败:', error)
      const action = editingMessage ? "更新" : (status === 'draft' ? "保存" : "发布")
      toast({
        title: `${action}失败`,
        description: "网络错误，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold">{editingMessage ? '编辑文章' : '新增文章'}</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-6">
          {/* 文章类型选择 */}
          <div>
            <Label className="text-sm font-medium mb-3 block">文章类型</Label>
            <div className="flex space-x-4">
              <Button
                variant={formData.type === 'content' ? 'default' : 'outline'}
                onClick={() => handleTypeChange('content')}
                className="flex items-center space-x-2"
              >
                <Type className="w-4 h-4" />
                <span>自定义内容</span>
              </Button>
              <Button
                variant={formData.type === 'link' ? 'default' : 'outline'}
                onClick={() => handleTypeChange('link')}
                className="flex items-center space-x-2"
              >
                <LinkIcon className="w-4 h-4" />
                <span>外部链接</span>
              </Button>
            </div>
          </div>

          {/* 标题 */}
          <div>
            <Label htmlFor="title" className="text-sm font-medium mb-2 block">
              标题 *
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="请输入消息标题"
              className="w-full"
            />
          </div>

          {/* 作者 */}
          <div>
            <Label htmlFor="author" className="text-sm font-medium mb-2 block">
              作者 *
            </Label>
            <Input
              id="author"
              value={formData.author}
              onChange={(e) => handleInputChange('author', e.target.value)}
              placeholder="请输入作者姓名"
              className="w-full"
            />
          </div>

          {/* 外部链接（仅链接类型显示） */}
          {formData.type === 'link' && (
            <div>
              <Label htmlFor="external_url" className="text-sm font-medium mb-2 block">
                外部链接 *
              </Label>
              <div className="flex space-x-2">
                <Input
                  id="external_url"
                  value={formData.external_url}
                  onChange={(e) => handleInputChange('external_url', e.target.value)}
                  placeholder="请输入外部链接URL"
                  className="flex-1"
                />
                <Button
                  onClick={handleExtractImage}
                  disabled={extractingImage || !formData.external_url}
                  variant="outline"
                >
                  {extractingImage ? '提取中...' : '提取图片'}
                </Button>
              </div>
            </div>
          )}

          {/* 内容（仅自定义内容类型显示） */}
          {formData.type === 'content' && (
            <div>
              <Label htmlFor="content" className="text-sm font-medium mb-2 block">
                内容 *
              </Label>
              <SimpleRichEditor
                value={formData.content}
                onChange={(value) => handleInputChange('content', value)}
                placeholder="请输入文章内容，支持富文本格式"
                className="w-full"
              />
            </div>
          )}

          {/* 封面图片 */}
          <div>
            <Label className="text-sm font-medium mb-2 block">封面图片</Label>
            <div className="space-y-3">
              <div className="flex space-x-2">
                <Input
                  value={formData.cover_image}
                  onChange={(e) => handleInputChange('cover_image', e.target.value)}
                  placeholder="图片URL或点击上传"
                  className="flex-1"
                />
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploadingImage}
                  variant="outline"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  {uploadingImage ? '上传中...' : '上传'}
                </Button>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              {formData.cover_image && (
                <div className="w-full h-32 relative border rounded">
                  <img
                    src={formData.cover_image}
                    alt="封面预览"
                    className="w-full h-full object-cover rounded"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          {!editingMessage && (
            <Button
              onClick={() => handleSubmit('draft')}
              disabled={loading}
              variant="outline"
            >
              <Save className="w-4 h-4 mr-2" />
              保存草稿
            </Button>
          )}
          <Button
            onClick={() => handleSubmit(editingMessage ? editingMessage.status : 'published')}
            disabled={loading}
            className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
          >
            <Send className="w-4 h-4 mr-2" />
            {editingMessage ? '保存更新' : '立即发布'}
          </Button>
        </div>
      </div>
    </div>
  )
}
