"use client"

import { useState, useEffect, memo, useMemo } from "react"
import { usePathname, useRouter } from "next/navigation"
import { Menu, X, Bell, ChevronDown, User } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { LoginModal } from "./login-modal"
import { useAuth, UserData } from "@/contexts/auth-context"
import { getUnreadNotificationCount } from "@/services/notification-service"
import { toast } from "@/components/ui/use-toast"
// 导入NotificationCenter组件和ClientOnly组件
import { NotificationCenterNew } from "@/components/notification/notification-center-new"
// 不再使用 ClientOnly 组件，而是直接在组件中实现客户端渲染逻辑
import { OptimizedLink } from "@/components/optimized-link"
import { debounce } from "@/lib/performance-utils"
import { logger, sanitizeData } from "@/utils/logger"

export const Navbar = memo(function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isPersonalMenuOpen, setIsPersonalMenuOpen] = useState(false)
  const [isMobilePersonalMenuOpen, setIsMobilePersonalMenuOpen] = useState(false)
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false)
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const { isLoggedIn, userData, logout, hasPermission } = useAuth()
  const pathname = usePathname()
  const router = useRouter()

  // 组件挂载后设置 isMounted 为 true
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 移除这里的permissionResults，将在navLinks定义后重新定义

  // 添加事件监听器，用于从其他组件触发登录弹窗
  useEffect(() => {
    const handleOpenLoginModal = (event: CustomEvent) => {
      setIsLoginModalOpen(true);
      if (event.detail?.isRegister) {
        setIsRegisterModalOpen(true);
      } else {
        setIsRegisterModalOpen(false);
      }
    };

    // 添加事件监听器
    window.addEventListener('open-login-modal', handleOpenLoginModal as EventListener);

    // 清理函数
    return () => {
      window.removeEventListener('open-login-modal', handleOpenLoginModal as EventListener);
    };
  }, []);

  // 监听localStorage中的登录状态变化 - 使用防抖优化
  useEffect(() => {
    // 检查登录状态 - 使用防抖函数减少不必要的刷新
    const checkLoginStatus = () => {
      const loggedInStorage = localStorage.getItem("isLoggedIn") === "true";

      // 如果localStorage中的登录状态与当前状态不一致，则刷新页面
      if (loggedInStorage !== isLoggedIn) {
        logger.debug("登录状态不一致，刷新页面", { loggedInStorage, isLoggedIn });
        // 使用路由器导航而不是强制刷新，提高性能
        router.refresh();
      }
    };

    // 使用防抖函数包装checkLoginStatus
    let timeoutId: NodeJS.Timeout;
    const debouncedCheckLoginStatus = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(checkLoginStatus, 100);
    };

    // 监听storage事件
    window.addEventListener("storage", debouncedCheckLoginStatus);

    // 页面加载时也检查一次
    debouncedCheckLoginStatus();

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener("storage", debouncedCheckLoginStatus);
    };
  }, [isLoggedIn, router]);

  // 不再需要isClient状态，因为我们使用ClientOnly组件

  // 导航链接
  const navLinks = [
    {
      name: "首页",
      href: "/",
      requirePermission: true, // 登录后需要权限才能显示
      permission: "home:access", // 需要的权限代码
      visibleWhenLoggedOut: true // 未登录时可见
    },
    {
      name: "家族专题",
      href: "/family-topic",
      requirePermission: true, // 登录后需要权限才能显示
      permission: "family:access", // 需要的权限代码
      visibleWhenLoggedOut: true // 未登录时可见
    },
    {
      name: "个人专题",
      href: "#",
      dropdown: true,
      items: [
        { name: "葛健豪", href: "/personal/ge-jianhao" },
        { name: "蔡和森", href: "/personal/cai-hesen" },
        { name: "向警予", href: "/personal/xiang-jingyu" },
        { name: "李富春", href: "/personal/li-fuchun" },
        { name: "蔡畅", href: "/personal/cai-chang" },
      ],
      requirePermission: true, // 登录后需要权限才能显示
      permission: "personal:access", // 需要的权限代码
      visibleWhenLoggedOut: true // 未登录时可见
    },
    {
      name: "知识库",
      href: "/knowledge",
      requirePermission: true, // 登录后需要权限才能显示
      permission: "knowledge:access", // 需要的权限代码
      visibleWhenLoggedOut: true // 未登录时可见
    },
    {
      name: "数据查询",
      href: "/data-mining",
      requirePermission: true, // 登录后需要权限才能显示
      permission: "data:access", // 需要的权限代码
      visibleWhenLoggedOut: true // 未登录时可见
    },
    {
      name: "AI研究助手",
      href: "/ai-assistant",
      requirePermission: true, // 登录后需要权限才能显示
      permission: "assistant:use", // 需要的权限代码
      visibleWhenLoggedOut: true // 未登录时可见
    },
    // 系统管理链接，只对有权限的人可见，未登录时不可见
    {
      name: "系统管理",
      href: "/system-management",
      requirePermission: true, // 需要权限才能显示
      permission: "system:access", // 需要的权限代码
      visibleWhenLoggedOut: false // 未登录时不可见
    },
  ]

  // 缓存权限检查结果，避免渲染期间重复调用
  const permissionResults = useMemo(() => {
    if (!isMounted || !isLoggedIn) return {}

    const results: Record<string, boolean> = {}
    navLinks.forEach(link => {
      if (link.requirePermission && link.permission) {
        results[link.permission] = hasPermission(link.permission)
      }
    })
    return results
  }, [isMounted, isLoggedIn, hasPermission])

  // 处理登录
  const handleLogin = (userInfo: { username: string; avatar?: string }) => {
    // 注意：这里只接收登录成功后的用户信息，不需要处理登录逻辑
    // 登录逻辑已经在LoginModal组件中处理完成

    // 关闭登录弹窗
    setIsLoginModalOpen(false);

    // 显示欢迎提示
    toast({
      title: "登录成功",
      description: `欢迎回来，${userInfo.username}！`,
    });

    // 刷新页面以更新状态
    router.refresh();
  }

  // 处理退出登录
  const handleLogout = () => {
    // 调用AuthContext的logout方法
    logout();
    // 关闭菜单
    setIsPersonalMenuOpen(false);

    // 显示退出提示
    toast({
      title: "已退出登录",
      description: "您已成功退出登录",
    });

    // 使用Next.js路由器导航，避免强制刷新
    setTimeout(() => {
      router.push("/");
      router.refresh();
    }, 100);
  }

  return (
    <>
      <nav className="fixed top-0 left-0 right-0 z-[9999] bg-white shadow-sm border-b border-gray-100">
        <div className="w-full px-4 lg:px-4">
          <div className="flex justify-between items-center h-16 w-full">
            {/* Logo */}
            <OptimizedLink href="/" className="flex items-center min-w-0 mr-4">
              <img
                src="/logo图标.png"
                alt="和富家族研究平台图标"
                className="h-6 w-6 sm:h-8 sm:w-8 mr-1 sm:mr-2 flex-shrink-0"
              />
              <span className="text-sm sm:text-base lg:text-xl font-bold text-[#1e8e3e] tracking-tight truncate">和富家族研究平台</span>
            </OptimizedLink>

            {/* 桌面导航 */}
            <div className="hidden lg:flex items-center space-x-1 flex-1 justify-center">
              {/* 显示导航项 - 避免水合错误 */}
              {isMounted && navLinks.map((link, index) => {
                // 未登录用户的处理
                if (!isLoggedIn) {
                  // 如果未登录且该导航项设置为未登录时不可见，则不显示
                  if (link.visibleWhenLoggedOut === false) {
                    return null;
                  }

                  // 显示导航项
                  if (link.dropdown) {
                    return (
                      <DropdownMenu key={`desktop-${link.name}-${index}`}>
                        <DropdownMenuTrigger asChild>
                          <button
                            className={`px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors ${
                              pathname.includes(link.href)
                                ? "text-[#1e8e3e] bg-green-50"
                                : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                            }`}
                          >
                            {link.name}
                            <ChevronDown className="ml-1 h-4 w-4" />
                          </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48 rounded-md shadow-lg">
                          {link.items?.map((item, itemIndex) => (
                            <DropdownMenuItem key={`desktop-${item.name}-${itemIndex}`} asChild>
                              <OptimizedLink
                                href={item.href}
                                className={`w-full transition-colors ${pathname === item.href ? "bg-green-50 text-[#1e8e3e]" : "text-gray-700 hover:bg-green-50 hover:text-[#1e8e3e]"}`}
                              >
                                {item.name}
                              </OptimizedLink>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    );
                  } else {
                    return (
                      <OptimizedLink
                        key={`desktop-${link.name}-${index}`}
                        href={link.href}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                          pathname === link.href
                            ? "text-[#1e8e3e] bg-green-50"
                            : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                        }`}
                      >
                        {link.name}
                      </OptimizedLink>
                    );
                  }
                }
                // 已登录用户的处理
                else if (link.requirePermission) {
                  const hasRequiredPermission = permissionResults[link.permission || ''] || false;

                  if (!hasRequiredPermission) {
                    return null;
                  }

                  if (link.dropdown) {
                    return (
                      <DropdownMenu key={`desktop-${link.name}-${index}`}>
                        <DropdownMenuTrigger asChild>
                          <button
                            className={`px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors ${
                              pathname.includes(link.href)
                                ? "text-[#1e8e3e] bg-green-50"
                                : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                            }`}
                          >
                            {link.name}
                            <ChevronDown className="ml-1 h-4 w-4" />
                          </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48 rounded-md shadow-lg">
                          {link.items?.map((item, itemIndex) => (
                            <DropdownMenuItem key={`desktop-${item.name}-${itemIndex}`} asChild>
                              <OptimizedLink
                                href={item.href}
                                className={`w-full transition-colors ${pathname === item.href ? "bg-green-50 text-[#1e8e3e]" : "text-gray-700 hover:bg-green-50 hover:text-[#1e8e3e]"}`}
                              >
                                {item.name}
                              </OptimizedLink>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    );
                  } else {
                    return (
                      <OptimizedLink
                        key={`desktop-${link.name}-${index}`}
                        href={link.href}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                          pathname === link.href
                            ? "text-[#1e8e3e] bg-green-50"
                            : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                        }`}
                      >
                        {link.name}
                      </OptimizedLink>
                    );
                  }
                }

                // 已登录用户，但不需要权限检查的导航项
                if (isLoggedIn && !link.requirePermission) {
                  if (link.dropdown) {
                    return (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <button
                            className={`px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors ${
                              pathname.includes(link.href)
                                ? "text-[#1e8e3e] bg-green-50"
                                : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                            }`}
                          >
                            {link.name}
                            <ChevronDown className="ml-1 h-4 w-4" />
                          </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48 rounded-md shadow-lg">
                          {link.items?.map((item) => (
                            <DropdownMenuItem key={item.name} asChild>
                              <OptimizedLink
                                href={item.href}
                                className={`w-full transition-colors ${pathname === item.href ? "bg-green-50 text-[#1e8e3e]" : "text-gray-700 hover:bg-green-50 hover:text-[#1e8e3e]"}`}
                              >
                                {item.name}
                              </OptimizedLink>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    );
                  } else {
                    return (
                      <OptimizedLink
                        href={link.href}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                          pathname === link.href
                            ? "text-[#1e8e3e] bg-green-50"
                            : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                        }`}
                      >
                        {link.name}
                      </OptimizedLink>
                    );
                  }
                }

                return null;
              })}
            </div>

            {/* 右侧操作区 */}
            <div className="hidden lg:flex items-center space-x-3 flex-shrink-0">
              {/* 服务器端渲染时显示占位符，客户端渲染后显示实际内容 */}
              {!isMounted ? (
                <div className="w-[180px] h-[38px]"></div>
              ) : isLoggedIn ? (
                  // 已登录状态
                  <>
                    <NotificationCenterNew />
                    <DropdownMenu open={isPersonalMenuOpen} onOpenChange={setIsPersonalMenuOpen}>
                      <DropdownMenuTrigger asChild>
                        <button className="flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 transition-colors">
                          {userData?.avatar ? (
                            <div className="w-8 h-8 rounded-full overflow-hidden shadow-sm">
                              <div
                                className="w-full h-full bg-center bg-cover"
                                style={{ backgroundImage: `url(${userData.avatar})` }}
                              />
                            </div>
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-[#1e8e3e] flex items-center justify-center text-white shadow-sm">
                              <User className="h-5 w-5" />
                            </div>
                          )}
                        </button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-56 rounded-md shadow-lg">
                        <div className="p-3 border-b">
                          <p className="font-medium">{userData?.username || "张文浩"}</p>
                          <p className="text-xs text-gray-500">{userData?.role === 'admin' ? '管理员' : '访问者'}</p>
                        </div>
                        <DropdownMenuItem asChild>
                          <OptimizedLink
                            href="/personal-center"
                            className="cursor-pointer hover:bg-green-50 hover:text-[#1e8e3e] transition-colors"
                          >
                            个人中心
                          </OptimizedLink>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <OptimizedLink
                            href="/personal-center/settings"
                            className="cursor-pointer hover:bg-green-50 hover:text-[#1e8e3e] transition-colors"
                          >
                            账号设置
                          </OptimizedLink>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600 cursor-pointer hover:bg-red-50 transition-colors"
                          onClick={handleLogout}
                        >
                          退出登录
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </>
                ) : (
                  // 未登录状态
                  <>
                    <button
                      onClick={() => setIsLoginModalOpen(true)}
                      className="px-4 py-1.5 rounded-md text-sm font-medium text-[#1e8e3e] border border-[#1e8e3e] hover:bg-green-50 transition-colors"
                    >
                      登录
                    </button>
                    <button
                      onClick={() => {
                        setIsLoginModalOpen(true)
                        setIsRegisterModalOpen(true)
                      }}
                      className="px-4 py-1.5 rounded-md text-sm font-medium text-white bg-[#f5a623] hover:bg-[#e69819] transition-colors"
                    >
                      注册
                    </button>
                  </>
                )}
            </div>

            {/* 移动端菜单按钮 */}
            <div className="lg:hidden flex items-center flex-shrink-0">
              {!isMounted ? (
                // 服务器端渲染占位符 - 与实际按钮尺寸完全一致
                <div className="p-1 sm:p-2 rounded-md w-[33px] h-[33px] sm:w-[36px] sm:h-[36px] flex items-center justify-center">
                  <div className="w-5 h-5"></div>
                </div>
              ) : (
                <button
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                  className="p-1 sm:p-2 rounded-md text-gray-600 hover:bg-gray-100 transition-colors touch-manipulation w-[33px] h-[33px] sm:w-[36px] sm:h-[36px] flex items-center justify-center"
                  aria-label={isMenuOpen ? "关闭菜单" : "打开菜单"}
                >
                  {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* 移动端菜单 */}
        {isMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-100">
            <div className="w-full px-4 lg:px-4 py-2">
              <div className="flex flex-col space-y-1">
                {/* 显示导航项 - 避免水合错误 */}
                {isMounted && navLinks.map((link, index) => {
                  // 未登录用户的处理
                  if (!isLoggedIn) {
                    // 如果未登录且该导航项设置为未登录时不可见，则不显示
                    if (link.visibleWhenLoggedOut === false) {
                      return null;
                    }

                    // 显示导航项
                    if (link.dropdown) {
                      return (
                        <div className="py-1" key={`mobile-${link.name}-${index}`}>
                          <button
                            onClick={() => setIsMobilePersonalMenuOpen(!isMobilePersonalMenuOpen)}
                            className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium flex items-center justify-between transition-colors ${
                              pathname.includes(link.href)
                                ? "text-[#1e8e3e] bg-green-50"
                                : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                            }`}
                          >
                            {link.name}
                            <ChevronDown className="h-4 w-4" />
                          </button>
                          {isMobilePersonalMenuOpen && (
                            <div className="ml-4 mt-1 space-y-1">
                              {link.items?.map((item, itemIndex) => (
                                <OptimizedLink
                                  key={`mobile-${item.name}-${itemIndex}`}
                                  href={item.href}
                                  className={`block px-3 py-2 rounded-md text-sm transition-colors ${
                                    pathname === item.href
                                      ? "text-[#1e8e3e] bg-green-50"
                                      : "text-gray-600 hover:text-[#1e8e3e] hover:bg-green-50"
                                  }`}
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  {item.name}
                                </OptimizedLink>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    } else {
                      return (
                        <OptimizedLink
                          key={`mobile-${link.name}-${index}`}
                          href={link.href}
                          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                            pathname === link.href
                              ? "text-[#1e8e3e] bg-green-50"
                              : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {link.name}
                        </OptimizedLink>
                      );
                    }
                  }
                  // 已登录用户的处理
                  else if (link.requirePermission) {
                    const hasRequiredPermission = permissionResults[link.permission || ''] || false;

                    if (!hasRequiredPermission) {
                      return null;
                    }

                    if (link.dropdown) {
                      return (
                        <div className="py-1" key={`mobile-${link.name}-${index}`}>
                          <button
                            onClick={() => setIsMobilePersonalMenuOpen(!isMobilePersonalMenuOpen)}
                            className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium flex items-center justify-between transition-colors ${
                              pathname.includes(link.href)
                                ? "text-[#1e8e3e] bg-green-50"
                                : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                            }`}
                          >
                            {link.name}
                            <ChevronDown className="h-4 w-4" />
                          </button>
                          {isMobilePersonalMenuOpen && (
                            <div className="ml-4 mt-1 space-y-1">
                              {link.items?.map((item, itemIndex) => (
                                <OptimizedLink
                                  key={`mobile-${item.name}-${itemIndex}`}
                                  href={item.href}
                                  className={`block px-3 py-2 rounded-md text-sm transition-colors ${
                                    pathname === item.href
                                      ? "text-[#1e8e3e] bg-green-50"
                                      : "text-gray-600 hover:text-[#1e8e3e] hover:bg-green-50"
                                  }`}
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  {item.name}
                                </OptimizedLink>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    } else {
                      return (
                        <OptimizedLink
                          key={`mobile-${link.name}-${index}`}
                          href={link.href}
                          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                            pathname === link.href
                              ? "text-[#1e8e3e] bg-green-50"
                              : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {link.name}
                        </OptimizedLink>
                      );
                    }
                  }

                  // 已登录用户，但不需要权限检查的导航项
                  if (isLoggedIn && !link.requirePermission) {
                    if (link.dropdown) {
                      return (
                        <div className="py-1" key={`mobile-${link.name}-${index}`}>
                          <button
                            onClick={() => setIsMobilePersonalMenuOpen(!isMobilePersonalMenuOpen)}
                            className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium flex items-center justify-between transition-colors ${
                              pathname.includes(link.href)
                                ? "text-[#1e8e3e] bg-green-50"
                                : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                            }`}
                          >
                            {link.name}
                            <ChevronDown className="h-4 w-4" />
                          </button>
                          {isMobilePersonalMenuOpen && (
                            <div className="ml-4 mt-1 space-y-1">
                              {link.items?.map((item) => (
                                <OptimizedLink
                                  key={item.name}
                                  href={item.href}
                                  className={`block px-3 py-2 rounded-md text-sm transition-colors ${
                                    pathname === item.href
                                      ? "text-[#1e8e3e] bg-green-50"
                                      : "text-gray-600 hover:text-[#1e8e3e] hover:bg-green-50"
                                  }`}
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  {item.name}
                                </OptimizedLink>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    } else {
                      return (
                        <OptimizedLink
                          href={link.href}
                          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                            pathname === link.href
                              ? "text-[#1e8e3e] bg-green-50"
                              : "text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50"
                          }`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {link.name}
                        </OptimizedLink>
                      );
                    }
                  }

                  return null;
                })}
              </div>

              {!isMounted ? (
                <div className="mt-4 h-[100px]"></div>
              ) : isLoggedIn ? (
                  // 已登录状态
                  <>
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-4">
                          <NotificationCenterNew />
                        </div>
                        <OptimizedLink
                          href="/personal-center"
                          className="flex items-center space-x-2 hover:bg-gray-50 rounded-lg p-2 transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {userData?.avatar ? (
                            <div className="w-8 h-8 rounded-full overflow-hidden shadow-sm">
                              <div
                                className="w-full h-full bg-center bg-cover"
                                style={{ backgroundImage: `url(${userData.avatar})` }}
                              />
                            </div>
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-[#1e8e3e] flex items-center justify-center text-white shadow-sm">
                              <User className="h-5 w-5" />
                            </div>
                          )}
                          <div>
                            <p className="text-sm font-medium">{userData?.username || "张文浩"}</p>
                            <p className="text-xs text-gray-500">{userData?.role === 'admin' ? '管理员' : '访问者'}</p>
                          </div>
                        </OptimizedLink>
                      </div>
                      <div className="space-y-2">
                        <OptimizedLink
                          href="/personal-center/settings"
                          className="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-[#1e8e3e] hover:bg-green-50 transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          账号设置
                        </OptimizedLink>
                      </div>
                    </div>
                    <button
                      onClick={handleLogout}
                      className="mt-4 block w-full px-3 py-2 rounded-md text-sm font-medium text-white bg-red-500 hover:bg-red-600 transition-colors shadow-sm"
                    >
                      退出登录
                    </button>
                  </>
                ) : (
                <div className="mt-4 flex space-x-2">
                  <button
                    onClick={() => {
                      setIsMenuOpen(false)
                      setIsLoginModalOpen(true)
                    }}
                    className="flex-1 px-3 py-1.5 rounded-md text-sm font-medium text-[#1e8e3e] border border-[#1e8e3e] hover:bg-green-50 transition-colors"
                  >
                    登录
                  </button>
                  <button
                    onClick={() => {
                      setIsMenuOpen(false)
                      setIsLoginModalOpen(true)
                      setIsRegisterModalOpen(true)
                    }}
                    className="flex-1 px-3 py-1.5 rounded-md text-sm font-medium text-white bg-[#f5a623] hover:bg-[#e69819] transition-colors"
                  >
                    注册
                  </button>
                </div>
                )}
            </div>
          </div>
        )}
      </nav>

      {/* 登录模态框 */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => {
          setIsLoginModalOpen(false)
          setIsRegisterModalOpen(false)
        }}
        isRegister={isRegisterModalOpen}
        onLogin={handleLogin}
      />
    </>
  )
})
