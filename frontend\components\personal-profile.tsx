"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import {
  X,
  ChevronDown,
  ChevronRight,
  Calendar,
  FileText,
  FileDown,
  Edit,
  Trash2,
  Plus,
  Upload,
  AlertCircle,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import dynamic from "next/dynamic"

// 不再需要PDF查看器组件，改为直接下载PDF文件

interface TimelineEvent {
  year: string
  event: string
}

// 新增的日期事件接口
interface DayEvent {
  day: number
  content: string
}

// 新增的月份事件接口
interface MonthEvent {
  month: number
  title: string
  days: DayEvent[]
}

// 新增的年份事件接口
interface YearEvent {
  year: number
  title: string
  months: MonthEvent[]
}

interface Material {
  id: string
  title: string
  description: string
  image: string
  pdfUrl?: string // PDF文件URL
}

// 导入评论服务和API服务
import commentService, { Comment as ApiComment } from "@/services/comment-service"
import * as apiService from "@/services/api-service"

// 本地评论接口，用于显示
interface Comment {
  id: string
  user: string
  content: string
  time: string
}

interface Person {
  id: string
  name: string
  nameEn: string
  birthDate: string
  deathDate: string
  birthPlace: string
  education: string
  portrait: string
  biography: string
  timeline: TimelineEvent[]
  // 新增的详细时间线
  detailedTimeline?: YearEvent[]
  materials: Material[]
  comments: Comment[]
}

interface PersonalProfileProps {
  person: Person
  isLoggedIn?: boolean
}

export function PersonalProfile({ person, isLoggedIn = false }: PersonalProfileProps) {
  const [newComment, setNewComment] = useState("")
  // 不使用本地数据初始化评论列表，而是从API获取
  const [comments, setComments] = useState<Comment[]>([])
  const [isLoadingComments, setIsLoadingComments] = useState(true)
  const commentContainerRef = useRef<HTMLDivElement>(null)
  const [hoveredCommentId, setHoveredCommentId] = useState<number | null>(null)
  const [selectedTimelineEvent, setSelectedTimelineEvent] = useState<TimelineEvent | null>(null)
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null)
  const [showCommentSuccess, setShowCommentSuccess] = useState(false)
  const [pdfLoadError, setPdfLoadError] = useState(false)

  // 新增状态：展开的年份和月份
  const [expandedYears, setExpandedYears] = useState<number[]>([])
  const [expandedMonths, setExpandedMonths] = useState<{ [key: number]: number[] }>({})
  const [selectedDay, setSelectedDay] = useState<{ year: number; month: number; day: number; content: string } | null>(
    null,
  )

  // 新增管理相关状态
  const [isManagementMode, setIsManagementMode] = useState(false)
  const [materials, setMaterials] = useState<Material[]>(person.materials)
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null)
  const [showAddMaterialModal, setShowAddMaterialModal] = useState(false)
  const [newMaterial, setNewMaterial] = useState<Partial<Material>>({
    title: "",
    description: "",
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [selectedPdfFile, setSelectedPdfFile] = useState<File | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const pdfFileInputRef = useRef<HTMLInputElement>(null)
  const [uploadError, setUploadError] = useState("")

  // 不再需要处理PDF加载失败，因为我们只提供下载功能

  // 加载评论列表
  useEffect(() => {
    if (person && person.id) {
      fetchComments()
    }
  }, [person])

  // 获取评论列表
  const fetchComments = async () => {
    try {
      // 设置加载状态
      setIsLoadingComments(true);

      // 从API获取数据
      const response = await commentService.getTopicComments(
        "personal_topic",
        person.id,
        { limit: 20 }
      );

      // 检查response是否有comments属性
      if (response && response.comments && Array.isArray(response.comments)) {
        // 将API返回的评论格式转换为本地格式
        const formattedComments = response.comments
          .filter(comment => comment.status === 'approved')
          .map(comment => ({
            id: comment.id,
            user: comment.user?.username || '匿名用户',
            content: comment.content || '',
            time: comment.created_at ? formatCommentTime(new Date(comment.created_at)) : '未知时间'
          }));

        setComments(formattedComments);
      } else {
        console.warn("获取的评论数据格式不正确:", response);
        // 显示错误提示
        toast({
          title: "数据格式错误",
          description: "评论数据格式不正确，请联系管理员",
          variant: "destructive"
        });
        setComments([]);
      }
    } catch (error) {
      console.error("获取评论列表失败:", error);
      // 显示错误提示
      toast({
        title: "加载评论失败",
        description: "无法连接到服务器，请检查网络连接或稍后重试",
        variant: "destructive"
      });
      setComments([]);
    } finally {
      // 无论成功还是失败，都结束加载状态
      setIsLoadingComments(false);
    }
  }

  // 格式化评论时间
  const formatCommentTime = (date: Date): string => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return "刚刚"
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`

    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
  }

  // 提交新留言
  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      toast({
        title: "评论内容不能为空",
        description: "请输入评论内容",
        variant: "destructive"
      });
      return;
    }

    // 检查登录状态
    const token = localStorage.getItem('hefamily_token');
    const loginFlag = localStorage.getItem('isLoggedIn') === 'true';
    const actuallyLoggedIn = !!token && loginFlag;

    console.log("提交评论前检查登录状态:", {
      propsIsLoggedIn: isLoggedIn,
      hasToken: !!token,
      loginFlag: loginFlag,
      actuallyLoggedIn: actuallyLoggedIn
    });

    if (!actuallyLoggedIn) {
      toast({
        title: "请先登录",
        description: "您需要登录后才能发表评论",
        variant: "destructive"
      });

      // 触发登录弹窗
      const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
      window.dispatchEvent(event);

      return;
    }

    try {
      console.log("开始提交评论");

      // 确保person.id是有效的
      if (!person || person.id === undefined || person.id === null) {
        console.error("无效的person对象:", person);
        toast({
          title: "提交评论失败",
          description: "无法识别当前专题页面",
          variant: "destructive"
        });
        return;
      }

      // 确保person.id是数字类型
      let topicId = 1; // 默认ID

      if (typeof person.id === 'number') {
        topicId = person.id;
      } else if (typeof person.id === 'string') {
        // 尝试将字符串转换为数字
        const parsedId = parseInt(person.id);
        if (!isNaN(parsedId)) {
          topicId = parsedId;
        } else {
          console.warn(`无法将person.id "${person.id}" 转换为数字，使用默认ID 1`);
        }
      } else {
        console.warn(`person.id 类型不支持: ${typeof person.id}，使用默认ID 1`);
      }

      console.log(`处理后的主题ID: ${topicId} (${typeof topicId})`);

      // 准备评论数据
      const commentData = {
        topic_id: topicId, // 使用数字ID
        topic_type: "personal_topic",
        topic_title: person.name || "个人专题",
        content: newComment.trim()
      };

      console.log("评论数据:", commentData);

      try {
        // 直接使用apiService发送请求，避免多层嵌套调用
        const response = await apiService.post('/comments', commentData);

        console.log("评论提交响应:", response);

        // 清空输入框
        setNewComment("");

        // 显示提交成功弹窗
        setShowCommentSuccess(true);
        toast({
          title: "评论提交成功",
          description: "您的评论已提交，等待管理员审核",
        });

        // 3秒后自动关闭弹窗
        setTimeout(() => {
          setShowCommentSuccess(false);
        }, 3000);

        // 刷新评论列表，显示最新数据
        fetchComments();
      } catch (apiError) {
        console.error("API调用失败:", apiError);

        // 显示错误信息
        toast({
          title: "评论提交失败",
          description: apiError.message || "服务器错误，请稍后再试",
          variant: "destructive"
        });

        throw apiError;
      }
    } catch (error) {
      console.error("提交评论失败:", error);

      // 根据错误类型显示不同的错误信息
      if (error.message === 'Network Error') {
        toast({
          title: "网络错误",
          description: "无法连接到服务器，请检查网络连接后重试",
          variant: "destructive"
        });
      } else if (error.message && error.message.includes('登录')) {
        toast({
          title: "登录已过期",
          description: "您的登录已过期，请重新登录后再试",
          variant: "destructive"
        });

        // 清除登录状态
        localStorage.removeItem('hefamily_token');
        localStorage.removeItem('isLoggedIn');

        // 触发登录弹窗
        setTimeout(() => {
          const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
          window.dispatchEvent(event);
        }, 1500);
      } else if (error.response && error.response.status === 401) {
        toast({
          title: "未授权",
          description: "您的登录已过期，请重新登录后再试",
          variant: "destructive"
        });

        // 清除登录状态
        localStorage.removeItem('hefamily_token');
        localStorage.removeItem('isLoggedIn');

        // 触发登录弹窗
        setTimeout(() => {
          const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
          window.dispatchEvent(event);
        }, 1500);
      } else if (error.response && error.response.status === 500) {
        toast({
          title: "服务器错误",
          description: "服务器内部错误，请联系管理员",
          variant: "destructive"
        });
      } else {
        toast({
          title: "提交评论失败",
          description: error.message || "服务器处理请求时出错，请稍后重试",
          variant: "destructive"
        });
      }
    }
  }

  // 弹幕动画效果
  useEffect(() => {
    if (!commentContainerRef.current || comments.length === 0) return

    const container = commentContainerRef.current
    const commentElements = container.querySelectorAll(".comment-item")

    commentElements.forEach((comment, index) => {
      const element = comment as HTMLElement
      const delay = index * 2 // 每个评论之间的延迟
      const duration = 15 // 动画持续时间

      element.style.animationDelay = `${delay}s`
      element.style.animationDuration = `${duration}s`
    })
  }, [comments])

  // 处理年份点击
  const toggleYear = (year: number) => {
    setExpandedYears((prev) => (prev.includes(year) ? prev.filter((y) => y !== year) : [...prev, year]))
  }

  // 处理月份点击
  const toggleMonth = (year: number, month: number) => {
    setExpandedMonths((prev) => {
      const yearMonths = prev[year] || []
      const newYearMonths = yearMonths.includes(month) ? yearMonths.filter((m) => m !== month) : [...yearMonths, month]

      return {
        ...prev,
        [year]: newYearMonths,
      }
    })
  }

  // 处理日期点击
  const handleDayClick = (year: number, month: number, day: number, content: string) => {
    setSelectedDay({ year, month, day, content })
  }

  // 获取月份名称
  const getMonthName = (month: number) => {
    const monthNames = [
      "一月",
      "二月",
      "三月",
      "四月",
      "五月",
      "六月",
      "七月",
      "八月",
      "九月",
      "十月",
      "十一月",
      "十二月",
    ]
    return monthNames[month - 1]
  }

  // 新增：处理资料点击
  const handleMaterialClick = (material: Material) => {
    if (isManagementMode) {
      setEditingMaterial(material)
    } else {
      setSelectedMaterial(material)
    }
  }

  // 新增：处理删除资料
  const handleDeleteMaterial = (id: string) => {
    if (confirm("确定要删除这个资料吗？")) {
      setMaterials(materials.filter((m) => m.id !== id))
    }
  }

  // 新增：处理更新资料
  const handleUpdateMaterial = () => {
    if (!editingMaterial) return

    setMaterials(materials.map((m) => (m.id === editingMaterial.id ? editingMaterial : m)))
    setEditingMaterial(null)
  }

  // 新增：处理添加资料
  const handleAddMaterial = () => {
    if (!newMaterial.title || (!selectedFile && !selectedPdfFile)) {
      setUploadError("请填写标题并上传图片或PDF文件")
      return
    }

    setUploadError("")

    // 模拟文件上传
    let imageUrl = "/placeholder.svg"
    let pdfUrl = undefined

    if (selectedFile) {
      // 模拟图片上传，实际应用中应该调用API上传文件
      imageUrl = URL.createObjectURL(selectedFile)
    }

    if (selectedPdfFile) {
      // 模拟PDF上传，实际应用中应该调用API上传文件
      pdfUrl = URL.createObjectURL(selectedPdfFile)
    }

    // 生成唯一的字符串ID
    const newId = (materials.length + 1).toString()

    const materialToAdd: Material = {
      id: newId,
      title: newMaterial.title || "",
      description: newMaterial.description || "",
      image: imageUrl,
      pdfUrl: pdfUrl,
    }

    setMaterials([...materials, materialToAdd])
    setNewMaterial({ title: "", description: "" })
    setSelectedFile(null)
    setSelectedPdfFile(null)
    setShowAddMaterialModal(false)
  }

  // 新增：处理图片文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0])
    }
  }

  // 新增：处理PDF文件选择
  const handlePdfFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedPdfFile(e.target.files[0])
    }
  }

  return (
    <div className="py-8 px-6">
      {/* 基本信息区 */}
      <section className="mb-12">
        <div className="bg-white rounded-lg shadow-sm overflow-hidden p-6">
          {/* 标题区域 */}
          <div className="border-b border-gray-100 pb-4 mb-4">
            {/* 检查是否为葛健豪、蔡和森、向警予、李富春或蔡畅页面，如果是则显示特殊标题 */}
            {person.id === 4 || person.id === "4" || person.id === "ge-jianhao" ? (
              <h1 className="text-3xl font-bold mb-2 text-[#1e7a43]">革命的母亲——葛健豪</h1>
            ) : person.id === 1 || person.id === "1" || person.id === "cai-hesen" ? (
              <h1 className="text-3xl font-bold mb-2 text-[#1e7a43]">中国共产党的创立者——蔡和森</h1>
            ) : person.id === 2 || person.id === "2" || person.id === "cai-chang" ? (
              <h1 className="text-3xl font-bold mb-2 text-[#1e7a43]">中国妇女运动的领导人——蔡畅</h1>
            ) : person.id === 3 || person.id === "3" || person.id === "li-fuchun" ? (
              <h1 className="text-3xl font-bold mb-2 text-[#1e7a43]">中国社会主义经济建设的重要领导人——李富春</h1>
            ) : person.id === 5 || person.id === "5" || person.id === "xiang-jingyu" ? (
              <h1 className="text-3xl font-bold mb-2 text-[#1e7a43]">中国妇女运动的先驱——向警予</h1>
            ) : (
              <h1 className="text-3xl font-bold mb-2">{person.name}</h1>
            )}
            {/* 对葛健豪、蔡和森、蔡畅、李富春和向警予页面隐藏拼音 */}
            {person.id !== 4 && person.id !== "4" && person.id !== "ge-jianhao" &&
              person.id !== 1 && person.id !== "1" && person.id !== "cai-hesen" &&
              person.id !== 2 && person.id !== "2" && person.id !== "cai-chang" &&
              person.id !== 3 && person.id !== "3" && person.id !== "li-fuchun" &&
              person.id !== 5 && person.id !== "5" && person.id !== "xiang-jingyu" && (
              <p className="text-gray-600">{person.nameEn}</p>
            )}
          </div>

          {/* 内容区域 - 照片浮动在文字左侧 */}
          <div className="clearfix">
            {/* 照片 - 浮动在左侧 */}
            <div className="float-left mr-6 mb-4 relative w-[200px] h-[260px] rounded-md overflow-hidden shadow-sm">
              <Image
                src={person.portrait || "/placeholder.svg"}
                alt={person.name}
                fill
                className="object-cover object-top"
              />
            </div>

            {/* 基本信息 - 对葛健豪、蔡和森、蔡畅、李富春和向警予页面隐藏这些信息 */}
            {person.id !== 4 && person.id !== "4" && person.id !== "ge-jianhao" &&
              person.id !== 1 && person.id !== "1" && person.id !== "cai-hesen" &&
              person.id !== 2 && person.id !== "2" && person.id !== "cai-chang" &&
              person.id !== 3 && person.id !== "3" && person.id !== "li-fuchun" &&
              person.id !== 5 && person.id !== "5" && person.id !== "xiang-jingyu" && (
              <div className="mb-4 text-sm">
                <div className="flex flex-wrap gap-x-6 gap-y-2">
                  <div>
                    <span className="text-gray-500">生卒年月：</span>
                    <span>{person.birthDate}-{person.deathDate}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">籍贯：</span>
                    <span>{person.birthPlace}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">学历：</span>
                    <span>{person.education}</span>
                  </div>
                </div>
              </div>
            )}

            {/* 传记内容 - 环绕照片，支持换行，处理标题 */}
            {(() => {
              // 葛健豪页面特殊处理
              if (person.id === 4 || person.id === "ge-jianhao") {
                // 直接硬编码葛健豪的传记内容，确保服务器端和客户端渲染一致
                return (
                  <div className="text-gray-700 leading-relaxed">
                    <p className="mb-4 text-indent-2em">
                      葛健豪（1865-1943），湖南省湘乡县（今双峰县）人，是蔡和森和蔡畅的母亲。自幼敢于冲破封建桎梏，倡导男女平等，热衷女子教育，是湖南著名的女子教育家；与中国同盟会第一个女委员唐群英和"鉴湖女侠"秋瑾，并称为"女界三杰"。年过五十还去长沙求学，后来又伴子女赴法勤工俭学，被舆论界称誉为廿世纪"惊人的妇人"。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      一生始终心系革命，支持毛泽东、蔡和森创办新民学会，不顾个人安危掩护党的地下工作，为党哺育和培养了一批优秀的革命儿女，家中走出了4名中共中央委员：儿子蔡和森、儿媳向警予、女儿蔡畅、女婿李富春。她虽非中共党员，却立传于《中共党史人物传》第六卷，是其中唯一的非中共党内人士。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      毛泽东曾评价道："蔡母葛健豪的家教是卓有成效的，她舍小家为大家，教育自己的子女都走上了革命的道路，是我们每个革命同志学习的榜样。"1943年，毛泽东在延安得知葛健豪逝世后，提笔写了"老妇人，新妇道；儿英烈，女英雄"的挽联。
                    </p>
                  </div>
                );
              }
              // 蔡和森页面特殊处理
              else if (person.id === 1 || person.id === "cai-hesen") {
                // 直接硬编码蔡和森的传记内容，确保服务器端和客户端渲染一致
                return (
                  <div className="text-gray-700 leading-relaxed">
                    <p className="mb-4 text-indent-2em">
                      蔡和森（1895-1931），中国无产阶级杰出的革命家、中国共产党早期卓越领导人之一，著名政治活动家、理论家、宣传家，1895年3月30日生于上海。1913年进入湖南省立第一师范读书，期间，同毛泽东等人一起组织进步团体新民学会，创办《湘江评论》，参加五四运动。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      1919年底赴法国勤工俭学，"猛看猛译"马克思主义著作，认真研究俄国十月革命的经验，成为坚定的马克思主义者；第一次旗帜鲜明地提出"正式成立一个中国共产党"的主张，明确提出共产党是无产阶级革命运动的"发动者、宣传者、先锋队、作战部"；并对中国共产党建党的理论、方针及组织原则也作了系统的阐述，为党的创建和早期党的建设作出重要贡献。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      1921年底回国，从事党的理论宣传工作，于1922年中共"二大"上当选中央委员，主办中共机关刊物《向导》，运用马克思列宁主义积极探索中国革命基本问题，对中国革命的性质、任务、前途、中国社会各阶级在革命中的地位和作用等问题，提出了许多正确观点。在党的三大、四大上，他当选为中央局委员，参与中央领导工作。1925年参与领导五卅反帝爱国运动。同年10月，受中共中央委派，赴莫斯科参加共产国际第五届执行委员会第六次扩大会议，会后任中共驻共产国际代表。1927年春回国，在5月举行的中共五届一中全会上当选为中央政治局委员、常委，随后又兼任中共中央秘书长。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      在党的"八七会议"上，他支持毛泽东的正确意见，主张土地革命和独立开展武装斗争，为党确立土地革命和武装反抗国民党反动派的总方针起了重要作用。1928年6月至7月在莫斯科举行的中共六大上，蔡和森认真总结土地革命战争初期的经验教训，指出中国社会具有不平衡的特点，阐明在农村开展武装斗争、建立红军、开辟割据局面的可能性，并当选为中央政治局委员、常委，兼任中央宣传部部长。1931年3月，中共中央决定派蔡和森到香港担任两广省委书记，不会说粤语的蔡和森明知危险，但还是义无反顾地投身于其中。他说："干革命，哪里需要就去哪里，不能只考虑个人的安危。"结果在香港出席海员工会时遭叛徒顾顺章指认被捕。8月牺牲于广州军政监狱酷刑之下，终年36岁。
                    </p>
                  </div>
                );
              }
              // 向警予页面特殊处理
              else if (person.id === 5 || person.id === "5" || person.id === "xiang-jingyu") {
                // 直接显示传记内容，不添加额外的标题
                return (
                  <div className="text-gray-700 leading-relaxed">
                    <p className="mb-4 text-indent-2em">
                      向警予（1895—1928），湖南省怀化市溆浦县人，是杰出的共产主义战士、忠诚的无产阶级革命家，党的早期卓越领导人，中国妇女运动的先驱和领袖。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      1918年参加毛泽东、蔡和森领导的"新民学会"，1919年她与 蔡畅等组织湖南女子工学世界会，为湖南女界勤工俭学运动的首创者。1919年赴法国勤工俭学，1920年5月与蔡和森在蒙达尼结婚。1922 年回国后加入中国共产党。在党的二大、三大、四大上当选为中央候 补委员、中央委员，四大后增补为中央局委员，是党的第一位女中央 委员和第一任妇女部长。1923年领导上海丝厂和烟厂的女工罢工。 1925年去苏联莫斯科大学学习。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      向警予理想信念坚定，常说："人总是要死的，但要看为什么而死，为革命为人民而死是光荣的。"1927年"七·一五"反革命政变后，武汉处于白色恐怖之中。作为公开身份的中共妇女领袖，向警予处境极度危险。但她坚决留在武汉坚持地下斗争。1928年3月20日因叛徒出卖在汉口法租界被捕，面对严刑拷打，始终坚贞不屈，于五一国际劳动节被残酷杀害，年仅33岁。
                    </p>
                    <p className="mb-4 text-indent-2em">
                      向警予牺牲后，蔡和森悲痛著悼文，"伟大的警予，英勇的警予，你没有死，你永远没有死!你不是和森个人的爱人，你是中国无产阶级永远的爱人!"
                    </p>
                  </div>
                );
              } else {
                // 其他人物页面的处理逻辑保持不变
                // 检查biography是否包含标题（第一行以"——"结尾）
                const lines = person.biography.split('\n');
                const hasTitle = lines.length > 0 && lines[0].includes('——');

                if (hasTitle) {
                  // 提取标题和正文
                  const title = lines[0];
                  const content = lines.slice(2).join('\n'); // 跳过标题和空行

                  return (
                    <>
                      <h2 className="text-xl font-bold mb-3 text-[#1e7a43]">{title}</h2>
                      <div className="text-gray-700 leading-relaxed">
                        {content.split('\n\n').map((paragraph, index) => (
                          <p key={index} className="mb-4 text-indent-2em">{paragraph}</p>
                        ))}
                      </div>
                    </>
                  );
                } else {
                  // 没有标题，直接显示全部内容
                  return (
                    <div className="text-gray-700 leading-relaxed">
                      {person.biography.split('\n\n').map((paragraph, index) => (
                        <p key={index} className="mb-4 text-indent-2em">{paragraph}</p>
                      ))}
                    </div>
                  );
                }
              }
            })()}
          </div>
        </div>
      </section>

      {/* 时空柱展示区 - 修改为层级结构 */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">人生轨迹</h2>

        {/* 如果有详细时间线，则使用新的层级结构 */}
        {person.detailedTimeline ? (
          <div className="space-y-4">
            {person.detailedTimeline.map((yearEvent) => (
              <div key={yearEvent.year} className="bg-white rounded-lg shadow-sm overflow-hidden">
                {/* 年份标题行 */}
                <div
                  className="p-4 border-l-4 border-[#1e7a43] flex justify-between items-center cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleYear(yearEvent.year)}
                >
                  <div>
                    <h3 className="font-bold text-lg">{yearEvent.year}年</h3>
                    <p className="text-gray-700">{yearEvent.title}</p>
                  </div>
                  {expandedYears.includes(yearEvent.year) ? (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-gray-500" />
                  )}
                </div>

                {/* 月份列表 - 仅在年份展开时显示 */}
                {expandedYears.includes(yearEvent.year) && (
                  <div className="pl-6 pr-4 pb-4 space-y-2">
                    {yearEvent.months.map((monthEvent) => (
                      <div key={monthEvent.month} className="border rounded-md overflow-hidden">
                        {/* 月份标题行 */}
                        <div
                          className="p-3 bg-gray-50 flex justify-between items-center cursor-pointer hover:bg-gray-100"
                          onClick={() => toggleMonth(yearEvent.year, monthEvent.month)}
                        >
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-[#1e7a43]" />
                            <span className="font-medium">{getMonthName(monthEvent.month)}</span>
                            <span className="ml-2 text-sm text-gray-500">{monthEvent.title}</span>
                          </div>
                          {expandedMonths[yearEvent.year]?.includes(monthEvent.month) ? (
                            <ChevronDown className="h-4 w-4 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-gray-500" />
                          )}
                        </div>

                        {/* 日期列表 - 仅在月份展开时显示 */}
                        {expandedMonths[yearEvent.year]?.includes(monthEvent.month) && (
                          <div className="p-3 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                            {monthEvent.days.map((dayEvent) => (
                              <div
                                key={dayEvent.day}
                                className="p-2 border rounded text-center cursor-pointer hover:bg-gray-50 hover:border-[#1e7a43]"
                                onClick={() =>
                                  handleDayClick(yearEvent.year, monthEvent.month, dayEvent.day, dayEvent.content)
                                }
                              >
                                <span className="font-medium">{dayEvent.day}日</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          // 如果没有详细时间线，则使用原来的简单时间线
          <div className="space-y-4">
            {person.timeline.map((item, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-sm overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setSelectedTimelineEvent(item)}
              >
                <div className="p-4 border-l-4 border-[#1e7a43]">
                  <h3 className="font-bold mb-2">{item.year}</h3>
                  <p className="text-gray-700">{item.event}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* 相关资料区 - 暂时注释掉 */}
      {/*
      <section className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">相关资料</h2>
          <div className="flex gap-2">
            <Button
              variant={isManagementMode ? "default" : "outline"}
              onClick={() => setIsManagementMode(!isManagementMode)}
              className={isManagementMode ? "bg-[#1e7a43] hover:bg-[#1e7a43]/90" : ""}
            >
              {isManagementMode ? "退出管理" : "管理资料"}
            </Button>
            {isManagementMode && (
              <Button
                onClick={() => setShowAddMaterialModal(true)}
                className="bg-[#f5a623] hover:bg-[#f5a623]/90 flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                添加资料
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {materials.map((material) => (
            <div
              key={material.id}
              className={`bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow ${
                isManagementMode ? "border-2 border-dashed border-gray-200 hover:border-[#1e7a43]" : ""
              }`}
              onClick={() => handleMaterialClick(material)}
            >
              <div className="relative h-48 w-full">
                {material.pdfUrl ? (
                  <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100">
                    <FileText className="h-16 w-16 text-gray-400" />
                    <span className="mt-2 text-sm font-medium text-gray-500">点击查看PDF文件</span>
                  </div>
                ) : (
                  <Image
                    src={material.image || "/placeholder.svg"}
                    alt={material.title}
                    fill
                    className="object-cover"
                  />
                )}

                {isManagementMode && (
                  <div className="absolute top-2 right-2 flex gap-2">
                    <button
                      className="p-1 bg-white rounded-full shadow-md hover:bg-gray-100"
                      onClick={(e) => {
                        e.stopPropagation()
                        setEditingMaterial(material)
                      }}
                    >
                      <Edit className="h-4 w-4 text-[#1e7a43]" />
                    </button>
                    <button
                      className="p-1 bg-white rounded-full shadow-md hover:bg-gray-100"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteMaterial(material.id)
                      }}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </button>
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="font-bold mb-2">{material.title}</h3>
                <p className="text-gray-700 text-sm">{material.description}</p>
                {material.pdfUrl && (
                  <div className="mt-2 flex items-center text-[#1e7a43]">
                    <FileText className="h-4 w-4 mr-1" />
                    <span className="text-sm">PDF文档</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>
      */}

      {/* 留言区 */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">读者留言</h2>

        {/* 弹幕区域 - 修改为支持单个留言悬停暂停 */}
        <div ref={commentContainerRef} className="relative h-32 mb-6 bg-gray-50 rounded-lg overflow-hidden">
          {isLoadingComments ? (
            // 加载状态
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1e7a43]"></div>
                <p className="mt-2 text-sm text-gray-500">加载评论中...</p>
              </div>
            </div>
          ) : comments.length > 0 ? (
            // 有评论数据
            <div className="comment-container w-full h-full">
              {comments.map((comment, index) => {
                // 计算每条评论的垂直位置，确保不重叠
                const topPosition = (index % 4) * 25 + 5 // 4行评论，每行占25%高度，加5px间距

                // 为每条评论生成不同的背景色
                const colors = [
                  "rgba(134, 38, 51, 0.1)",
                  "rgba(31, 41, 55, 0.1)",
                  "rgba(75, 85, 99, 0.1)",
                  "rgba(107, 114, 128, 0.1)",
                  "rgba(156, 163, 175, 0.1)",
                ]
                const bgColor = colors[index % colors.length]

                return (
                  <div
                    key={comment.id}
                    className="comment-item px-3 py-1 rounded-full text-sm"
                    style={{
                      top: `${topPosition}%`,
                      backgroundColor: bgColor,
                      color: "#111827",
                      // 只有当前悬停的留言才暂停动画，其他留言继续滚动
                      animationPlayState: hoveredCommentId === comment.id ? "paused" : "running",
                    }}
                    // 添加鼠标悬停和离开事件处理器
                    onMouseEnter={() => setHoveredCommentId(comment.id)}
                    onMouseLeave={() => setHoveredCommentId(null)}
                  >
                    <span className="font-bold mr-2">{comment.user}:</span>
                    <span>{comment.content}</span>
                    <span className="ml-2 text-xs text-gray-500">({comment.time})</span>
                  </div>
                )
              })}
            </div>
          ) : (
            // 没有评论数据
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">暂无评论</p>
            </div>
          )}
        </div>

        {/* 留言输入框 */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          {isLoggedIn ? (
            <>
              <Textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="写下您的留言..."
                className="mb-4 resize-none"
                rows={3}
              />
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-500">留言提交后需要管理员审核通过才会显示</p>
                <Button onClick={handleSubmitComment} className="bg-[#f5a623] hover:bg-[#f5a623]/90">
                  发表留言
                </Button>
              </div>
            </>
          ) : (
            <div className="text-center py-6">
              <p className="text-gray-500 mb-4">登录后才能发表留言</p>
              <Button
                className="bg-[#1e7a43] hover:bg-[#1e7a43]/90"
                onClick={() => {
                  // 触发登录弹窗
                  const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
                  window.dispatchEvent(event);
                }}
              >
                去登录
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* 时间线事件详情弹窗 */}
      {selectedTimelineEvent && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-3xl p-8 relative max-h-[80vh] overflow-y-auto">
            <button
              onClick={() => setSelectedTimelineEvent(null)}
              className="absolute top-6 right-6 text-gray-500 hover:text-gray-700"
            >
              <X className="h-6 w-6" />
            </button>
            <div className="border-l-4 border-[#1e7a43] pl-4 mb-6">
              <h3 className="text-2xl font-bold mb-2">{selectedTimelineEvent.year}</h3>
              <p className="text-lg text-gray-700 font-medium">{selectedTimelineEvent.event}</p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h4 className="text-lg font-semibold mb-4">详细描述</h4>
              <p className="text-gray-700 leading-relaxed">
                {/* 这里可以添加更详细的描述，目前使用相同内容并扩展 */}
                {selectedTimelineEvent.event}{" "}
                这是一段更为详细的历史事件描述，包含了更多的历史背景、人物关系和事件影响。在实际应用中，这里会展示完整的历史记录和相关资料，帮助读者深入了解这一历史时刻的重要性及其在整个历史进程中的地位。
              </p>
            </div>


          </div>
        </div>
      )}

      {/* 相关资料详情弹窗 - 暂时注释掉 */}
      {/*
      {selectedMaterial && !isManagementMode && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-3xl p-6 relative max-h-[90vh] overflow-y-auto">
            <button
              onClick={() => setSelectedMaterial(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>

            <h2 className="text-2xl font-bold mb-6">{selectedMaterial.title}</h2>

            <div className="mb-6">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-bold mb-2">概述</h3>
                <p className="text-gray-700">{selectedMaterial.description}</p>
              </div>
            </div>

            <div className="mb-6">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-bold mb-2">详细描述</h3>
                <p className="text-gray-700 whitespace-pre-line">
                  {selectedMaterial.description
                    ? selectedMaterial.description +
                      "\n\n这是关于该资料的更详细描述，包含了更多背景信息和历史意义。在实际应用中，这里会展示完整的资料内容和相关分析。"
                    : "暂无详细描述"}
                </p>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              {selectedMaterial.pdfUrl && (
                <>
                  {isLoggedIn ? (
                    <Button
                      onClick={() => {
                        const link = document.createElement("a")
                        link.href = selectedMaterial.pdfUrl!
                        link.download = selectedMaterial.title || "document.pdf"
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link)
                      }}
                      className="bg-[#1e7a43] hover:bg-[#1e7a43]/90 flex items-center gap-2"
                    >
                      <FileDown className="h-4 w-4" />
                      下载文件
                    </Button>
                  ) : (
                    <div className="flex flex-col items-end">
                      <Button
                        onClick={() => alert("请先登录后再下载文件")}
                        className="bg-gray-300 hover:bg-gray-400 flex items-center gap-2 cursor-not-allowed"
                      >
                        <FileDown className="h-4 w-4" />
                        下载文件
                      </Button>
                      <p className="text-sm text-gray-500 mt-1">请先登录后再下载文件</p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
      */}

      {/* 日期详情弹窗 */}
      {selectedDay && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-2xl p-6 relative">
            <button
              onClick={() => setSelectedDay(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>

            <h2 className="text-2xl font-bold mb-4">
              {selectedDay.year}年{selectedDay.month}月{selectedDay.day}日
            </h2>

            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-700 whitespace-pre-line">{selectedDay.content}</p>
            </div>


          </div>
        </div>
      )}

      {/* 编辑资料弹窗 - 暂时注释掉 */}
      {/*
      {editingMaterial && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-2xl p-6 relative">
            <button
              onClick={() => setEditingMaterial(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>

            <h2 className="text-2xl font-bold mb-6">编辑资料</h2>

            <div className="space-y-4">
              <div>
                <Label htmlFor="title">标题</Label>
                <Input
                  id="title"
                  value={editingMaterial.title}
                  onChange={(e) => setEditingMaterial({ ...editingMaterial, title: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={editingMaterial.description}
                  onChange={(e) => setEditingMaterial({ ...editingMaterial, description: e.target.value })}
                  rows={4}
                />
              </div>

              <div>
                <Label>当前图片</Label>
                <div className="mt-2 flex flex-col items-center">
                  <div className="relative h-48 w-full mb-4 border rounded-md overflow-hidden">
                    <Image
                      src={editingMaterial.image || "/placeholder.svg"}
                      alt={editingMaterial.title}
                      fill
                      className="object-contain"
                    />
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    替换图片
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        // 模拟上传，实际应用中应该调用API上传文件
                        const file = e.target.files[0]
                        const imageUrl = URL.createObjectURL(file)
                        setEditingMaterial({ ...editingMaterial, image: imageUrl })
                      }
                    }}
                  />
                </div>
              </div>

              <div>
                <Label>PDF文件</Label>
                {editingMaterial.pdfUrl ? (
                  <div className="flex items-center gap-2 mt-2">
                    <FileText className="h-5 w-5 text-[#1e7a43]" />
                    <span>已上传PDF文件</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingMaterial({ ...editingMaterial, pdfUrl: undefined })}
                      className="ml-auto"
                    >
                      移除
                    </Button>
                  </div>
                ) : (
                  <div className="mt-2">
                    <Button
                      variant="outline"
                      onClick={() => pdfFileInputRef.current?.click()}
                      className="flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      上传PDF文件
                    </Button>
                    <input
                      ref={pdfFileInputRef}
                      type="file"
                      accept="application/pdf"
                      className="hidden"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          // 模拟上传，实际应用中应该调用API上传文件
                          const file = e.target.files[0]
                          const pdfUrl = URL.createObjectURL(file)
                          setEditingMaterial({ ...editingMaterial, pdfUrl })
                        }
                      }}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <Button variant="outline" onClick={() => setEditingMaterial(null)}>
                取消
              </Button>
              <Button onClick={handleUpdateMaterial} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
                保存修改
              </Button>
            </div>
          </div>
        </div>
      )}
      */}

      {/* 添加资料弹窗 - 暂时注释掉 */}
      {/*
      {showAddMaterialModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-full max-w-2xl p-6 relative">
            <button
              onClick={() => {
                setShowAddMaterialModal(false)
                setNewMaterial({ title: "", description: "" })
                setSelectedFile(null)
                setSelectedPdfFile(null)
                setUploadError("")
              }}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>

            <h2 className="text-2xl font-bold mb-6">添加新资料</h2>

            {uploadError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-red-700 text-sm">{uploadError}</p>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <Label htmlFor="new-title">标题 *</Label>
                <Input
                  id="new-title"
                  value={newMaterial.title}
                  onChange={(e) => setNewMaterial({ ...newMaterial, title: e.target.value })}
                  placeholder="输入资料标题"
                />
              </div>

              <div>
                <Label htmlFor="new-description">描述</Label>
                <Textarea
                  id="new-description"
                  value={newMaterial.description}
                  onChange={(e) => setNewMaterial({ ...newMaterial, description: e.target.value })}
                  placeholder="输入资料描述"
                  rows={4}
                />
              </div>

              <div>
                <Label>上传图片</Label>
                <div className="mt-2 flex flex-col items-center">
                  {selectedFile ? (
                    <div className="relative h-48 w-full mb-4 border rounded-md overflow-hidden">
                      <Image
                        src={URL.createObjectURL(selectedFile) || "/placeholder.svg"}
                        alt="预览图片"
                        fill
                        className="object-contain"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-48 w-full mb-4 border rounded-md bg-gray-50">
                      <div className="text-center">
                        <FileText className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                        <p className="text-gray-500">未选择图片</p>
                      </div>
                    </div>
                  )}
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    {selectedFile ? "更换图片" : "上传图片"}
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </div>
              </div>

              <div>
                <Label>PDF文件</Label>
                {selectedPdfFile ? (
                  <div className="mt-2 p-2 bg-gray-50 rounded-md flex items-center">
                    <div className="h-10 w-10 bg-gray-200 rounded flex items-center justify-center mr-3">
                      <FileText className="h-5 w-5 text-gray-500" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{selectedPdfFile.name}</p>
                      <p className="text-xs text-gray-500">{Math.round(selectedPdfFile.size / 1024)} KB</p>
                    </div>
                    <button className="ml-2 text-gray-500 hover:text-gray-700" onClick={() => setSelectedPdfFile(null)}>
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <div className="mt-2">
                    <Button
                      variant="outline"
                      onClick={() => pdfFileInputRef.current?.click()}
                      className="flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      上传PDF文件
                    </Button>
                    <input
                      ref={pdfFileInputRef}
                      type="file"
                      accept="application/pdf"
                      className="hidden"
                      onChange={handlePdfFileChange}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowAddMaterialModal(false)
                  setNewMaterial({ title: "", description: "" })
                  setSelectedFile(null)
                  setSelectedPdfFile(null)
                  setUploadError("")
                }}
              >
                取消
              </Button>
              <Button onClick={handleAddMaterial} className="bg-[#1e7a43] hover:bg-[#1e7a43]/90">
                添加资料
              </Button>
            </div>
          </div>
        </div>
      )}
      */}

      {/* 留言提交成功弹窗 */}
      {showCommentSuccess && (
        <div className="fixed bottom-8 right-8 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50">
          <div className="flex items-center">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <p>留言已提交，等待审核通过后显示</p>
          </div>
        </div>
      )}
    </div>
  )
}
