"use client"

import { useState, useRef, useCallback } from "react"
import { Bold, Italic, Underline, Image, Link, List, ListOrdered, Type } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface SimpleRichEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
}

/**
 * 简单富文本编辑器组件
 *
 * 支持基本的文本格式化功能，不依赖外部库
 */
export function SimpleRichEditor({ value, onChange, placeholder, className }: SimpleRichEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const [showImageDialog, setShowImageDialog] = useState(false)
  const [showLinkDialog, setShowLinkDialog] = useState(false)
  const [imageUrl, setImageUrl] = useState('')
  const [linkUrl, setLinkUrl] = useState('')
  const [linkText, setLinkText] = useState('')

  // 执行格式化命令
  const execCommand = useCallback((command: string, value?: string) => {
    document.execCommand(command, false, value)
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML)
    }
  }, [onChange])

  // 处理内容变化
  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML)
    }
  }, [onChange])

  // 插入图片
  const insertImage = () => {
    if (imageUrl.trim()) {
      execCommand('insertImage', imageUrl)
      setImageUrl('')
      setShowImageDialog(false)
    }
  }

  // 插入链接
  const insertLink = () => {
    if (linkUrl.trim()) {
      if (linkText.trim()) {
        // 如果有链接文本，先插入文本再创建链接
        execCommand('insertText', linkText)
        const selection = window.getSelection()
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          range.setStart(range.endContainer, range.endOffset - linkText.length)
          selection.removeAllRanges()
          selection.addRange(range)
        }
      }
      execCommand('createLink', linkUrl)
      setLinkUrl('')
      setLinkText('')
      setShowLinkDialog(false)
    }
  }

  // 设置字体大小
  const setFontSize = (size: string) => {
    execCommand('fontSize', size)
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center space-x-1 p-2 border-b bg-gray-50">
        {/* 字体大小 */}
        <select
          defaultValue="3"
          onChange={(e) => setFontSize(e.target.value)}
          className="text-sm border rounded px-2 py-1"
        >
          <option value="1">小</option>
          <option value="3">正常</option>
          <option value="4">大</option>
          <option value="5">较大</option>
          <option value="6">很大</option>
        </select>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        {/* 格式化按钮 */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('bold')}
          className="p-1"
        >
          <Bold size={16} />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('italic')}
          className="p-1"
        >
          <Italic size={16} />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('underline')}
          className="p-1"
        >
          <Underline size={16} />
        </Button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        {/* 列表按钮 */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('insertUnorderedList')}
          className="p-1"
        >
          <List size={16} />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('insertOrderedList')}
          className="p-1"
        >
          <ListOrdered size={16} />
        </Button>

        <div className="w-px h-6 bg-gray-300 mx-2" />

        {/* 插入按钮 */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setShowImageDialog(true)}
          className="p-1"
        >
          <Image size={16} />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setShowLinkDialog(true)}
          className="p-1"
        >
          <Link size={16} />
        </Button>
      </div>

      {/* 编辑区域 */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleContentChange}
        dangerouslySetInnerHTML={{ __html: value }}
        className="min-h-[200px] p-4 focus:outline-none"
        style={{ wordBreak: 'break-word' }}
        data-placeholder={placeholder}
      />

      {/* 插入图片对话框 */}
      {showImageDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-bold mb-4">插入图片</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="imageUrl">图片URL</Label>
                <Input
                  id="imageUrl"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  placeholder="请输入图片URL"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowImageDialog(false)}>
                  取消
                </Button>
                <Button onClick={insertImage}>
                  插入
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 插入链接对话框 */}
      {showLinkDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-bold mb-4">插入链接</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="linkText">链接文本</Label>
                <Input
                  id="linkText"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  placeholder="请输入链接文本"
                />
              </div>
              <div>
                <Label htmlFor="linkUrl">链接URL</Label>
                <Input
                  id="linkUrl"
                  value={linkUrl}
                  onChange={(e) => setLinkUrl(e.target.value)}
                  placeholder="请输入链接URL"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowLinkDialog(false)}>
                  取消
                </Button>
                <Button onClick={insertLink}>
                  插入
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
