"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback, useRef } from "react"
import userService, { User } from "@/services/user-service"
import permissionService from "@/services/permission-service"
import { toast } from "@/components/ui/use-toast"
import { logger, sanitizeData } from "@/utils/logger"

// 定义用户角色类型
export interface Role {
  id: number
  name: string
  description?: string
}

// 定义用户数据类型
export interface UserData {
  id: number
  username: string
  email: string
  phone?: string
  avatar?: string
  role: string
  role_id?: number
  role_name?: string
  is_active: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

// 定义认证上下文类型
interface AuthContextType {
  isLoggedIn: boolean
  userData: UserData | null
  hasPermission: (permission: string) => boolean
  login: (credentials: { username: string; password: string }) => Promise<boolean>
  logout: () => void
  updateUserData: () => Promise<void>
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证上下文提供者组件
export function AuthProvider({ children }: { children: ReactNode }) {
  // 初始化时直接从localStorage读取登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(() => {
    // 只在客户端执行
    if (typeof window !== 'undefined') {
      return localStorage.getItem('isLoggedIn') === 'true'
    }
    return false
  })
  const [userData, setUserData] = useState<UserData | null>(null)

  // 权限缓存，使用useRef避免渲染期间的状态更新
  const permissionCacheRef = useRef<Map<string, boolean>>(new Map())

  // 检查用户是否有特定权限 - 使用缓存优化性能
  const hasPermission = useCallback((permission: string) => {
    if (!isLoggedIn) return false

    // 检查缓存
    if (permissionCacheRef.current.has(permission)) {
      return permissionCacheRef.current.get(permission)!
    }

    // 使用权限服务检查权限
    const result = permissionService.hasPermissionInStorage(permission)

    // 缓存结果 - 使用ref避免触发重新渲染
    permissionCacheRef.current.set(permission, result)

    return result
  }, [isLoggedIn])

  // 清除权限缓存
  const clearPermissionCache = useCallback(() => {
    permissionCacheRef.current.clear()
  }, [])

  // 从localStorage获取用户数据
  const getUserDataFromStorage = (): UserData | null => {
    try {
      const userDataStr = localStorage.getItem('hefamily_user_data')
      if (userDataStr) {
        return JSON.parse(userDataStr)
      }
    } catch (error) {
      logger.error('从localStorage获取用户数据失败:', error)
    }
    return null
  }

  // 保存用户数据到localStorage
  const saveUserDataToStorage = (user: UserData) => {
    try {
      localStorage.setItem('hefamily_user_data', JSON.stringify(user))
    } catch (error) {
      logger.error('保存用户数据到localStorage失败:', error)
    }
  }

  // 更新用户数据
  const updateUserData = async () => {
    try {
      logger.debug('开始更新用户数据')
      const user = await userService.getCurrentUser()

      if (user) {
        logger.debug('成功获取用户数据:', sanitizeData(user))
        setUserData(user)
        setIsLoggedIn(true)

        // 保存用户数据到localStorage
        saveUserDataToStorage(user)

        // 在localStorage中保存登录状态
        localStorage.setItem('isLoggedIn', 'true')

        // 获取并保存用户权限
        try {
          const userPermissions = await permissionService.getCurrentUserPermissions()
          const permissionCodes = userPermissions.map(p => p.code)
          permissionService.saveUserPermissionsToStorage(permissionCodes)

          // 清除权限缓存，确保使用最新权限
          clearPermissionCache()
        } catch (permError) {
          logger.error('获取用户权限失败:', sanitizeData(permError))
          // 确保用户至少有基本访问权限
          permissionService.saveUserPermissionsToStorage(['basic:access'])
        }
      } else {
        logger.warn('API返回的用户数据为空')

        // 尝试从localStorage获取用户数据
        const storedUser = getUserDataFromStorage()
        if (storedUser) {
          logger.debug('使用localStorage中的用户数据:', sanitizeData(storedUser))
          setUserData(storedUser)
          setIsLoggedIn(true)
        } else {
          logger.error('无法获取用户数据，清除登录状态')
          setIsLoggedIn(false)
          setUserData(null)
          permissionService.clearPermissionsFromStorage()
          localStorage.removeItem('isLoggedIn')
        }
      }
    } catch (error) {
      logger.error('获取用户信息失败:', sanitizeData(error))

      // 尝试从localStorage获取用户数据
      const storedUser = getUserDataFromStorage()
      if (storedUser) {
        logger.debug('API调用失败，使用localStorage中的用户数据:', sanitizeData(storedUser))
        setUserData(storedUser)
        setIsLoggedIn(true)
      } else {
        // 用户未登录或token无效，确保状态正确
        logger.error('无法获取用户数据，清除登录状态')
        setIsLoggedIn(false)
        setUserData(null)
        permissionService.clearPermissionsFromStorage()
        localStorage.removeItem('isLoggedIn')
      }
    }
  }

  // 登录处理函数
  const login = async (credentials: { username: string; password: string }): Promise<boolean> => {
    try {
      // 开始登录流程

      // 验证参数
      if (!credentials.username || !credentials.password) {
        logger.info("登录参数无效");
        throw new Error("用户名和密码不能为空");
      }

      // 调用API进行登录
      const response = await userService.login({
        username: credentials.username.trim(),
        password: credentials.password
      });

      logger.debug("登录API响应:", sanitizeData(response));

      // 处理不同的响应格式
      let token = '';
      let user = null;

      // 格式1: { token, user }
      if (response.token && response.user) {
        logger.debug("处理格式1: 响应包含token和user字段");
        token = response.token;
        user = response.user;
      }
      // 格式2: { data: { token, user } }
      else if (response.data && response.data.token && response.data.user) {
        logger.debug("处理格式2: 响应的data字段包含token和user");
        token = response.data.token;
        user = response.data.user;
      }
      // 格式3: { success: true, message: '登录成功', data: { token, user } }
      else if (response.success === true && response.data && response.data.token && response.data.user) {
        logger.debug("处理格式3: 响应包含success和data字段");
        token = response.data.token;
        user = response.data.user;
      }
      // 其他格式
      else {
        logger.error("登录响应格式不匹配:", sanitizeData(response));
        throw new Error("登录响应格式错误");
      }

      if (!token || !user) {
        logger.error("登录响应缺少token或user:", sanitizeData(response));
        throw new Error("登录响应缺少必要信息");
      }

      logger.debug("登录成功，保存token和用户数据");

      // 保存token
      userService.setAuthToken(token);

      // 设置用户数据
      setUserData(user);

      // 保存用户数据到localStorage
      saveUserDataToStorage(user);

      // 在localStorage中保存登录状态
      localStorage.setItem('isLoggedIn', 'true');

      // 设置登录状态
      setIsLoggedIn(true);

      // 清除权限缓存，确保使用最新权限
      clearPermissionCache();

      try {
        // 获取并保存用户权限
        logger.debug("获取用户权限");

        // 使用改进后的权限服务获取权限
        // 该服务已经处理了各种错误情况，并会返回默认权限
        const userPermissions = await permissionService.getCurrentUserPermissions();
        logger.debug("获取到的用户权限:", userPermissions);

        // 权限已经在服务中保存到本地存储，这里不需要再次保存
      } catch (permError) {
        logger.error("获取用户权限过程中发生错误:", sanitizeData(permError));
        // 权限获取失败不影响登录流程
        // 确保用户至少有基本访问权限
        permissionService.saveUserPermissionsToStorage(['basic:access']);
      }

      return true;
    } catch (error: any) {
      // 使用info级别记录登录失败信息，而不是error级别
      logger.info('登录处理:', sanitizeData(error));

      // 提供更详细的错误信息
      let errorMessage = "用户名或密码错误，请重试";

      if (error.response?.status === 404) {
        // 检查响应数据中是否包含"用户不存在"的消息
        if (error.response?.data?.message && error.response?.data?.message.includes('用户不存在')) {
          errorMessage = "用户不存在";
        } else {
          errorMessage = "用户不存在";
        }
      } else if (error.message === 'Network Error') {
        errorMessage = "网络错误，无法连接到服务器";
      } else if (error.response?.status === 401) {
        errorMessage = "密码错误";
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "登录失败",
        description: errorMessage,
        variant: "destructive"
      });

      return false;
    }
  }

  // 登出处理函数
  const logout = () => {
    try {
      // 清除localStorage中的登录状态和用户数据
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('hefamily_user_data')

      // 调用API进行登出
      userService.logout()

      // 清除权限
      permissionService.clearPermissionsFromStorage()

      // 清除权限缓存
      clearPermissionCache()

      // 清除本地状态
      setUserData(null)
      setIsLoggedIn(false)

      logger.debug("登出成功，已清除登录状态")
    } catch (error) {
      logger.error('登出失败:', sanitizeData(error))
      toast({
        title: "登出失败",
        description: "无法完成登出操作，请稍后再试",
        variant: "destructive"
      })

      // 即使API调用失败，也要确保本地状态被清除
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('hefamily_user_data')
      setIsLoggedIn(false)
      setUserData(null)
    }
  }

  // 初始化时从API获取用户状态
  useEffect(() => {
    // 检查是否已登录并获取用户数据
    const checkAuth = async () => {
      try {
        // 如果已登录，则获取用户数据
        if (isLoggedIn) {
          const token = userService.getAuthToken()
          if (token) {
            // 如果有token，则获取用户数据
            try {
              const user = await userService.getCurrentUser()
              if (user) {
                setUserData(user)

                // 获取并保存用户权限
                try {
                  const userPermissions = await permissionService.getCurrentUserPermissions()
                  const permissionCodes = userPermissions.map(p => p.code)
                  permissionService.saveUserPermissionsToStorage(permissionCodes)
                } catch (permError) {
                  console.error("获取用户权限失败:", permError)
                  // 确保用户至少有基本访问权限
                  permissionService.saveUserPermissionsToStorage(['basic:access'])
                }
              } else {
                // 用户数据为空但API调用成功，这是一种异常情况
                console.warn("API返回的用户数据为空")
                // 不清除登录状态，可能是临时API问题
              }
            } catch (userError) {
              // 获取用户数据失败，但不立即清除登录状态
              console.error("获取用户数据失败:", userError)
              // 如果是401错误，则清除登录状态
              if (userError.response && userError.response.status === 401) {
                console.warn("用户未授权，清除登录状态")
                setIsLoggedIn(false)
                localStorage.removeItem('isLoggedIn')
              }
              // 其他错误可能是临时网络问题，保持登录状态
            }
          } else {
            // 没有token但状态为已登录，清除登录状态
            console.warn("已登录但没有token，清除登录状态")
            setIsLoggedIn(false)
            localStorage.removeItem('isLoggedIn')
          }
        } else {
          // 未登录，确保用户数据为空
          setUserData(null)
          // 确保localStorage中的登录状态为false
          localStorage.removeItem('isLoggedIn')
        }
      } catch (error) {
        console.error("检查登录状态出错:", error)
      }
    }

    // 监听storage事件，处理在其他标签页中的登录/登出操作
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'isLoggedIn') {
        const newIsLoggedIn = event.newValue === 'true'
        if (newIsLoggedIn !== isLoggedIn) {
          setIsLoggedIn(newIsLoggedIn)
          if (!newIsLoggedIn) {
            // 如果新状态是未登录，则清除用户数据
            setUserData(null)
          }
        }
      } else if (event.key === 'hefamily_token' && !event.newValue) {
        // token被清除，清除登录状态
        setIsLoggedIn(false)
        setUserData(null)
        localStorage.removeItem('isLoggedIn')
      }
    }

    // 只有在客户端才执行
    if (typeof window !== 'undefined') {
      checkAuth()
      window.addEventListener('storage', handleStorageChange)
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('storage', handleStorageChange)
      }
    }
  }, [isLoggedIn]) // eslint-disable-line react-hooks/exhaustive-deps
  // 我们在这里添加了isLoggedIn作为依赖项，以便在登录状态变化时重新检查

  return (
    <AuthContext.Provider value={{ isLoggedIn, userData, hasPermission, login, logout, updateUserData }}>
      {children}
    </AuthContext.Provider>
  )
}

// 自定义钩子，用于访问认证上下文
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
