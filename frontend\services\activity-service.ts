import apiService from './api-service';
import { logger, sanitizeData } from '@/utils/logger';

/**
 * 活动附件接口定义
 */
export interface ActivityAttachment {
  id: string;
  name: string;
  original_name: string; // 原始文件名
  url: string;
  type: string;
  size: string;
  created_at: string;
  uploader_id?: string;
  uploader?: {
    id: string;
    username: string;
  };
}

/**
 * 活动接口定义
 */
export interface Activity {
  id: string;
  title: string;
  date: string;
  description: string;
  image: string;
  status: 'published' | 'draft' | 'archived';
  attachments: ActivityAttachment[];
  creator_id?: string;
  creator?: {
    id: string;
    username: string;
  };
  created_at: string;
  updated_at: string;
}

/**
 * 活动服务
 *
 * 处理活动的创建、查询、更新、删除等操作
 */
class ActivityService {
  /**
   * 获取活动列表
   * @param params 查询参数
   * @returns 活动列表和分页信息
   */
  async getActivities(params: {
    page?: number;
    limit?: number;
    status?: string;
  }) {
    // 检查是否在管理模式下
    const isManagementMode = params.status === '';

    // 发送请求
    try {
      const response = await apiService.get('/activities', params);

      return {
        activities: (response.data?.activities || response.activities || []) as Activity[],
        pagination: response.data?.pagination || response.pagination
      };
    } catch (error) {
      // 如果是管理模式但请求失败，尝试再次请求但不带status参数
      if (isManagementMode) {
        const newParams = { ...params };
        delete newParams.status;

        try {
          const response = await apiService.get('/activities', newParams);

          return {
            activities: (response.data?.activities || response.activities || []) as Activity[],
            pagination: response.data?.pagination || response.pagination
          };
        } catch (retryError) {
          throw retryError;
        }
      }

      throw error;
    }
  }

  /**
   * 获取活动详情
   * @param id 活动ID
   * @returns 活动详情
   */
  async getActivityById(id: string) {
    const response = await apiService.get(`/activities/${id}`);

    // 获取活动附件
    try {
      const attachments = await this.getActivityAttachments(id);
      if (response && attachments) {
        return {
          ...response,
          attachments
        } as Activity;
      }
    } catch (error) {
      // 获取附件失败，继续返回活动数据
    }

    return response as Activity;
  }

  /**
   * 创建活动
   * @param data 活动数据
   * @returns 创建结果
   */
  async createActivity(data: {
    title: string;
    date: string;
    description: string;
    image?: string;
    status?: 'published' | 'draft' | 'archived';
  }) {
    const response = await apiService.post('/activities', data);
    return response;
  }

  /**
   * 更新活动
   * @param id 活动ID
   * @param data 活动数据
   * @returns 更新结果
   */
  async updateActivity(id: string, data: {
    title?: string;
    date?: string;
    description?: string;
    image?: string;
    status?: 'published' | 'draft' | 'archived';
  }) {
    const response = await apiService.put(`/activities/${id}`, data);
    return response;
  }

  /**
   * 删除活动
   * @param id 活动ID
   * @returns 删除结果
   */
  async deleteActivity(id: string) {
    const response = await apiService.del(`/activities/${id}`);
    return response;
  }

  /**
   * 上传活动图片
   * @param file 图片文件
   * @returns 上传结果
   */
  async uploadActivityImage(file: File) {
    const formData = new FormData();
    formData.append('image', file);
    const response = await apiService.upload('/activities/upload-image', formData);
    return response;
  }

  /**
   * 获取活动附件列表
   * @param activityId 活动ID
   * @returns 附件列表
   */
  async getActivityAttachments(activityId: string) {
    const response = await apiService.get(`/activities/${activityId}/attachments`);
    return (response.data || response || []) as ActivityAttachment[];
  }

  /**
   * 上传活动附件
   * @param activityId 活动ID
   * @param file 附件文件
   * @returns 上传结果
   */
  async uploadActivityAttachment(activityId: string, file: File) {
    // 文件大小检查（不限制大小）
    const warningSize = 100 * 1024 * 1024; // 100MB

    // 检查文件类型 - 允许图片和文档文件
    const allowedTypes = [
      // 图片文件
      'image/jpeg', 'image/png', 'image/gif',
      // 文档文件
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'application/rtf',
      'application/zip'
    ];
    if (!allowedTypes.includes(file.type)) {
      throw new Error('不支持的文件类型，请上传图片或常见文档格式');
    }

    // 创建表单数据
    const formData = new FormData();
    formData.append('file', file);

    // 尝试上传，最多重试2次
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      try {
        attempts++;

        // 上传文件
        const response = await apiService.upload(`/activities/${activityId}/attachments`, formData);
        return response;
      } catch (error) {
        // 如果已经尝试了最大次数，则抛出错误
        if (attempts >= maxAttempts) {
          throw error;
        }

        // 否则等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }

  /**
   * 下载活动附件
   * @param attachmentId 附件ID
   * @returns 下载结果
   */
  async downloadActivityAttachment(attachmentId: string) {
    await apiService.download(`/activities/attachments/${attachmentId}/download`);
  }

  /**
   * 删除活动附件
   * @param attachmentId 附件ID
   * @returns 删除结果
   */
  async deleteActivityAttachment(attachmentId: string) {
    const response = await apiService.del(`/activities/attachments/${attachmentId}`);
    return response;
  }

  /**
   * 切换活动状态
   * @param id 活动ID
   * @param status 新状态
   * @returns 更新结果
   */
  async toggleActivityStatus(id: string, status: 'published' | 'draft' | 'archived') {
    const response = await apiService.put(`/activities/${id}/status`, { status });
    return response;
  }
}

const activityService = new ActivityService();
export default activityService;
