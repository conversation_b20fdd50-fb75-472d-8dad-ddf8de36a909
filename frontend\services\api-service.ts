/**
 * API服务
 *
 * 提供通用的API请求方法，并实现安全的日志记录
 */

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { toast } from '@/components/ui/use-toast'
import { logger, sanitizeData, sanitizeToken } from '@/utils/logger'

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 3600000, // 增加默认超时时间到60分钟
  withCredentials: true // 允许跨域请求携带凭证
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 只在客户端环境中访问localStorage
    if (typeof window !== 'undefined') {
      // 从localStorage获取token
      const token = localStorage.getItem('hefamily_token')
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`
      }
    }

    return config
  },
  error => {
    // 只记录错误消息，不记录详细错误对象
    logger.error('API请求错误');
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    return response
  },
  error => {
    try {
      // 提取错误详情，但不包含敏感数据
      const errorDetails = {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        statusText: error.response?.statusText
      };

      // 只在开发环境记录详细错误信息
      if (process.env.NODE_ENV !== 'production' &&
          (typeof window === 'undefined' || window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1')) {
        // 根据错误类型使用不同的日志级别

        // 业务逻辑错误（如用户名已存在、密码错误等）使用info级别
        if (error.response?.status === 400 && error.response?.data?.message) {
          logger.info('业务逻辑错误:', error.response.data.message);
        }
        // 权限错误（403）使用info级别
        else if (error.response?.status === 403) {
          logger.info('API请求权限错误:', {
            url: errorDetails.url,
            method: errorDetails.method,
            message: error.response?.data?.message || '没有权限'
          });
        }
        // 认证错误（401）使用info级别
        else if (error.response?.status === 401) {
          logger.info('认证错误:', {
            url: errorDetails.url,
            method: errorDetails.method,
            message: error.response?.data?.message || '认证失败'
          });
        }
        // 用户不存在（404）使用info级别
        else if (error.response?.status === 404) {
          logger.info('用户不存在:', {
            url: errorDetails.url,
            method: errorDetails.method,
            message: error.response?.data?.message || '用户不存在'
          });
        }
        // 登录相关错误使用info级别
        else if (error.config?.url?.includes('/login')) {
          logger.info('登录错误:', {
            url: errorDetails.url,
            method: errorDetails.method,
            status: error.response?.status,
            message: error.response?.data?.message || '登录失败'
          });
        }
        // 其他错误使用error级别
        else {
          // 使用sanitizeData处理错误详情，确保不会记录敏感信息
          logger.error('API请求错误:', sanitizeData(errorDetails));
        }
      } else {
        // 在生产环境不记录任何错误信息
        // 生产环境中logger.error不会输出任何内容，这里只是为了代码完整性
      }

      // 处理错误响应
      const errorMessage = error.response?.data?.message || error.message || '请求失败';
      let shouldShowToast = true; // 是否显示错误提示

      // 401: 未登录或token过期
      if (error.response?.status === 401) {
        // 清除token - 只在客户端环境中访问localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('hefamily_token')
          localStorage.removeItem('hefamily_user_info')
        }

        // 检查当前URL是否包含system-management - 只在客户端环境中访问window.location
        const isSystemManagement = typeof window !== 'undefined' ? window.location.pathname.includes('system-management') : false

        if (isSystemManagement) {
          // 在系统管理页面，只显示错误信息，不弹出登录窗口
          toast({
            title: '请求失败',
            description: '无法获取数据，请检查网络连接或稍后重试',
            variant: 'destructive'
          })
          shouldShowToast = false; // 已经显示了toast，不需要再显示
        } else {
          // 在其他页面，显示登录已过期提示并弹出登录窗口
          toast({
            title: '登录已过期',
            description: '请重新登录',
            variant: 'destructive'
          })
          shouldShowToast = false; // 已经显示了toast，不需要再显示

          // 延迟触发登录弹窗，让用户看到提示 - 只在客户端环境中访问window
          if (typeof window !== 'undefined') {
            setTimeout(() => {
              const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
              window.dispatchEvent(event);
            }, 1500)
          }
        }
      } else if (error.request && !error.response) {
        // 请求已发送但没有收到响应（网络错误）
        // 对所有页面都显示网络错误提示
        toast({
          title: '网络错误',
          description: '无法连接到服务器，请检查网络连接或稍后重试',
          variant: 'destructive'
        });

        shouldShowToast = false; // 已经显示了toast，不需要再显示
      }

      // 显示一般错误提示（如果需要）
      if (shouldShowToast) {
        toast({
          title: '请求失败',
          description: errorMessage,
          variant: 'destructive'
        })
      }
    } catch (toastError) {
      // 如果显示toast时出错，记录到控制台但不中断错误处理流程
      logger.error('显示错误提示失败:', toastError);
    }

    return Promise.reject(error)
  }
)

/**
 * 通用GET请求
 * @param url 请求URL
 * @param params 请求参数
 * @param config 请求配置
 */
export const get = async <T>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> => {
  try {
    // 设置超时处理
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('请求超时，请检查后端服务是否正常运行')), 15000)
    );

    // 使用Promise.race来处理超时
    const fetchPromise = apiClient.get(url, {
      params,
      ...config
    });

    // 尝试从API获取数据，但设置超时
    const response: AxiosResponse = await Promise.race([fetchPromise, timeoutPromise]);

    // 处理不同的响应格式
    if (response.data) {

      // 检查是否包含文件列表和上传者信息
      if (url.includes('/files') || url.includes('/knowledge-base')) {
        if (response.data.data && response.data.data.files) {
          response.data.data.files.forEach((file) => {
            // 如果上传者信息不是对象或为null，但有上传者ID，尝试创建上传者对象
            if ((!file.uploader || typeof file.uploader !== 'object') && file.uploader_id) {
              file.uploader = {
                id: file.uploader_id,
                username: `用户ID: ${file.uploader_id}`,
                email: ''
              };
            }
          });
        } else if (response.data.files) {
          response.data.files.forEach((file) => {
            // 如果上传者信息不是对象或为null，但有上传者ID，尝试创建上传者对象
            if ((!file.uploader || typeof file.uploader !== 'object') && file.uploader_id) {
              file.uploader = {
                id: file.uploader_id,
                username: `用户ID: ${file.uploader_id}`,
                email: ''
              };
            }
          });
        }
      }

      if (response.data.data) {
        return response.data.data as T;
      } else {
        return response.data as T;
      }
    } else {
      return {} as T;
    }
  } catch (error: any) {
    throw error;
  }
}

/**
 * 通用POST请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 请求配置
 */
export const post = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  try {
    // 获取认证token - 只在客户端环境中访问localStorage
    let token = null;
    if (typeof window !== 'undefined') {
      token = localStorage.getItem('hefamily_token');
    }

    // 如果有token且配置中没有设置Authorization头，则添加
    if (token && (!config || !config.headers || !config.headers['Authorization'])) {
      config = config || {};
      config.headers = {
        ...config.headers,
        'Authorization': `Bearer ${token}`
      };
    }

    logger.debug(`开始发送POST请求: ${url}`, {
      data: sanitizeData(data),
      config: sanitizeData(config),
      hasToken: !!token,
      headers: sanitizeData({
        ...apiClient.defaults.headers,
        ...(config?.headers || {})
      }),
      baseURL: apiClient.defaults.baseURL
    });

    // 打印请求数据的类型和内容
    logger.debug("请求数据类型:", typeof data);
    if (process.env.NODE_ENV !== 'production') {
      try {
        logger.debug("请求数据内容:", JSON.stringify(sanitizeData(data)));
      } catch (e) {
        logger.debug("无法序列化请求数据");
      }
    }

    // 特殊处理评论请求
    if (url.includes('/comments') && data && typeof data.topic_id !== 'undefined') {
      // 确保topic_id是数字类型
      if (typeof data.topic_id === 'string') {
        try {
          data.topic_id = parseInt(data.topic_id);
          if (isNaN(data.topic_id)) {
            data.topic_id = 1; // 使用默认ID
          }
        } catch (e) {
          logger.warn("无法将topic_id转换为数字，使用默认值1");
          data.topic_id = 1;
        }
      }
      logger.debug("处理后的评论数据:", sanitizeData(data));
    }

    const response: AxiosResponse = await apiClient.post(url, data, config);

    logger.debug(`POST请求成功: ${url}`, {
      status: response.status,
      statusText: response.statusText,
      headers: sanitizeData(response.headers),
      data: sanitizeData(response.data)
    });

    // 特殊处理登录请求
    if (url.includes('/login')) {
      logger.debug("处理登录响应:", sanitizeData(response.data));

      // 如果响应包含success和data字段，返回整个响应对象
      if (response.data && response.data.success && response.data.data) {
        logger.debug("登录响应包含success和data字段，返回整个响应对象");
        return response.data as T;
      }
    }

    // 特殊处理注册请求
    if (url.includes('/register')) {
      logger.debug("处理注册响应:", sanitizeData(response.data));

      // 如果响应包含success和data字段，返回整个响应对象
      if (response.data && response.data.success && response.data.data) {
        logger.debug("注册响应包含success和data字段，返回整个响应对象");
        return response.data as T;
      }
    }

    // 处理不同的响应格式
    if (response.data && response.data.data) {
      logger.debug("响应包含data字段，返回data");
      return response.data.data as T;
    } else if (url.includes('/login') || url.includes('/register')) {
      // 登录和注册接口返回的数据格式不同
      logger.debug("登录或注册请求，返回整个响应数据");
      return response.data as T;
    } else {
      logger.debug("其他请求，返回整个响应数据");
      return response.data as T;
    }
  } catch (error: any) {
    // 根据错误类型和URL使用不同的日志级别
    if (url.includes('/login') || url.includes('/register')) {
      // 登录和注册相关错误使用info级别
      logger.info(`${url.includes('/login') ? '登录' : '注册'}请求处理:`, {
        message: error.message,
        status: error.response?.status
      });
    } else {
      // 其他错误使用error级别
      logger.error(`POST请求失败: ${url}`, {
        message: error.message,
        response: sanitizeData(error.response?.data),
        status: error.response?.status
      });
    }
    throw error;
  }
}

/**
 * 通用PUT请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 请求配置
 */
export const put = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  try {
    logger.debug(`开始发送PUT请求: ${url}`, { data: sanitizeData(data), config: sanitizeData(config) });

    const response: AxiosResponse = await apiClient.put(url, data, config);

    logger.debug(`PUT请求成功: ${url}`, {
      status: response.status,
      statusText: response.statusText,
      data: sanitizeData(response.data)
    });

    // 处理不同的响应格式
    if (response.data && response.data.data) {
      logger.debug(`PUT请求返回data.data: ${url}`);
      return response.data.data as T;
    } else {
      logger.debug(`PUT请求返回data: ${url}`);
      return response.data as T;
    }
  } catch (error: any) {
    logger.error(`PUT请求失败: ${url}`, {
      message: error.message,
      response: sanitizeData(error.response?.data),
      status: error.response?.status
    });
    throw error;
  }
}

/**
 * 通用DELETE请求
 * @param url 请求URL
 * @param config 请求配置
 */
export const del = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    logger.debug(`开始发送DELETE请求: ${url}`, { config: sanitizeData(config) });

    const response: AxiosResponse = await apiClient.delete(url, config);

    logger.debug(`DELETE请求成功: ${url}`, {
      status: response.status,
      statusText: response.statusText,
      data: sanitizeData(response.data)
    });

    // 处理不同的响应格式
    if (response.data && response.data.data) {
      logger.debug(`DELETE请求返回data.data: ${url}`);
      return response.data.data as T;
    } else {
      logger.debug(`DELETE请求返回data: ${url}`);
      return response.data as T;
    }
  } catch (error: any) {
    logger.error(`DELETE请求失败: ${url}`, {
      message: error.message,
      response: sanitizeData(error.response?.data),
      status: error.response?.status
    });

    // 如果有响应数据，将错误信息附加到错误对象上
    if (error.response && error.response.data) {
      error.message = error.response.data.message || error.message;
    }

    throw error;
  }
}

/**
 * 文件上传请求
 * @param url 请求URL
 * @param formData 表单数据
 * @param onUploadProgress 上传进度回调
 */
export const upload = async <T>(
  url: string,
  formData: FormData,
  onUploadProgress?: (progressEvent: any) => void
): Promise<T> => {
  try {
    // 记录上传信息
    const fileInfo = Array.from(formData.entries()).map(([key, value]) => {
      if (value instanceof File) {
        // 确保文件名正确编码
        const fileName = value.name;
        return {
          key,
          fileName,
          fileSize: value.size,
          fileType: value.type
        };
      }
      return { key, value };
    });

    logger.debug(`开始上传文件: ${url}`, { formDataEntries: fileInfo });

    // 检查文件大小
    const files = fileInfo.filter(item => item.fileSize !== undefined);
    if (files.length > 0) {
      const totalSize = files.reduce((sum, file) => sum + (file.fileSize || 0), 0);
      logger.debug(`总文件大小: ${(totalSize / (1024 * 1024)).toFixed(2)}MB`);

      // 如果文件太大，给出警告但仍允许上传
      if (totalSize > 30 * 1024 * 1024) { // 30MB
        logger.warn('文件过大，可能导致上传超时');
      }
    }

    // 为文件上传设置更长的超时时间
    const response: AxiosResponse<{ data: T }> = await apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 3600000, // 上传文件的超时时间设置为1小时
      onUploadProgress: (progressEvent) => {
        // 计算上传进度百分比
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        logger.debug(`文件上传进度: ${percentCompleted}%`);

        // 如果提供了进度回调，则调用它
        if (onUploadProgress) {
          onUploadProgress(progressEvent);
        }
      }
    });

    logger.debug(`文件上传成功: ${url}`, {
      status: response.status,
      data: sanitizeData(response.data)
    });

    // 检查响应中是否包含有效的数据
    if (!response.data || !response.data.data) {
      logger.warn('上传响应中没有有效数据:', sanitizeData(response.data));
      throw new Error('服务器返回的数据格式不正确');
    }

    return response.data.data;
  } catch (error: any) {
    // 权限错误（403）不需要在控制台显示为错误
    if (error.response?.status === 403) {
      logger.info(`文件上传权限错误: ${url}`, {
        message: error.response.data?.message || '没有权限上传文件'
      });

      // 确保错误对象包含正确的消息
      if (error.response.data?.message) {
        error.message = error.response.data.message;
      } else {
        error.message = '没有权限上传文件';
      }
    } else {
      // 其他错误正常记录
      logger.error(`文件上传失败: ${url}`, {
        message: error.message,
        code: error.code,
        response: sanitizeData(error.response?.data)
      });

      // 提供更详细的错误信息
      if (error.message === 'Network Error') {
        logger.error('网络错误，无法连接到后端服务器');
      } else if (error.message.includes('timeout')) {
        logger.error('上传超时，文件可能太大或服务器处理时间过长');
      } else if (error.response) {
        if (error.response.status === 413) {
          logger.error('文件太大，超出服务器允许的最大大小');
        } else if (error.response.status === 415) {
          logger.error('不支持的文件类型');
        }
      }
    }

    throw error;
  }
}

/**
 * FormData POST请求
 * @param url 请求URL
 * @param formData 表单数据
 * @param config 请求配置
 */
export const postFormData = async <T>(
  url: string,
  formData: FormData,
  config?: AxiosRequestConfig
): Promise<T> => {
  try {
    const response: AxiosResponse<{ data: T }> = await apiClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
    return response.data.data
  } catch (error: any) {
    // 处理403权限错误，添加更友好的错误消息
    if (error.response?.status === 403) {
      // 记录权限错误但不作为严重错误
      logger.info(`API权限错误: ${url}`, {
        message: error.response.data?.message || '权限不足'
      })

      // 确保错误对象包含正确的消息
      if (error.response.data?.message) {
        error.message = error.response.data.message
      } else {
        error.message = '您没有权限执行此操作'
      }
    }

    throw error
  }
}

/**
 * FormData PUT请求
 * @param url 请求URL
 * @param formData 表单数据
 * @param config 请求配置
 */
export const putFormData = async <T>(
  url: string,
  formData: FormData,
  config?: AxiosRequestConfig
): Promise<T> => {
  try {
    const response: AxiosResponse<{ data: T }> = await apiClient.put(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
    return response.data.data
  } catch (error) {
    throw error
  }
}

/**
 * 文件下载请求
 * @param url 请求URL
 * @param filename 文件名
 */
export const download = async (url: string, filename?: string): Promise<void> => {
  try {
    const response = await apiClient.get(url, {
      responseType: 'blob'
    })

    // 创建下载链接 - 只在客户端环境中执行
    if (typeof window !== 'undefined') {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl

      // 设置文件名
      if (filename) {
        link.download = filename
      } else {
        // 尝试从响应头获取文件名
        const contentDisposition = response.headers['content-disposition']
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="?([^"]*)"?/)
          if (filenameMatch && filenameMatch[1]) {
            link.download = filenameMatch[1]
          }
        }
      }

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }
  } catch (error) {
    throw error
  }
}

/**
 * 获取二进制数据
 * @param url 请求URL
 * @param config 请求配置
 */
export const getBlob = async (url: string, config?: AxiosRequestConfig): Promise<Blob> => {
  try {
    const response = await apiClient.get(url, {
      ...config,
      responseType: 'blob'
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 获取API基础URL
 */
export const getBaseUrl = (): string => {
  return apiClient.defaults.baseURL || '';
}

export default {
  get,
  post,
  put,
  del,
  upload,
  download,
  postFormData,
  putFormData,
  getBlob,
  getBaseUrl
}
