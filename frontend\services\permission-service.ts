/**
 * 权限服务
 *
 * 提供与权限相关的API调用和权限检查功能，并实现安全的日志记录
 */

import apiService from './api-service';
import { logger, sanitizeData } from '@/utils/logger';
import { mapToBackendPermission, mapPermissionsToFrontend } from '@/utils/permission-mapping';

// 权限类型
export interface Permission {
  id: number;
  code: string;
  name: string;
  module: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// 权限服务
const permissionService = {
  /**
   * 获取所有权限
   * @returns 权限列表
   */
  async getAllPermissions(): Promise<Permission[]> {
    try {
      const response = await apiService.get<Permission[]>('/permissions');
      return response;
    } catch (error) {
      logger.error('获取所有权限失败:', error);
      throw error;
    }
  },

  /**
   * 获取当前用户的权限
   * @returns 权限列表
   */
  async getCurrentUserPermissions(): Promise<Permission[]> {
    try {
      // 检查token是否存在
      const token = localStorage.getItem('hefamily_token');
      if (!token) {
        logger.warn('获取当前用户权限失败: 未登录');
        return this.getDefaultPermissions();
      }

      try {
        // 尝试从API获取权限
        logger.debug('尝试从API获取用户权限');
        const response = await apiService.get<{success: boolean, data: Permission[]}>('/users/me/permissions');

        // 详细记录API响应
        logger.debug('权限API响应:', sanitizeData(response));

        // 直接使用响应数据，不再检查格式
        logger.debug('处理权限API响应');

        // 如果响应是数组，直接使用
        if (Array.isArray(response)) {
          logger.debug('响应是数组，直接使用');
          const permissionCodes = response.map(p => p.code);
          logger.debug('权限代码列表:', permissionCodes);
          this.saveUserPermissionsToStorage(permissionCodes);
          return response;
        }

        // 如果响应有data字段且是数组，使用data
        if (response && Array.isArray(response.data)) {
          logger.debug('响应有data字段且是数组，使用data');
          const permissionCodes = response.data.map(p => p.code);
          logger.debug('权限代码列表:', permissionCodes);
          this.saveUserPermissionsToStorage(permissionCodes);
          return response.data;
        }

        // 如果响应有data.data字段且是数组，使用data.data
        if (response && response.data && Array.isArray(response.data.data)) {
          logger.debug('响应有data.data字段且是数组，使用data.data');
          const permissionCodes = response.data.data.map(p => p.code);
          logger.debug('权限代码列表:', permissionCodes);
          this.saveUserPermissionsToStorage(permissionCodes);
          return response.data.data;
        }

        // 如果响应有success字段且为true，尝试解析
        if (response && response.success === true) {
          logger.debug('响应有success字段且为true，尝试解析');

          // 尝试从data字段获取权限
          const data = response.data;
          if (Array.isArray(data)) {
            logger.debug('data字段是数组，使用data');
            const permissionCodes = data.map(p => p.code);
            logger.debug('权限代码列表:', permissionCodes);
            this.saveUserPermissionsToStorage(permissionCodes);
            return data;
          }
        }

        logger.warn('无法解析API返回的权限格式:', sanitizeData(response));
        return this.getDefaultPermissions();
      } catch (error: any) {
        // 处理各种错误情况
        logger.warn('获取用户权限时出错:', sanitizeData(error));

        // 如果API返回401、404或500，使用默认权限
        if (error.response && (error.response.status === 401 ||
                              error.response.status === 404 ||
                              error.response.status === 500)) {
          logger.warn(`权限API返回${error.response.status}错误，使用默认权限`);
          return this.getDefaultPermissions();
        }

        // 如果是网络错误，使用默认权限
        if (error.message === 'Network Error') {
          logger.warn('网络错误，使用默认权限');
          return this.getDefaultPermissions();
        }

        // 其他错误，使用默认权限
        logger.error('未知错误，使用默认权限:', sanitizeData(error));
        return this.getDefaultPermissions();
      }
    } catch (error) {
      logger.error('获取当前用户权限过程中发生异常:', sanitizeData(error));
      return this.getDefaultPermissions();
    }
  },

  /**
   * 获取默认权限 - 不再提供任何默认权限，只返回空列表
   * @returns 空权限列表
   */
  getDefaultPermissions(): Permission[] {
    logger.debug('使用空权限列表（不提供默认权限）');

    // 返回空权限列表
    const emptyPermissions: Permission[] = [];

    // 保存空权限列表到本地存储
    this.saveUserPermissionsToStorage([]);

    return emptyPermissions;
  },

  /**
   * 检查当前用户是否有指定权限
   * @param permissionCode 权限代码
   * @returns 是否有权限
   */
  async hasPermission(permissionCode: string): Promise<boolean> {
    try {
      // 检查token是否存在
      const token = localStorage.getItem('hefamily_token');
      if (!token) {
        logger.warn('检查权限失败: 未登录');
        return false;
      }

      // 首先检查本地存储中的权限
      const hasPermissionInStorage = this.hasPermissionInStorage(permissionCode);
      if (hasPermissionInStorage) {
        logger.debug(`用户拥有权限(本地存储): ${permissionCode}`);
        return true;
      }

      try {
        // 尝试从API检查权限
        logger.debug(`尝试从API检查权限: ${permissionCode}`);
        const response = await apiService.get<{success: boolean, data: {has_permission: boolean}}>('/users/me/check-permission', {
          code: permissionCode
        });

        // 检查响应是否有效
        if (response && response.success && response.data && typeof response.data.has_permission === 'boolean') {
          logger.debug(`API权限检查结果: ${response.data.has_permission ? '有权限' : '无权限'}`);
          return response.data.has_permission;
        } else {
          logger.warn('API返回的权限检查结果格式无效:', sanitizeData(response));
          return this.hasPermissionInStorage(permissionCode);
        }
      } catch (error: any) {
        // 处理各种错误情况
        logger.warn('检查权限时出错:', sanitizeData(error));

        // 如果API返回401、404或500，使用本地存储中的权限
        if (error.response && (error.response.status === 401 ||
                              error.response.status === 404 ||
                              error.response.status === 500)) {
          logger.warn(`权限检查API返回${error.response.status}错误，使用本地存储中的权限`);
          return this.hasPermissionInStorage(permissionCode);
        }

        // 如果是网络错误，使用本地存储中的权限
        if (error.message === 'Network Error') {
          logger.warn('网络错误，使用本地存储中的权限');
          return this.hasPermissionInStorage(permissionCode);
        }

        // 其他错误，使用本地存储中的权限
        logger.error('未知错误，使用本地存储中的权限:', sanitizeData(error));
        return this.hasPermissionInStorage(permissionCode);
      }
    } catch (error) {
      logger.error('检查权限过程中发生异常:', sanitizeData(error));
      return this.hasPermissionInStorage(permissionCode);
    }
  },

  /**
   * 从本地存储中获取用户权限
   * @returns 权限代码列表
   */
  getUserPermissionsFromStorage(): string[] {
    try {
      const permissionsStr = localStorage.getItem('hefamily_user_permissions');
      return permissionsStr ? JSON.parse(permissionsStr) : [];
    } catch (error) {
      logger.error('从本地存储获取权限失败:', error);
      return [];
    }
  },

  /**
   * 保存用户权限到本地存储
   * @param permissions 权限代码列表
   */
  saveUserPermissionsToStorage(permissions: string[]): void {
    try {
      localStorage.setItem('hefamily_user_permissions', JSON.stringify(permissions));
    } catch (error) {
      logger.error('保存权限到本地存储失败:', error);
    }
  },

  /**
   * 检查本地存储中是否有指定权限
   * @param permissionCode 权限代码
   * @returns 是否有权限
   */
  hasPermissionInStorage(permissionCode: string): boolean {
    // 检查用户是否是管理员
    const userDataStr = localStorage.getItem('hefamily_user_data');
    if (userDataStr) {
      try {
        const userData = JSON.parse(userDataStr);

        if (userData && userData.role === 'admin') {
          // 管理员拥有所有权限
          return true;
        }
      } catch (error) {
        // 解析失败，继续检查具体权限
      }
    }

    // 非管理员检查具体权限
    const permissions = this.getUserPermissionsFromStorage();

    // 将前端权限代码映射到后端权限代码
    const backendPermissionCode = mapToBackendPermission(permissionCode);

    // 正常检查权限
    const hasPermission = permissions.includes(backendPermissionCode) || permissions.includes('*');
    return hasPermission;
  },

  /**
   * 清除本地存储中的权限
   */
  clearPermissionsFromStorage(): void {
    localStorage.removeItem('hefamily_user_permissions');
  }
};

export default permissionService;
