/**
 * 上传服务
 *
 * 处理文件上传相关的功能
 */

import * as apiService from './api-service'

/**
 * 上传图片
 * @param file 图片文件
 * @param type 图片类型（avatar: 头像, cover: 封面, icon: 图标, other: 其他）
 * @returns 上传结果，包含图片URL
 */
export const uploadImage = async (file: File, type: 'avatar' | 'cover' | 'icon' | 'other' = 'other'): Promise<{ url: string }> => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)

  const result = await apiService.upload<{ url: string }>('/upload/image', formData)
  // api-service.upload已经返回了response.data.data，所以result就是{ url: string }
  return result
}

/**
 * 上传文件
 * @param file 文件
 * @param knowledgeBaseId 知识库ID（如果是上传到知识库）
 * @returns 上传结果，包含文件URL和ID
 */
export const uploadFile = async (file: File, knowledgeBaseId?: string): Promise<{ url: string, id: string }> => {
  const formData = new FormData()
  formData.append('file', file)

  if (knowledgeBaseId) {
    formData.append('knowledgeBaseId', knowledgeBaseId)
  }

  return await apiService.upload<{ url: string, id: string }>('/upload/file', formData)
}

/**
 * 删除上传的文件
 * @param fileId 文件ID
 * @returns 操作结果
 */
export const deleteFile = async (fileId: string): Promise<{ message: string }> => {
  return await apiService.del<{ message: string }>(`/upload/file/${fileId}`)
}

export default {
  uploadImage,
  uploadFile,
  deleteFile
}
