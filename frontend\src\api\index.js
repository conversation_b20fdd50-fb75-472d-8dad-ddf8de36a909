/**
 * API服务入口
 *
 * 导出所有API服务，方便统一管理和使用
 */

import axios from 'axios';
import { getToken, removeToken } from '../utils/auth';

// 创建axios实例
const service = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || '/api',
  timeout: 15000 // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在请求发送前做一些处理
    const token = getToken();
    if (token) {
      // 让每个请求携带token
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // 添加缓存控制头，确保获取最新数据
    config.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
    config.headers['Pragma'] = 'no-cache';
    config.headers['Expires'] = '0';

    return config;
  },
  error => {
    // 处理请求错误
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;

    // 如果响应成功但业务状态码不是成功
    if (res.code && res.code !== 200) {
      console.error('API错误:', res.message || '请求失败');

      // 401: 未登录或token过期
      if (res.code === 401) {
        // 清除token
        removeToken();

        // 触发登录弹窗
        const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
        window.dispatchEvent(event);
      }

      return Promise.reject(new Error(res.message || '请求失败'));
    }

    return res;
  },
  error => {
    // 处理响应错误
    console.error('响应错误:', error);

    // 获取错误信息
    const errorMessage = error.response?.data?.message || error.message || '请求失败';

    // 401: 未登录或token过期
    if (error.response?.status === 401) {
      // 清除token
      removeToken();

      // 显示错误提示
      console.error('登录已过期，请重新登录');

      // 触发登录弹窗
      const event = new CustomEvent('open-login-modal', { detail: { isRegister: false } });
      window.dispatchEvent(event);
    } else if (error.response?.status === 404) {
      // 404错误不在这里处理，让业务逻辑自己处理
      console.warn('资源未找到:', errorMessage);
    } else {
      console.error('API错误:', errorMessage);
    }

    return Promise.reject(error);
  }
);

// 导出API服务
export { default as userApi } from './modules/user';
export { default as knowledgeApi } from './modules/knowledge';
export { default as fileApi } from './modules/file';
export { default as aiApi } from './modules/ai';
export { default as systemApi } from './modules/system';
export { default as activityApi } from './modules/activity';
export { default as commentApi } from './modules/comment';
export { default as notificationApi } from './modules/notification';
export { default as messageApi } from './modules/message';

// 导出axios实例
export default service;
