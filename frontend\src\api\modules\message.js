/**
 * 消息相关API
 *
 * 处理消息的创建、查询、更新、删除等请求
 */

import request from '../index';

/**
 * 获取消息列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.type - 消息类型 (content/link)
 * @param {string} params.status - 消息状态 (draft/published/archived)
 * @param {string} params.search - 搜索关键词
 * @param {string} params.author - 作者筛选
 * @returns {Promise} 消息列表
 */
export function getMessageList(params) {
  return request({
    url: '/messages',
    method: 'get',
    params
  });
}

/**
 * 获取消息详情
 * @param {string} id - 消息ID
 * @returns {Promise} 消息详情
 */
export function getMessageById(id) {
  return request({
    url: `/messages/${id}`,
    method: 'get'
  });
}

/**
 * 创建消息
 * @param {Object} data - 消息数据
 * @param {string} data.title - 标题
 * @param {string} data.content - 内容（富文本）
 * @param {string} data.author - 作者
 * @param {string} data.type - 类型 (content/link)
 * @param {string} data.external_url - 外部链接（type=link时必填）
 * @param {string} data.cover_image - 封面图片URL
 * @param {string} data.status - 状态 (draft/published)
 * @returns {Promise} 创建结果
 */
export function createMessage(data) {
  console.log('前端发送消息创建请求:', JSON.stringify(data, null, 2));
  return request({
    url: '/messages',
    method: 'post',
    data
  });
}

/**
 * 更新消息
 * @param {string} id - 消息ID
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export function updateMessage(id, data) {
  return request({
    url: `/messages/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除消息
 * @param {string} id - 消息ID
 * @returns {Promise} 删除结果
 */
export function deleteMessage(id) {
  return request({
    url: `/messages/${id}`,
    method: 'delete'
  });
}

/**
 * 发布消息
 * @param {string} id - 消息ID
 * @returns {Promise} 发布结果
 */
export function publishMessage(id) {
  return request({
    url: `/messages/${id}/publish`,
    method: 'patch'
  });
}

/**
 * 归档消息
 * @param {string} id - 消息ID
 * @returns {Promise} 归档结果
 */
export function archiveMessage(id) {
  return request({
    url: `/messages/${id}/archive`,
    method: 'patch'
  });
}

/**
 * 下架消息（将已发布改为草稿）
 * @param {string} id - 消息ID
 * @returns {Promise} 下架结果
 */
export function unpublishMessage(id) {
  return request({
    url: `/messages/${id}/unpublish`,
    method: 'patch'
  });
}

/**
 * 从URL提取图片
 * @param {string} url - 网页URL
 * @returns {Promise} 图片URL
 */
export function extractImageFromUrl(url) {
  return request({
    url: '/messages/extract-image',
    method: 'post',
    data: { url }
  });
}

// 导出默认对象
export default {
  getMessageList,
  getMessageById,
  createMessage,
  updateMessage,
  deleteMessage,
  publishMessage,
  archiveMessage,
  unpublishMessage,
  extractImageFromUrl
};
