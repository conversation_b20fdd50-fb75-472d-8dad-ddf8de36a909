/**
 * 图片工具函数
 *
 * 提供统一的图片URL构建和处理功能
 */

/**
 * 构建图片URL的辅助函数
 * @param imagePath 图片路径
 * @returns 完整的图片URL
 */
export const buildImageUrl = (imagePath: string | null | undefined): string => {
  if (!imagePath) {
    return "/placeholder.svg"
  }

  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http')) {
    return imagePath
  }

  // 确保路径以/开头
  const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`

  // 在生产环境中，直接使用相对路径，让nginx处理
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    return normalizedPath
  }

  // 开发环境使用后端服务器
  const baseUrl = 'http://localhost:5001'
  return `${baseUrl}${normalizedPath}`
}

/**
 * 获取图片的显示URL
 * 专门用于处理不同模块的图片路径格式
 * @param imagePath 图片路径
 * @param fallback 备用图片路径
 * @returns 完整的图片URL
 */
export const getImageDisplayUrl = (imagePath: string | null | undefined, fallback: string = "/placeholder.svg"): string => {
  if (!imagePath) {
    return fallback
  }

  return buildImageUrl(imagePath)
}

/**
 * 检查图片路径是否有效
 * @param imagePath 图片路径
 * @returns 是否有效
 */
export const isValidImagePath = (imagePath: string | null | undefined): boolean => {
  return !!(imagePath && imagePath.trim().length > 0)
}
