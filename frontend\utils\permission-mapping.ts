/**
 * 权限映射工具
 *
 * 将前端使用的权限代码映射到后端实际存在的权限代码
 */

import { logger } from './logger';

// 前端权限代码到后端权限代码的映射表
const frontendToBackendMapping: Record<string, string> = {
  // 基础页面权限
  'home:access': 'home:access',
  'family:access': 'family:access',

  // 系统管理权限
  'system:access': 'system:access',
  'user:manage': 'user:manage',
  'role:manage': 'role:manage',
  'ai:manage': 'ai:manage',

  // 知识库权限
  'knowledge:access': 'knowledge:access',
  'knowledge:manage': 'knowledge:manage',
  'knowledge:create_user': 'knowledge:create_user',
  'knowledge:create_system': 'knowledge:create_system',
  'file:upload': 'file:upload',
  'file:download': 'file:download',
  'file:manage': 'file:manage',
  'file:review': 'file:review',

  // 个人专题权限
  'personal:access': 'personal:access',
  'personal:ai_use': 'personal:ai_use',
  'personal:manage_materials': 'personal:manage_materials',

  // 数据查询权限
  'data:access': 'data:access',
  'data:ai_query': 'data:ai_query',

  // AI研究助手权限
  'assistant:use': 'assistant:use',

  // 内容管理权限
  'content:view': 'content:view',
  'content:publish': 'content:publish',
  'content:manage': 'content:manage',

  // 其他权限
  'comment:manage': 'comment:manage',
  'notification:manage': 'notification:manage',
  'timeline:manage': 'timeline:manage',
  'activity:manage': 'activity:manage',
  'activity:view': 'activity:view',
  'security:manage': 'security:manage',
};

// 后端权限代码到前端权限代码的映射表
const backendToFrontendMapping: Record<string, string[]> = {
  // 基础页面权限
  'activity:view': ['home:access'],
  'exhibition:manage': ['family:access', 'personal:access'],
  'home:access': ['home:access'],
  'family:access': ['family:access'],

  // 系统管理权限
  'system:access': ['system:access'],
  'system:config': ['system:access'],
  'user:manage': ['user:manage'],
  'role:manage': ['role:manage'],
  'ai:manage': ['ai:manage', 'personal:ai_use', 'data:ai_query', 'assistant:use'],

  // 知识库权限
  'knowledge:access': ['knowledge:access', 'data:access'],
  'knowledge:manage': ['knowledge:manage'],
  'knowledge:create_user': ['knowledge:create_user'],
  'file:upload': ['file:upload'],
  'file:download': ['file:download'],
  'file:manage': ['file:manage'],
  'file:review': ['file:review'],

  // 个人专题权限
  'personal:access': ['personal:access'],
  'personal:ai_use': ['personal:ai_use'],
  'personal:manage_materials': ['personal:manage_materials'],

  // 数据查询权限
  'data:access': ['data:access'],
  'data:ai_query': ['data:ai_query'],

  // AI研究助手权限
  'assistant:use': ['assistant:use'],

  // 内容管理权限
  'content:view': ['content:view'],
  'content:publish': ['content:publish'],
  'content:manage': ['content:manage'],

  // 其他权限
  'comment:manage': ['comment:manage'],
  'notification:manage': ['notification:manage'],
  'timeline:manage': ['timeline:manage'],
  'activity:manage': ['activity:manage'],
  'security:manage': ['security:manage'],
};

// 创建一个完整的前端权限代码列表，用于检查权限代码是否有效
const allFrontendPermissionCodes = [
  // 基础页面权限
  'home:access',
  'family:access',

  // 系统管理权限
  'system:access',
  'user:manage',
  'role:manage',
  'ai:manage',

  // 知识库权限
  'knowledge:access',
  'knowledge:manage',
  'knowledge:create_user',
  'knowledge:create_system',
  'file:upload',
  'file:download',
  'file:manage',
  'file:review',

  // 个人专题权限
  'personal:access',
  'personal:ai_use',
  'personal:manage_materials',

  // 数据查询权限
  'data:access',
  'data:ai_query',

  // AI研究助手权限
  'assistant:use',

  // 消息内容权限
  'content:publish',
  'content:manage',
  'content:view',

  // 其他权限
  'comment:manage',
  'notification:manage',
  'timeline:manage',
  'activity:manage',
  'activity:view',
  'security:manage'
];

// 创建一个完整的后端权限代码列表，用于检查权限代码是否有效
const allBackendPermissionCodes = [
  // 基础页面权限
  'home:access',
  'family:access',
  'activity:view',
  'activity:manage',
  'exhibition:manage',

  // 系统管理权限
  'system:access',
  'system:config',
  'user:manage',
  'role:manage',
  'ai:manage',
  'security:manage',

  // 知识库权限
  'knowledge:access',
  'knowledge:manage',
  'knowledge:create_user',
  'knowledge:create_system',
  'file:upload',
  'file:download',
  'file:manage',
  'file:review',

  // 个人专题权限
  'personal:access',
  'personal:ai_use',
  'personal:manage_materials',

  // 数据查询权限
  'data:access',
  'data:ai_query',

  // AI研究助手权限
  'assistant:use',

  // 消息内容权限
  'content:publish',
  'content:manage',
  'content:view',

  // 其他权限
  'comment:manage',
  'notification:manage',
  'timeline:manage'
];

/**
 * 将前端权限代码映射到后端权限代码
 *
 * @param frontendPermission 前端权限代码
 * @returns 后端权限代码
 */
export function mapToBackendPermission(frontendPermission: string): string {
  // 如果输入的是后端权限代码，直接返回
  if (allBackendPermissionCodes.includes(frontendPermission)) {
    logger.debug(`输入的是后端权限代码，直接返回: ${frontendPermission}`);
    return frontendPermission;
  }

  // 直接使用映射表中的映射关系，不再需要特殊处理
  // 所有的映射关系都已经在frontendToBackendMapping中定义

  const backendPermission = frontendToBackendMapping[frontendPermission];

  if (backendPermission) {
    logger.debug(`权限映射: ${frontendPermission} -> ${backendPermission}`);
    return backendPermission;
  }

  // 如果没有映射，则返回原始权限代码
  logger.debug(`未找到权限映射，使用原始权限代码: ${frontendPermission}`);
  return frontendPermission;
}

/**
 * 将后端权限代码映射到前端权限代码
 *
 * @param backendPermission 后端权限代码
 * @returns 前端权限代码数组（一个后端权限可能对应多个前端权限）
 */
export function mapToFrontendPermissions(backendPermission: string): string[] {
  // 如果输入的是前端权限代码，检查它是否有效
  if (allFrontendPermissionCodes.includes(backendPermission)) {
    logger.debug(`输入的是前端权限代码，直接返回: ${backendPermission}`);
    return [backendPermission];
  }

  // 直接使用映射表中的映射关系，不再需要特殊处理
  // 所有的映射关系都已经在backendToFrontendMapping中定义

  // 根据后端权限代码获取对应的前端权限代码
  const frontendPermissions = backendToFrontendMapping[backendPermission];

  if (frontendPermissions && frontendPermissions.length > 0) {
    logger.debug(`后端权限映射到前端: ${backendPermission} -> ${frontendPermissions.join(', ')}`);
    return frontendPermissions;
  }

  // 如果没有映射，则返回原始权限代码
  logger.debug(`未找到后端权限映射，使用原始权限代码: ${backendPermission}`);
  return [backendPermission];
}

/**
 * 将前端权限代码列表映射到后端权限代码列表
 *
 * @param frontendPermissions 前端权限代码列表
 * @returns 后端权限代码列表（去重）
 */
export function mapPermissionsToBackend(frontendPermissions: string[]): string[] {
  // 如果输入为空，返回空数组
  if (!frontendPermissions || frontendPermissions.length === 0) {
    return [];
  }

  // 过滤掉重复的前端权限代码
  const uniqueFrontendPermissions = [...new Set(frontendPermissions)];

  // 构建后端权限代码列表
  const backendPermissions = [];

  // 直接映射所有权限
  uniqueFrontendPermissions.forEach(code => {
    const backendCode = mapToBackendPermission(code);
    if (backendCode && !backendPermissions.includes(backendCode)) {
      backendPermissions.push(backendCode);
    }
  });

  logger.debug(`批量映射前端权限到后端: [${uniqueFrontendPermissions.join(', ')}] -> [${backendPermissions.join(', ')}]`);

  return backendPermissions;
}

/**
 * 将后端权限代码列表映射到前端权限代码列表
 *
 * @param backendPermissions 后端权限代码列表
 * @returns 前端权限代码列表（去重）
 */
export function mapPermissionsToFrontend(backendPermissions: string[]): string[] {
  // 如果输入为空，返回空数组
  if (!backendPermissions || backendPermissions.length === 0) {
    return [];
  }

  // 过滤掉重复的后端权限代码
  const uniqueBackendPermissions = [...new Set(backendPermissions)];

  // 将每个后端权限映射到前端权限，然后展平数组
  const frontendPermissions = uniqueBackendPermissions.flatMap(backendPermission => {
    // 对所有权限，使用标准映射函数
    return mapToFrontendPermissions(backendPermission);
  });

  logger.debug(`批量映射后端权限到前端: [${uniqueBackendPermissions.join(', ')}] -> [${frontendPermissions.join(', ')}]`);

  // 去重
  return [...new Set(frontendPermissions)];
}
