@echo off
echo 开始提交代码到Git...

echo 添加新文件...
git add backend\src\scripts\cleanup-test-roles.js
git add backend\src\scripts\fix-admin-roles.js
git add backend\src\middlewares\improvedPermissionMiddleware.js
git add backend\fix-roles.bat
git add backend\docs\role-fix-guide.md
git add backend\fix-roles.sql
git add backend\docs\manual-fix-guide.md

echo 提交代码...
git commit -m "修复角色管理问题：清理测试角色，修复管理员角色，改进权限检查机制"

echo 推送到远程仓库...
git push

echo 完成！
pause
