# Nginx配置文件 - 优化流式传输
# 用于生产环境，解决流式输出断断续续的问题

server {
    listen 80;
    server_name _;  # 接受所有域名

    # 禁用缓冲，确保流式数据立即传输
    proxy_buffering off;
    proxy_cache off;
    proxy_request_buffering off;
    
    # 设置较小的缓冲区大小
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    
    # 禁用gzip压缩（对于流式数据）
    gzip off;
    
    # 前端静态文件
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 后端API - 特别优化流式端点
    location /api/ai/data-query/stream {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        
        # 禁用所有缓冲
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;
        
        # 设置超时
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;  # 5分钟，适应AI响应时间
        
        # 设置头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection '';
        
        # 禁用Nginx缓冲（重要！）
        proxy_set_header X-Accel-Buffering no;
        
        # 立即刷新
        proxy_flush_interval 1s;
    }
    
    # 其他流式端点
    location ~ ^/api/ai/assistant/.*/stream$ {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        
        # 禁用所有缓冲
        proxy_buffering off;
        proxy_cache off;
        proxy_request_buffering off;
        
        # 设置超时
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
        
        # 设置头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection '';
        
        # 禁用Nginx缓冲
        proxy_set_header X-Accel-Buffering no;
        
        # 立即刷新
        proxy_flush_interval 1s;
    }
    
    # 普通API端点
    location /api/ {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 普通API可以使用缓冲
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
}

# 全局配置优化
http {
    # 禁用服务器令牌
    server_tokens off;
    
    # 设置较小的发送超时
    send_timeout 60s;
    
    # 客户端配置
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 保持连接
    keepalive_timeout 65s;
    keepalive_requests 100;
    
    # 日志格式
    log_format stream_log '$remote_addr - $remote_user [$time_local] '
                         '"$request" $status $body_bytes_sent '
                         '"$http_referer" "$http_user_agent" '
                         'rt=$request_time uct="$upstream_connect_time" '
                         'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    # 访问日志
    access_log /var/log/nginx/access.log stream_log;
    error_log /var/log/nginx/error.log warn;
}
