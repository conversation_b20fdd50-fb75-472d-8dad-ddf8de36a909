/**
 * 测试API功能
 */

const axios = require('axios');

// 测试配置
const API_BASE = 'http://localhost:5001/api';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInJvbGUiOiJhZG1pbiIsImlhdCI6MTczNzU0NzE5NCwiZXhwIjoxNzM3NjMzNTk0fQ.Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8Qs8'; // 请替换为有效的token

async function testMessageAPIs() {
  console.log('开始测试消息API...\n');

  try {
    // 1. 测试获取消息列表
    console.log('1. 测试获取消息列表');
    const listResponse = await axios.get(`${API_BASE}/messages`);
    console.log('✓ 获取消息列表成功，状态码:', listResponse.status);
    const messages = listResponse.data.data.messages;
    console.log('消息数量:', messages.length);
    
    if (messages.length > 0) {
      const testMessage = messages[0];
      console.log('测试消息ID:', testMessage.id, '标题:', testMessage.title, '状态:', testMessage.status);

      // 2. 测试获取消息详情
      console.log('\n2. 测试获取消息详情');
      const detailResponse = await axios.get(`${API_BASE}/messages/${testMessage.id}`);
      console.log('✓ 获取消息详情成功，状态码:', detailResponse.status);

      // 3. 测试下架功能（如果消息是已发布状态）
      if (testMessage.status === 'published') {
        console.log('\n3. 测试下架功能');
        try {
          const unpublishResponse = await axios.patch(
            `${API_BASE}/messages/${testMessage.id}/unpublish`,
            {},
            {
              headers: {
                'Authorization': `Bearer ${TEST_TOKEN}`
              }
            }
          );
          console.log('✓ 下架成功，状态码:', unpublishResponse.status);
          console.log('响应数据:', unpublishResponse.data);
        } catch (error) {
          console.log('✗ 下架失败:', error.response?.status, error.response?.data?.message || error.message);
        }
      }

      // 4. 测试发布功能（如果消息是草稿状态）
      if (testMessage.status === 'draft') {
        console.log('\n4. 测试发布功能');
        try {
          const publishResponse = await axios.patch(
            `${API_BASE}/messages/${testMessage.id}/publish`,
            {},
            {
              headers: {
                'Authorization': `Bearer ${TEST_TOKEN}`
              }
            }
          );
          console.log('✓ 发布成功，状态码:', publishResponse.status);
          console.log('响应数据:', publishResponse.data);
        } catch (error) {
          console.log('✗ 发布失败:', error.response?.status, error.response?.data?.message || error.message);
        }
      }
    }

    // 5. 测试图片上传
    console.log('\n5. 测试图片上传API路径');
    try {
      const FormData = require('form-data');
      const fs = require('fs');
      const path = require('path');
      
      // 创建一个测试图片文件（1x1像素的PNG）
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x57, 0x63, 0xF8, 0x0F, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);
      
      const formData = new FormData();
      formData.append('file', testImageBuffer, {
        filename: 'test.png',
        contentType: 'image/png'
      });
      
      const uploadResponse = await axios.post(`${API_BASE}/upload/image`, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${TEST_TOKEN}`
        }
      });
      console.log('✓ 图片上传成功，状态码:', uploadResponse.status);
      console.log('上传结果:', uploadResponse.data);
    } catch (error) {
      console.log('✗ 图片上传失败:', error.response?.status, error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('测试失败:', error.response?.status, error.response?.data?.message || error.message);
  }
}

// 运行测试
testMessageAPIs();
