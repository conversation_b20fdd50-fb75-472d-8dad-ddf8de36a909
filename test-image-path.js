const path = require('path');
const fs = require('fs');

// 测试图片保存路径
console.log('当前工作目录:', process.cwd());

// 模拟后端代码中的路径计算
const uploadDir = path.join(__dirname, 'backend/uploads/images');
console.log('图片保存目录:', uploadDir);
console.log('目录是否存在:', fs.existsSync(uploadDir));

// 检查nginx配置对应的路径
const nginxPath = '/soft/hefu/20250606/HF-production/backend/uploads';
console.log('nginx配置路径:', nginxPath);

// 测试生成的URL
const filename = 'external-1749178857358-501912449.jpg';
const localUrl = `/uploads/images/${filename}`;
const fullUrl = `http://hefuf.com${localUrl}`;
console.log('生成的本地URL:', localUrl);
console.log('完整URL:', fullUrl);

// 检查实际文件路径
const actualFilePath = path.join(__dirname, 'backend/uploads/images', filename);
console.log('实际文件路径:', actualFilePath);
