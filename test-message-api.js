/**
 * 测试消息API的脚本
 */

const axios = require('axios');

const API_BASE = 'http://localhost:5001/api';

// 从浏览器控制台获取的token
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testMessageAPI() {
  try {
    console.log('=== 测试消息API ===');
    
    // 1. 测试获取所有状态的消息（管理模式）
    console.log('\n1. 测试获取所有状态的消息（status=""）:');
    try {
      const response = await axios.get(`${API_BASE}/messages`, {
        params: {
          page: 1,
          limit: 10,
          status: '' // 空字符串表示获取所有状态
        },
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`
        }
      });
      
      console.log('✓ 请求成功，状态码:', response.status);
      console.log('返回的消息数量:', response.data.data.messages.length);
      console.log('消息状态统计:');
      const statusCount = {};
      response.data.data.messages.forEach(msg => {
        statusCount[msg.status] = (statusCount[msg.status] || 0) + 1;
      });
      console.log(statusCount);
      
      console.log('\n消息列表:');
      response.data.data.messages.forEach(msg => {
        console.log(`- ID: ${msg.id}, 标题: ${msg.title}, 状态: ${msg.status}`);
      });
      
    } catch (error) {
      console.log('✗ 请求失败:', error.response?.status, error.response?.data?.message || error.message);
    }

    // 2. 测试获取草稿消息
    console.log('\n2. 测试获取草稿消息（status="draft"）:');
    try {
      const response = await axios.get(`${API_BASE}/messages`, {
        params: {
          page: 1,
          limit: 10,
          status: 'draft'
        },
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`
        }
      });
      
      console.log('✓ 请求成功，状态码:', response.status);
      console.log('返回的草稿消息数量:', response.data.data.messages.length);
      
      if (response.data.data.messages.length > 0) {
        console.log('草稿消息列表:');
        response.data.data.messages.forEach(msg => {
          console.log(`- ID: ${msg.id}, 标题: ${msg.title}, 状态: ${msg.status}`);
        });
      } else {
        console.log('没有找到草稿消息');
      }
      
    } catch (error) {
      console.log('✗ 请求失败:', error.response?.status, error.response?.data?.message || error.message);
    }

    // 3. 测试创建草稿消息
    console.log('\n3. 测试创建草稿消息:');
    try {
      const response = await axios.post(`${API_BASE}/messages`, {
        title: '测试草稿消息 ' + Date.now(),
        content: '这是一个测试草稿消息的内容',
        author: '测试作者',
        type: 'content',
        status: 'draft'
      }, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✓ 草稿创建成功，状态码:', response.status);
      console.log('创建的消息ID:', response.data.data.id);
      console.log('消息状态:', response.data.data.status);
      
    } catch (error) {
      console.log('✗ 创建失败:', error.response?.status, error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
testMessageAPI();
