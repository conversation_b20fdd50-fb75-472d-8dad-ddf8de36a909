<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>相关文章管理模式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>相关文章管理模式测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>本页面用于测试相关文章模块的管理模式功能：</p>
        <ul>
            <li>✅ 新增文章按钮只在管理模式下显示</li>
            <li>✅ 管理模式下显示所有状态的文章（包括草稿）</li>
            <li>✅ 非管理模式下只显示已发布的文章</li>
            <li>✅ 封面图片正常显示</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>打开首页：<a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
            <li>滚动到"相关文章"部分</li>
            <li>检查是否有"管理模式"按钮（需要登录且有权限）</li>
            <li>点击"管理模式"按钮</li>
            <li>检查是否出现"新增文章"按钮</li>
            <li>检查是否显示草稿状态的文章</li>
            <li>测试上传封面图片功能</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>快速测试</h2>
        <button onclick="testAPI()">测试API调用</button>
        <button onclick="testPermissions()">测试权限检查</button>
        <button onclick="openHomePage()">打开首页</button>
        <div id="test-results"></div>
    </div>

    <script>
        function openHomePage() {
            window.open('http://localhost:3000', '_blank');
        }

        async function testAPI() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="test-result">正在测试API...</div>';

            try {
                // 测试获取消息列表API
                const response = await fetch('http://localhost:5001/api/messages?status=&limit=10', {
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('hefamily_token')
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ API调用成功<br>
                            消息总数: ${data.data?.messages?.length || 0}<br>
                            草稿数量: ${data.data?.messages?.filter(m => m.status === 'draft').length || 0}<br>
                            已发布数量: ${data.data?.messages?.filter(m => m.status === 'published').length || 0}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="test-result error">❌ API调用失败: ${response.status}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function testPermissions() {
            const resultsDiv = document.getElementById('test-results');
            const token = localStorage.getItem('hefamily_token');
            
            if (!token) {
                resultsDiv.innerHTML = '<div class="test-result error">❌ 未登录，请先登录</div>';
                return;
            }

            try {
                // 测试权限API
                const response = await fetch('http://localhost:5001/api/auth/me', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const permissions = data.data?.permissions || [];
                    const hasPublish = permissions.includes('content:publish');
                    const hasManage = permissions.includes('content:manage');
                    
                    resultsDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ 权限检查成功<br>
                            用户角色: ${data.data?.role || '未知'}<br>
                            content:publish权限: ${hasPublish ? '✅' : '❌'}<br>
                            content:manage权限: ${hasManage ? '✅' : '❌'}<br>
                            可以管理: ${hasPublish || hasManage ? '✅' : '❌'}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="test-result error">❌ 权限检查失败: ${response.status}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ 网络错误: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
