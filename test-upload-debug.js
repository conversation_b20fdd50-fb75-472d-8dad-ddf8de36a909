const fs = require('fs');
const path = require('path');

console.log('=== 上传功能调试脚本 ===');
console.log('');

// 检查当前工作目录
console.log('1. 当前工作目录:', process.cwd());
console.log('');

// 检查各个目录是否存在
const directories = [
  '/soft/hefu/images',
  '/soft/hefu/20250606/HF-production/backend/public/uploads/images',
  '/soft/hefu/20250606/HF-production/backend/uploads/images',
  path.join(process.cwd(), 'backend/public/uploads/images'),
  path.join(process.cwd(), 'backend/uploads/images')
];

console.log('2. 检查目录存在性:');
directories.forEach(dir => {
  const exists = fs.existsSync(dir);
  console.log(`   ${exists ? '✓' : '✗'} ${dir}`);
  if (exists) {
    try {
      const stats = fs.statSync(dir);
      console.log(`     权限: ${stats.mode.toString(8)}`);
      const files = fs.readdirSync(dir);
      console.log(`     文件数量: ${files.length}`);
      if (files.length > 0) {
        console.log(`     最近文件: ${files.slice(-3).join(', ')}`);
      }
    } catch (e) {
      console.log(`     错误: ${e.message}`);
    }
  }
});
console.log('');

// 测试创建文件
console.log('3. 测试文件创建:');
const testFile = '/soft/hefu/images/test-upload-debug.txt';
try {
  // 确保目录存在
  if (!fs.existsSync('/soft/hefu/images')) {
    fs.mkdirSync('/soft/hefu/images', { recursive: true });
    console.log('   ✓ 创建目录: /soft/hefu/images');
  }
  
  // 创建测试文件
  fs.writeFileSync(testFile, `测试文件创建时间: ${new Date().toISOString()}`);
  console.log('   ✓ 成功创建测试文件:', testFile);
  
  // 读取测试文件
  const content = fs.readFileSync(testFile, 'utf8');
  console.log('   ✓ 成功读取测试文件:', content);
  
  // 删除测试文件
  fs.unlinkSync(testFile);
  console.log('   ✓ 成功删除测试文件');
} catch (e) {
  console.log('   ✗ 文件操作失败:', e.message);
}
console.log('');

// 检查环境变量
console.log('4. 环境变量:');
console.log('   NODE_ENV:', process.env.NODE_ENV || '未设置');
console.log('   UPLOAD_DIR:', process.env.UPLOAD_DIR || '未设置');
console.log('   用户:', process.env.USER || process.env.USERNAME || '未知');
console.log('');

// 检查进程信息
console.log('5. 进程信息:');
console.log('   进程ID:', process.pid);
console.log('   用户ID:', process.getuid ? process.getuid() : '不支持');
console.log('   组ID:', process.getgid ? process.getgid() : '不支持');
console.log('');

console.log('=== 调试完成 ===');
