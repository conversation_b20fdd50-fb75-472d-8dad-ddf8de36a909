获取到的消息: (5) [{…}, {…}, {…}, {…}, {…}]
C:\Users\<USER>\Desktop\P\frontend\components\message-list.tsx:93 消息总数: 5
C:\Users\<USER>\Desktop\P\frontend\components\message-list.tsx:94 草稿消息: []
C:\Users\<USER>\Desktop\P\frontend\components\message-list.tsx:95 完整的API响应: {
  "success": true,
  "data": {
    "messages": [
      {
        "id": 23,
        "title": "测试草稿",
        "content": "啊啊啊啊a'a'a'aa'a'aa'aa",
        "excerpt": "啊啊啊啊a'a'a'aa'a'aa'aa",
        "cover_image": "http://localhost:5001/uploads/images/file-1748510599647-894588485.png",
        "author": "测试草稿",
        "type": "content",
        "external_url": null,
        "status": "published",
        "view_count": 0,
        "created_by": 1,
        "created_at": "2025-05-29T09:23:25.000Z",
        "updated_at": "2025-05-29T09:23:25.000Z",
        "creator": {
          "id": 1,
          "username": "admin"
        }
      },
      {
        "id": 6,
        "title": "123123",
        "content": "请问请问请<i>问请问</i>q'we'q'we'q'we'q'wq'<b>we'q'we'q'we'qq'w</b>e'q'we'q'weq'we'q'we'q'wq'we'q'we'<u>qq'we'q'weq'we'q'wq'we'qq</u>'weq'wqqweq'weq'wq<img src=\"data:image/png;base64,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
C:\Users\<USER>\Desktop\P\frontend\components\message-card.tsx:50 渲染消息卡片: {id: 23, title: '测试草稿', status: 'published', cover_image: 'http://localhost:5001/uploads/images/file-1748510599647-894588485.png', isManagementMode: true}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 检查权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 检查本地存储中是否有权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户数据: {id: 1, username: 'admin', email: 'ad***@example.com', phone: '130****4444', role: 'admin', …}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户是管理员，拥有所有权限
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 权限检查结果: 有权限
C:\Users\<USER>\Desktop\P\frontend\components\message-card.tsx:50 渲染消息卡片: {id: 6, title: '123123', status: 'published', cover_image: 'blob:http://localhost:3000/fe540ba4-b769-4c37-89da-a6c8c69b96ed', isManagementMode: true}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 检查权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 检查本地存储中是否有权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户数据: {id: 1, username: 'admin', email: 'ad***@example.com', phone: '130****4444', role: 'admin', …}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户是管理员，拥有所有权限
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 权限检查结果: 有权限
C:\Users\<USER>\Desktop\P\frontend\components\message-card.tsx:50 渲染消息卡片: {id: 4, title: 'ceshi', status: 'published', cover_image: 'http://www.81.cn/template/tenant207/t306/img/jfjb.png', isManagementMode: true}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 检查权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 检查本地存储中是否有权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户数据: {id: 1, username: 'admin', email: 'ad***@example.com', phone: '130****4444', role: 'admin', …}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户是管理员，拥有所有权限
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 权限检查结果: 有权限
C:\Users\<USER>\Desktop\P\frontend\components\message-card.tsx:50 渲染消息卡片: {id: 3, title: '124124', status: 'published', cover_image: 'http://mmecoa.qpic.cn/sz_mmecoa_jpg/xZLeuOyZDicaPv…xbvg2bVUmyVTFFTnqhVQNvexVQQ7zPvNKTw/0?wx_fmt=jpeg', isManagementMode: true}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 检查权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 检查本地存储中是否有权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户数据: {id: 1, username: 'admin', email: 'ad***@example.com', phone: '130****4444', role: 'admin', …}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户是管理员，拥有所有权限
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 权限检查结果: 有权限
C:\Users\<USER>\Desktop\P\frontend\components\message-card.tsx:50 渲染消息卡片: {id: 2, title: '1', status: 'published', cover_image: 'blob:http://localhost:3000/180a29ad-e2e5-43b5-bfe5-3e2d51db0714', isManagementMode: true}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 [AuthContext] 检查权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 检查本地存储中是否有权限: content:publish
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户数据: {id: 1, username: 'admin', email: 'ad***@example.com', phone: '130****4444', role: 'admin', …}
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 用户是管理员，拥有所有权限
C:\Users\<USER>\Desktop\P\frontend\utils\logger.ts:23 